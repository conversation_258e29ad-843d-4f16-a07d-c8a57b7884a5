#ifndef UTILS_TIME_HPP
#define UTILS_TIME_HPP

#include <chrono>
#include <cstdint>
#include <string>

namespace fescue_iox
{

class TimeDiff
{
public:
    TimeDiff();
    void SetReff();
    unsigned int GetDiffNs() const;
    unsigned int GetDiffUs() const;
    unsigned int GetDiffMs() const;
    unsigned int GetDiffS() const;
    unsigned int GetDiffMin() const;
    unsigned int GetDiffHours() const;

private:
    std::chrono::steady_clock::time_point reff_;
};

struct DateTime
{
    unsigned int year;
    unsigned int month;
    unsigned int day;
    unsigned int hour;
    unsigned int minute;
    unsigned int second;
    unsigned int millisecond;
    unsigned int microsecond;
    unsigned int nanosecond;
};

DateTime GetTime();
uint64_t GetTimestampMs(); // ms
uint64_t GetSteadyClockTimestampMs();
uint64_t DateTimeToMilliseconds(const DateTime &date_time);
std::string ConvertDateTime(const std::string &data_time);
void ConvertDateTime(const std::string &data_time, int &year, int &month, int &day, int &hour, int &minute, int &second);

} // namespace fescue_iox

#endif
