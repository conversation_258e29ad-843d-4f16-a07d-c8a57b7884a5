#pragma once

#include <vector>
#include <memory>

#include "utils/imu_filter.hpp"
#include "utils/slip_detector.hpp"

namespace fescue_iox 
{

class PoseFusion 
{
public:
    PoseFusion();

    void Update(double current_time, double ax, double ay, double az,
                double gx, double gy, double gz, double left_motor_speed, 
                double right_motor_speed, bool is_motion);
    
    void Reset();

    double GetX() const { return x_; }
    double GetY() const { return y_; }
    double GetPitch() const { return imu_filter_pitch_; }
    double GetRoll() const { return imu_filter_roll_; }
    double GetVX() const { return vx_; }
    double GetVY() const { return vy_; }
    double GetYaw() const { return imu_filter_yaw_; }
    double GetLinearVelocity() const { return linear_velocity_; }
    double GetAngularVelocity() const { return angular_velocity_; }
    double GetTurningSlipRatio() const { return turning_slip_ratio_; }
    double GetMovingSlipRatio() const { return moving_slip_ratio_; }
    bool GetIsWheelSlip() const { return is_wheel_slip_; }

private:
    std::pair<double, double> GetRobotSpeed(double motor_speed_left, double motor_speed_right) const;
    double UpdateSlipRatio(double current_time, double ax, double ay, double az,
                           double gx, double gy, double gz, double left_motor_speed, 
                           double right_motor_speed, bool is_motion);
    double GetFusionRatio(double slip_ratio) const;
    void UpdateVelocity(double slip_ratio);

private:
    std::shared_ptr<IMUFilter> imu_filter_ = nullptr;
    std::shared_ptr<SlipDetector> slip_detector_ = nullptr;
    double imu_filter_yaw_ = 0;
    double imu_filter_pitch_ = 0;
    double imu_filter_roll_ = 0;
    double x_ = 0;
    double y_ = 0;
    double vx_ = 0;
    double vy_ = 0;
    double last_time_ = -1;
    double first_time_ = -1;
    double turning_slip_ratio_ = 0;
    double moving_slip_ratio_ = 0;
    bool is_wheel_slip_ = false;
    double linear_velocity_ = 0;
    double angular_velocity_ = 0;
};

}