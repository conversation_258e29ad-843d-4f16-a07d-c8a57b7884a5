#pragma once

#include <string>
#include <vector>

namespace fescue_iox
{

std::string Fstring(const char *format, ...);

std::vector<std::string> SpliteString(const std::string &data, const std::string &pattern);

std::string GetFileNameWithCurrentTime(const std::string &prefix, const std::string &extension);

std::string GetGitCommitIDPrefix8(const std::string &commit_id);

void ParseVersionString(const std::string &version_str, int &major, int &minor, int &patch);

} // namespace fescue_iox