#pragma once

#include <memory>
#include <mutex>

// 智能指针 + 双重检查锁定
template <typename T>
class Singleton
{
public:
    template <typename... Args>
    static T *GetInstance(Args &&...args);
    static void DestroyInstance();

private:
    static std::mutex mtx_;
    static std::unique_ptr<T> instance_{nullptr};
    Singleton() = default;
    ~Singleton() = default;
    Singleton(const Singleton &) = delete;
    Singleton &operator=(const Singleton &) = delete;
}

// 静态成员定义
template <typename T>
std::unique_ptr<T> Singleton<T>::instance_ = nullptr;

template <typename T>
std::mutex Singleton<T>::mtx_;

// 支持可变参数构造的 GetInstance
template <typename T>
template <typename... Args>
T *Singleton<T>::GetInstance(Args &&...args)
{
    if (!instance_)
    {
        std::lock_guard<std::mutex> guard(mtx_);
        if (!instance_)
        {
            instance_ = std::make_unique<T>(std::forward<Args>(args)...);
        }
    }

    return instance_.get();
}

template <typename T>
void Singleton<T>::DestroyInstance()
{
    std::lock_guard<std::mutex> lock(mtx_);
    instance_.reset();
}

/*

class MyClass
{
public:
    MyClass(const std::string& name) {
        std::cout << "MyClass constructed with " << name << std::endl;
    }
    void Hello() {
        std::cout << "Hello from Singleton!" << std::endl;
    }
};

// 使用：
auto* instance = Singleton<MyClass>::GetInstance("TestName");
instance->Hello();
Singleton<MyClass>::DestroyInstance();


*/