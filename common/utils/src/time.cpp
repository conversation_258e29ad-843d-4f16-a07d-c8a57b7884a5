#include "utils/time.hpp"

#include "time.hpp"

#include <cstring>
#include <ctime>
#include <iostream>
#include <regex>

namespace fescue_iox
{

TimeDiff::TimeDiff()
{
    reff_ = std::chrono::steady_clock::now();
}

void TimeDiff::SetReff()
{
    reff_ = std::chrono::steady_clock::now();
}

unsigned int TimeDiff::GetDiffNs() const
{
    std::chrono::steady_clock::time_point currentTime = std::chrono::steady_clock::now();
    return std::chrono::duration_cast<std::chrono::nanoseconds>(currentTime - reff_).count();
}

unsigned int TimeDiff::GetDiffUs() const
{
    std::chrono::steady_clock::time_point currentTime = std::chrono::steady_clock::now();
    return std::chrono::duration_cast<std::chrono::microseconds>(currentTime - reff_).count();
}

unsigned int TimeDiff::GetDiffMs() const
{
    std::chrono::steady_clock::time_point currentTime = std::chrono::steady_clock::now();
    return std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - reff_).count();
}

unsigned int TimeDiff::GetDiffS() const
{
    std::chrono::steady_clock::time_point currentTime = std::chrono::steady_clock::now();
    return std::chrono::duration_cast<std::chrono::seconds>(currentTime - reff_).count();
}

unsigned int TimeDiff::GetDiffMin() const
{
    std::chrono::steady_clock::time_point currentTime = std::chrono::steady_clock::now();
    return std::chrono::duration_cast<std::chrono::minutes>(currentTime - reff_).count();
}

unsigned int TimeDiff::GetDiffHours() const
{
    std::chrono::steady_clock::time_point currentTime = std::chrono::steady_clock::now();
    return std::chrono::duration_cast<std::chrono::hours>(currentTime - reff_).count();
}

DateTime GetTime()
{
    DateTime stru_timeData = {};
    std::chrono::high_resolution_clock::time_point utcNow = std::chrono::high_resolution_clock::now();
    std::time_t utcNow_t = std::chrono::high_resolution_clock::to_time_t(utcNow);
    struct tm *localNow = localtime(&utcNow_t);
    std::chrono::milliseconds ms = std::chrono::duration_cast<std::chrono::milliseconds>(utcNow.time_since_epoch());
    std::chrono::microseconds us = std::chrono::duration_cast<std::chrono::microseconds>(utcNow.time_since_epoch());
    std::chrono::nanoseconds ns = std::chrono::duration_cast<std::chrono::nanoseconds>(utcNow.time_since_epoch());
    stru_timeData.year = localNow->tm_year + 1900;
    stru_timeData.month = localNow->tm_mon + 1;
    stru_timeData.day = localNow->tm_mday;
    stru_timeData.hour = localNow->tm_hour;
    stru_timeData.minute = localNow->tm_min;
    stru_timeData.second = localNow->tm_sec;
    stru_timeData.millisecond = (int)(ms.count() % 1000);
    stru_timeData.microsecond = (int)(us.count() % 1000);
    stru_timeData.nanosecond = (int)(ns.count() % 1000);
    return stru_timeData;
}

uint64_t GetTimestampMs() // ms
{
    std::chrono::high_resolution_clock::time_point utcNow = std::chrono::high_resolution_clock::now();
    std::chrono::milliseconds ms = std::chrono::duration_cast<std::chrono::milliseconds>(utcNow.time_since_epoch());
    return ms.count();
}

uint64_t GetSteadyClockTimestampMs() {
    auto now = std::chrono::steady_clock::now();
    return std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
}

uint64_t DateTimeToMilliseconds(const DateTime &date_time)
{
    struct tm stTm;
    memset(&stTm, 0, sizeof(struct tm));
    stTm.tm_year = date_time.year - 1900;
    stTm.tm_mon = date_time.month - 1;
    stTm.tm_mday = date_time.day;
    stTm.tm_hour = date_time.hour;
    stTm.tm_min = date_time.minute;
    stTm.tm_sec = date_time.second;
    time_t timestamp = mktime(&stTm);
    return timestamp * 1000 + date_time.millisecond;
}

/**
 * @brief Convert format: YYYY-MM-DD HH:MM:SS to YYYYMMDDHHMMSS
 *
 * @param input
 * @return std::string
 */
std::string ConvertDateTime(const std::string &input)
{
    static const std::regex pattern(R"(^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$)");
    if (!std::regex_match(input, pattern))
    {
        std::cout << "Invalid datetime format. Expected format: YYYY-MM-DD HH:MM:SS." << std::endl;
        return "unknown";
    }

    try
    {
        std::string output;
        output.reserve(14);          // 提前分配内存提高性能
        output.append(input, 0, 4);  // 年
        output.append(input, 5, 2);  // 月
        output.append(input, 8, 2);  // 日
        output.append(input, 11, 2); // 时
        output.append(input, 14, 2); // 分
        output.append(input, 17, 2); // 秒
        return output;
    }
    catch (...)
    {
        std::cout << "Unexpected error occurred during string processing." << std::endl;
    }

    return "unknown";
}

/**
 * @brief Convert format: YYYY-MM-DD HH:MM:SS to YYYYMMDDHHMMSS
 *
 * @param input
 * @return std::string
 */
void ConvertDateTime(const std::string &data_time, int &year, int &month, int &day, int &hour, int &minute, int &second)
{
    static const std::regex pattern(R"(^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$)");
    if (!std::regex_match(data_time, pattern))
    {
        std::cout << "Invalid datetime format. Expected format: YYYY-MM-DD HH:MM:SS." << std::endl;
        return;
    }

    std::sscanf(data_time.c_str(), "%04d-%02d-%02d %02d:%02d:%02d",
                &year, &month, &day, &hour, &minute, &second);
}

} // namespace fescue_iox
