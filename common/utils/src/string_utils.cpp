#include "utils/string_utils.hpp"

#include "string_utils.hpp"

#include <chrono>
#include <cstdio>
#include <cstring>
#include <iomanip>
#include <iostream>
#include <regex>
#include <sstream>
#include <stdarg.h>
#include <string>

namespace fescue_iox
{

constexpr size_t size = 512;

std::string Fstring(const char *format, ...)
{
    char *buff = new char[size];
    memset(buff, 0, size);
    va_list argList;
    va_start(argList, format);
    vsnprintf(buff, size, format, argList);
    va_end(argList);
    std::string ret(buff);
    delete[] buff;
    return ret;
}

std::vector<std::string> SpliteString(const std::string &data, const std::string &pattern)
{
    std::vector<std::string> result;
    std::string split_data = data + pattern;
    size_t pos = split_data.find(pattern);
    size_t size = split_data.size();
    while (pos != std::string::npos)
    {
        std::string tmp = split_data.substr(0, pos);
        result.emplace_back(tmp);
        split_data = split_data.substr(pos + 1, size);
        pos = split_data.find(pattern);
    }
    return result;
}

std::string GetFileNameWithCurrentTime(const std::string &prefix, const std::string &extension)
{
    // 获取当前时间
    auto now = std::chrono::system_clock::now();
    auto in_time_t = std::chrono::system_clock::to_time_t(now);

    // 格式化时间
    std::tm buf;
#ifdef _WIN32
    localtime_s(&buf, &in_time_t); // Windows
#else
    localtime_r(&in_time_t, &buf); // Linux/Unix
#endif

    std::ostringstream oss;
    oss << std::put_time(&buf, "%Y%m%d_%H%M%S"); // 格式化为 yyyyMMdd_hhmmss

    // 生成文件名
    return prefix + "_" + oss.str() + "." + extension;
}

std::string GetGitCommitIDPrefix8(const std::string &commit_id)
{
    if (commit_id.length() < 8)
    {
        std::cout << "Git commit ID is shorter than expected." << std::endl;
        return "unknown";
    }

    return commit_id.substr(0, 8);
}

void ParseVersionString(const std::string &version_str, int &major, int &minor, int &patch)
{
    std::sscanf(version_str.c_str(), "V%d.%d.%d", &major, &minor, &patch);
}

} // namespace fescue_iox
