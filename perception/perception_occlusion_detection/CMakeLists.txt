cmake_minimum_required(VERSION 3.16)
project(perception_occlusion_detection C CXX)

# set(CMAKE_CXX_STANDARD 17)
# set(CMAKE_C_STANDARD 11)
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fPIC -O3")
set(CMAKE_C_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG}  -fPIC -g -O3")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC -O3")
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -fPIC -g -O3")
# set(CMAKE_BUILD_TYPE "Debug")
set(CMAKE_BUILD_TYPE "Release")
#set(CMAKE_VERBOSE_MAKEFILE ON)

set(executable ${PROJECT_NAME}_node)

# thirdparty dir
set(THRIDPARTY_DIR ${CMAKE_SOURCE_DIR}/thirdparty)

#[1]指定头文件查找路径
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include/
    ${THRIDPARTY_DIR}/ob_mower_occlusion/include/
)

#[2]指定链接库查找路径
link_directories(
    ${THRIDPARTY_DIR}/ob_mower_occlusion/lib/${HOST_PLATFORM}/${SOC_NAME}/
)

#[3]可执行文件
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/src/ DIR_SRCS)
add_executable(${executable} ${DIR_SRCS})

#[4]链接库
target_link_libraries(${executable}
    attr
    acl
    iceoryx_platform
    iceoryx_posh
    iceoryx_hoofs
    pthread
    ob_utils
    ob_mower_occlusion
    opencv_aruco
    opencv_core
    opencv_imgcodecs
    opencv_imgproc
    boost_filesystem
    backtrace
    spdlog
    rt
    dl
    ${THRIDPARTY_DIR}/yaml-cpp/lib/${HOST_PLATFORM}/${SOC_NAME}/libyaml-cpp.a
)

#[5]安装
install(
    TARGETS ${executable}
    DESTINATION ${CMAKE_SOURCE_DIR}/install/${SOFTWARE_INSTALL_DIR}/bin/
)

#算法库的安装
install(DIRECTORY
    ${THRIDPARTY_DIR}/ob_mower_occlusion/lib/${HOST_PLATFORM}/${SOC_NAME}/
    DESTINATION ${CMAKE_SOURCE_DIR}/install/${SOFTWARE_INSTALL_DIR}/lib/
    FILES_MATCHING PATTERN "*.so"
    PATTERN "*.so.*"
)

#节点配置文件安装
install(DIRECTORY
    ${CMAKE_CURRENT_SOURCE_DIR}/conf/.
    DESTINATION ${CMAKE_SOURCE_DIR}/install/${SOFTWARE_INSTALL_DIR}/conf/${executable}/
)

#算法模型安装
# install(DIRECTORY
#     ${THRIDPARTY_DIR}/ob_mower_occlusion/model/${HOST_PLATFORM}/${SOC_NAME}/.
#     DESTINATION ${CMAKE_SOURCE_DIR}/install/${SOFTWARE_INSTALL_DIR}/model/
# )
