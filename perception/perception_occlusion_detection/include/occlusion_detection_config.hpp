#pragma once

#include "utils/config.hpp"

#include <string>

namespace fescue_iox
{

/**
 * @brief 镜头脏污检测算法参数
 */
struct PerceptionOcclusionDetectionAlgConfig
{
    int ignore_num = 15;              // Number of images ignored at the beginning.
    float line1 = 0.30f;              // 中区比例
    float line2 = 0.60f;              // 下区比例
    int num_frames_mid = 30;          // 中区最多连续检测的次数
    int min_detected_frames_mid = 15; // 中区判断为遮挡所需累计的次数
    int num_frames_bot = 10;          // 下区最多连续检测的次数
    int min_detected_frames_bot = 1;  // 下区判断为遮挡所需累计的次数
    int block_size = 64;              // 块大小
    double laplacian_thresh = 12.0;   // 拉普拉斯阈值
    double sobel_thresh = 8.0;        // sobel阈值
    int min_area = 200;               // debug显示时绘制的最小面积，仅对显示结果有影响
    int min_blocks_mid = 11;          // 中区判断为遮挡的最少块数
    int min_blocks_bot = 6;           // 下区判断为遮挡的最少块数
    int skip_frames = 15;             // 跳帧数
    bool test_mode = false;           // 测试模式开关，设置为true时，会可视化每一帧的检测结果。
    int log_level = 2;                // 0：打印所有调试信息，1：打印INFO以上调试信息， 2：打印WARN以上调试信息，3：打印ERROR调试信息。
    bool save_log = false;
    std::string save_log_path = "/userdata/log";
    int debug_mode = 0; // 0: 关闭debug模式, 1: 绘制检测结果，2: 绘制检测结果，并保存结果图，
                        // 3: 绘制检测结果，并保存原始图，但不保存结果图， 4：绘制检测结果，并保存原始图和结果图。
    std::string save_debug_img_path{"/userdata/image/perception_occlusion_detection_node/occlusion_detection"};

    PerceptionOcclusionDetectionAlgConfig() = default;
    ~PerceptionOcclusionDetectionAlgConfig() = default;
    PerceptionOcclusionDetectionAlgConfig(const PerceptionOcclusionDetectionAlgConfig &config) = default;
    PerceptionOcclusionDetectionAlgConfig &operator=(const PerceptionOcclusionDetectionAlgConfig &config);
    std::string toString() const;
};

bool operator==(const PerceptionOcclusionDetectionAlgConfig &lhs, const PerceptionOcclusionDetectionAlgConfig &rhs);
bool operator!=(const PerceptionOcclusionDetectionAlgConfig &lhs, const PerceptionOcclusionDetectionAlgConfig &rhs);

} // namespace fescue_iox
