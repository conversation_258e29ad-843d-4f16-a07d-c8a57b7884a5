#pragma once

#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "mower_msgs/msg/soc_exception.hpp"
#include "ob_mower_msgs/perception_occlusion_detection_result__struct.h"
#include "ob_mower_occlusion.hpp"
#include "ob_mower_occlusion_eorrcodes.h"
#include "occlusion_detection_config.hpp"
#include "opencv2/opencv.hpp"
#include "sensor_msgs/image__struct.h"
#include "utils/iceoryx_publisher_mower.hpp"

#include <condition_variable>
#include <mutex>
#include <string>
#include <vector>

namespace fescue_iox
{

struct OcclusionDebugImage
{
    uint64_t sec;
    uint64_t nanosec;
    uint32_t width;
    uint32_t height;
    std::string frame_id;
    std::string encoding;
    cv::Mat img;
    OcclusionDebugImage() = default;
    ~OcclusionDebugImage() = default;
    OcclusionDebugImage(const OcclusionDebugImage &other)
    {
        this->sec = other.sec;
        this->nanosec = other.nanosec;
        this->width = other.width;
        this->height = other.height;
        this->frame_id = other.frame_id;
        this->encoding = other.encoding;
        this->img = other.img.clone();
    }
    OcclusionDebugImage &operator=(const OcclusionDebugImage &other)
    {
        if (this != &other)
        {
            this->sec = other.sec;
            this->nanosec = other.nanosec;
            this->width = other.width;
            this->height = other.height;
            this->frame_id = other.frame_id;
            this->encoding = other.encoding;
            this->img = other.img.clone();
        }
        return *this;
    }
};

class PerceptionOcclusionDetectionAlg
{
    using iox_image_publisher = iox::popo::Publisher<sensor_msgs__msg__Image_iox>;
    using iox_occlusion_detect_result_publisher = iox::popo::Publisher<fescue_msgs__msg__PerceptionOcclusionDetectionResult>;

public:
    PerceptionOcclusionDetectionAlg(const std::string &conf_file);
    ~PerceptionOcclusionDetectionAlg();
    bool DoOcclusionDetection(const sensor_msgs__msg__Image_iox &image);
    bool SetOcclusionAlgParam(OcclusionClsInputParams &params);
    OcclusionClsInputParams GetOcclusionAlgParam();
    const char *GetOcclusionAlgVersion();

private:
    bool InitAlg();
    void DeinitAlg();
    void InitAlgParam();
    void InitPublisher();
    void PreOcclusionDetection(const sensor_msgs__msg__Image_iox &image);
    void PublishResult();
    void PublishImage();
    void PublishResultThread();
    void PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value);

private:
    inline static std::mutex mutex_;
    inline static std::condition_variable cv_;
    inline static OcclusionResultBuffer occlusion_detection_result_;
    inline static OcclusionDebugImage debug_img_;
    static void OcclusionDetectionResultCallback(OcclusionResultBuffer *result);

private:
    std::unique_ptr<iox_image_publisher> pub_debug_img_;                    // 只做debug
    std::unique_ptr<iox_occlusion_detect_result_publisher> pub_alg_result_; // 检测结果发布
    std::unique_ptr<IceoryxPublisherMower<mower_msgs::msg::SocException>> pub_exception_{nullptr};

    std::atomic_bool thread_running_{true};
    std::thread pub_result_thread_;

    // 配置参数
    std::string conf_file_{""};
    uint64_t sec_;
    uint64_t nanosec_;
    uint64_t timestamp_ms_;
    std::string frame_id_{""};
    std::string encoding_{""};

    OCCLUSION_CLASSIFIER_HANDLE alg_handle_{nullptr};
    ImageBuffer input_image_;
};

} // namespace fescue_iox
