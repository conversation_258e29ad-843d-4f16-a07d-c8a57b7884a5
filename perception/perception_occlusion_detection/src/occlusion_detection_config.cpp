#include "occlusion_detection_config.hpp"

#include <iostream>
#include <sstream>

namespace fescue_iox
{

PerceptionOcclusionDetectionAlgConfig &PerceptionOcclusionDetectionAlgConfig::operator=(const PerceptionOcclusionDetectionAlgConfig &config)
{
    if (this != &config)
    {
        ignore_num = config.ignore_num;
        line1 = config.line1;
        line2 = config.line2;
        num_frames_mid = config.num_frames_mid;
        min_detected_frames_mid = config.min_detected_frames_mid;
        num_frames_bot = config.num_frames_bot;
        min_detected_frames_bot = config.min_detected_frames_bot;
        block_size = config.block_size;
        laplacian_thresh = config.laplacian_thresh;
        sobel_thresh = config.sobel_thresh;
        min_area = config.min_area;
        min_blocks_mid = config.min_blocks_mid;
        min_blocks_bot = config.min_blocks_bot;
        skip_frames = config.skip_frames;
        test_mode = config.test_mode;
        log_level = config.log_level;
        save_log = config.save_log;
        save_log_path = config.save_log_path;
        debug_mode = config.debug_mode;
        save_debug_img_path = config.save_debug_img_path;
    }

    return *this;
}

std::string PerceptionOcclusionDetectionAlgConfig::toString() const
{
    std::stringstream ss;
    ss << "------------------------- " << typeid(*this).name() << " -------------------------\r\n";
    ss << "  ignore_num: " << ignore_num << "\n";
    ss << "  line1: " << line1 << "\n";
    ss << "  line2: " << line2 << "\n";
    ss << "  num_frames_mid: " << num_frames_mid << "\n";
    ss << "  min_detected_frames_mid: " << min_detected_frames_mid << "\n";
    ss << "  num_frames_bot: " << num_frames_bot << "\n";
    ss << "  min_detected_frames_bot: " << min_detected_frames_bot << "\n";
    ss << "  block_size: " << block_size << "\n";
    ss << "  laplacian_thresh: " << laplacian_thresh << "\n";
    ss << "  sobel_thresh: " << sobel_thresh << "\n";
    ss << "  min_area: " << min_area << "\n";
    ss << "  min_blocks_mid: " << min_blocks_mid << "\n";
    ss << "  min_blocks_bot: " << min_blocks_bot << "\n";
    ss << "  skip_frames: " << skip_frames << "\n";
    ss << "  test_mode: " << test_mode << "\n";
    ss << "  log_level: " << log_level << "\n";
    ss << "  save_log: " << save_log << "\n";
    ss << "  save_log_path: " << save_log_path << "\n";
    ss << "  debug_mode: " << debug_mode << "\n";
    ss << "  save_debug_img_path: " << save_debug_img_path << "\n";
    ss << "------------------------- " << typeid(*this).name() << " -------------------------";
    return ss.str();
}

bool operator==(const PerceptionOcclusionDetectionAlgConfig &lhs, const PerceptionOcclusionDetectionAlgConfig &rhs)
{
    return lhs.ignore_num == rhs.ignore_num &&
           lhs.line1 == rhs.line1 &&
           lhs.line2 == rhs.line2 &&
           lhs.num_frames_mid == rhs.num_frames_mid &&
           lhs.min_detected_frames_mid == rhs.min_detected_frames_mid &&
           lhs.num_frames_bot == rhs.num_frames_bot &&
           lhs.min_detected_frames_bot == rhs.min_detected_frames_bot &&
           lhs.block_size == rhs.block_size &&
           lhs.laplacian_thresh == rhs.laplacian_thresh &&
           lhs.sobel_thresh == rhs.sobel_thresh &&
           lhs.min_area == rhs.min_area &&
           lhs.min_blocks_mid == rhs.min_blocks_mid &&
           lhs.min_blocks_bot == rhs.min_blocks_bot &&
           lhs.skip_frames == rhs.skip_frames &&
           lhs.test_mode == rhs.test_mode &&
           lhs.log_level == rhs.log_level &&
           lhs.save_log == rhs.save_log &&
           lhs.save_log_path == rhs.save_log_path &&
           lhs.debug_mode == rhs.debug_mode &&
           lhs.save_debug_img_path == rhs.save_debug_img_path;
}

bool operator!=(const PerceptionOcclusionDetectionAlgConfig &lhs, const PerceptionOcclusionDetectionAlgConfig &rhs)
{
    return !(lhs == rhs);
}

template <>
bool Config<PerceptionOcclusionDetectionAlgConfig>::LoadConfig(PerceptionOcclusionDetectionAlgConfig &config, const std::string &conf_file)
{
    YAML::Node node;
    try
    {
        node = YAML::LoadFile(conf_file);
    }
    catch (const YAML::BadFile &e)
    {
        std::cerr << "PerceptionOcclusionDetectionAlgConfig load config fail (BadFile): " << e.what() << '\n';
        return false;
    }
    catch (const YAML::ParserException &e)
    {
        std::cerr << "PerceptionOcclusionDetectionAlgConfig load config fail (ParserException): " << e.what() << '\n';
        return false;
    }
    catch (const std::exception &e)
    {
        std::cerr << "PerceptionOcclusionDetectionAlgConfig load config fail (Unknown): " << e.what() << '\n';
        return false;
    }

    config.ignore_num = GetYamlValue<int>(node, "ignore_num", config.ignore_num);
    config.line1 = GetYamlValue<float>(node, "line1", config.line1);
    config.line2 = GetYamlValue<float>(node, "line2", config.line2);
    config.num_frames_mid = GetYamlValue<int>(node, "num_frames_mid", config.num_frames_mid);
    config.min_detected_frames_mid = GetYamlValue<int>(node, "min_detected_frames_mid", config.min_detected_frames_mid);
    config.num_frames_bot = GetYamlValue<int>(node, "num_frames_bot", config.num_frames_bot);
    config.min_detected_frames_bot = GetYamlValue<int>(node, "min_detected_frames_bot", config.min_detected_frames_bot);
    config.block_size = GetYamlValue<int>(node, "block_size", config.block_size);
    config.laplacian_thresh = GetYamlValue<double>(node, "laplacian_thresh", config.laplacian_thresh);
    config.sobel_thresh = GetYamlValue<double>(node, "sobel_thresh", config.sobel_thresh);
    config.min_area = GetYamlValue<int>(node, "min_area", config.min_area);
    config.min_blocks_mid = GetYamlValue<int>(node, "min_blocks_mid", config.min_blocks_mid);
    config.min_blocks_bot = GetYamlValue<int>(node, "min_blocks_bot", config.min_blocks_bot);
    config.skip_frames = GetYamlValue<int>(node, "skip_frames", config.skip_frames);
    config.test_mode = GetYamlValue<bool>(node, "test_mode", config.test_mode);
    config.log_level = GetYamlValue<int>(node, "log_level", config.log_level);
    config.save_log = GetYamlValue<bool>(node, "save_log", config.save_log);
    config.save_log_path = GetYamlValue<std::string>(node, "save_log_path", config.save_log_path);
    config.debug_mode = GetYamlValue<int>(node, "debug_mode", config.debug_mode);
    config.save_debug_img_path = GetYamlValue<std::string>(node, "save_debug_img_path", config.save_debug_img_path);

    return true;
}

template <>
bool Config<PerceptionOcclusionDetectionAlgConfig>::CreateConfig(const PerceptionOcclusionDetectionAlgConfig &conf, const std::string &conf_file)
{
    YAML::Node node;
    node["ignore_num"] = conf.ignore_num;
    node["line1"] = conf.line1;
    node["line2"] = conf.line2;
    node["num_frames_mid"] = conf.num_frames_mid;
    node["min_detected_frames_mid"] = conf.min_detected_frames_mid;
    node["num_frames_bot"] = conf.num_frames_bot;
    node["min_detected_frames_bot"] = conf.min_detected_frames_bot;
    node["block_size"] = conf.block_size;
    node["laplacian_thresh"] = conf.laplacian_thresh;
    node["sobel_thresh"] = conf.sobel_thresh;
    node["min_area"] = conf.min_area;
    node["min_blocks_mid"] = conf.min_blocks_mid;
    node["min_blocks_bot"] = conf.min_blocks_bot;
    node["skip_frames"] = conf.skip_frames;
    node["test_mode"] = conf.test_mode;
    node["log_level"] = conf.log_level;
    node["save_log"] = conf.save_log;
    node["save_log_path"] = conf.save_log_path;
    node["debug_mode"] = conf.debug_mode;
    node["save_debug_img_path"] = conf.save_debug_img_path;

    return WriteYamlFile(conf_file, node);
}

} // namespace fescue_iox
