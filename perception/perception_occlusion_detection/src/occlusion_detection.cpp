#include "occlusion_detection.hpp"

#include "occlusion_detection_config.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"

using namespace mower_msgs::msg;

namespace fescue_iox
{

void PerceptionOcclusionDetectionAlg::OcclusionDetectionResultCallback(OcclusionResultBuffer *result)
{
    LOG_DEBUG("PerceptionOcclusionDetectionAlg result: {} {} img size {}",
              result->timestamp, result->occlusion_result, result->debug_img.size);
    std::lock_guard<std::mutex> lock(mutex_);
    occlusion_detection_result_.timestamp = result->timestamp;
    occlusion_detection_result_.occlusion_result = result->occlusion_result;
    if (result->debug_img.size > 0 && result->debug_img.data)
    {
        int type = CV_8UC3;
        if (result->debug_img.channels == 1)
        {
            type = CV_8UC1;
            debug_img_.encoding = "momo8";
        }
        else
        {
            debug_img_.encoding = "bgr8";
        }
        debug_img_.frame_id = "occlusion";
        debug_img_.width = result->debug_img.width;
        debug_img_.height = result->debug_img.height;
        cv::Mat img(debug_img_.height, debug_img_.width, type, result->debug_img.data);
        debug_img_.img = img.clone();
    }
    else
    {
        debug_img_.img.release();
    }
    cv_.notify_one();
}

PerceptionOcclusionDetectionAlg::PerceptionOcclusionDetectionAlg(const std::string &conf_file)
    : conf_file_(conf_file)
{
    InitPublisher();
    InitAlg();
}

PerceptionOcclusionDetectionAlg::~PerceptionOcclusionDetectionAlg()
{
    LOG_WARN("PerceptionOcclusionDetectionAlg start stop!");
    DeinitAlg();
    OcclusionClassifierRelease(&alg_handle_);
    LOG_WARN("PerceptionOcclusionDetectionAlg stop success!");
}

bool PerceptionOcclusionDetectionAlg::InitAlg()
{
    InitAlgParam();

    OcclusionClsInputParams param;
    PerceptionOcclusionDetectionAlgConfig config = Config<PerceptionOcclusionDetectionAlgConfig>::GetConfig();

    param.ignore_num = config.ignore_num;
    param.line1 = config.line1;
    param.line2 = config.line2;
    param.num_frames_mid = config.num_frames_mid;
    param.min_detected_frames_mid = config.min_detected_frames_mid;
    param.num_frames_bot = config.num_frames_bot;
    param.min_detected_frames_bot = config.min_detected_frames_bot;
    param.block_size = config.block_size;
    param.laplacian_thresh = config.laplacian_thresh;
    param.sobel_thresh = config.sobel_thresh;
    param.min_area = config.min_area;
    param.min_blocks_mid = config.min_blocks_mid;
    param.min_blocks_bot = config.min_blocks_bot;
    param.skip_frames = config.skip_frames;

    param.test_mode = config.test_mode;
    param.log_level = config.log_level;
    param.save_log = config.save_log;
    param.save_log_path = config.save_log_path;
    param.debug_mode = config.debug_mode;
    param.save_debug_img_path = config.save_debug_img_path;

    int result = OcclusionClassifierCreateFromStruct(&alg_handle_, param);
    if (result != CLASSIFY_SUCCESS)
    {
        LOG_ERROR("Perception occlusion detection algorithm initialization failed, error code: {:X}", result);
        PublishException(SocExceptionLevel::ERROR,
                         SocExceptionValue::ALG_PERCEPTION_OCCLUSION_INIT_EXCEPTION);
        return false;
    }

    OcclusionClassifierRegisterCallback(OcclusionDetectionResultCallback);
    LOG_INFO("Perception occlusion detection alg version: {}", GetOcclusionClassifierVersion());

    pub_result_thread_ = std::thread(&PerceptionOcclusionDetectionAlg::PublishResultThread, this);

    return true;
}

void PerceptionOcclusionDetectionAlg::DeinitAlg()
{
    thread_running_.store(false);
    if (pub_result_thread_.joinable())
    {
        pub_result_thread_.join();
    }
    cv_.notify_all();
}

void PerceptionOcclusionDetectionAlg::InitAlgParam()
{
    std::string conf_path = GetDirectoryPath(conf_file_);
    if (!conf_path.empty())
    {
        LOG_INFO("Perception occlusion detection alg config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("Perception occlusion detection alg config path failed!");
        }
    }
    if (!Config<PerceptionOcclusionDetectionAlgConfig>::Init(conf_file_))
    {
        LOG_WARN("Init Perception occlusion detection alg config parameters failed!");
    }
    PerceptionOcclusionDetectionAlgConfig config = Config<PerceptionOcclusionDetectionAlgConfig>::GetConfig();
    LOG_INFO("{}", config.toString().c_str());
    if (!Config<PerceptionOcclusionDetectionAlgConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set Perception occlusion detection alg config parameters failed!");
    }
    CreateDirectories(config.save_log_path);
    CreateDirectories(config.save_debug_img_path);
}

bool PerceptionOcclusionDetectionAlg::DoOcclusionDetection(const sensor_msgs__msg__Image_iox &image)
{
    PreOcclusionDetection(image);

    int result = OcclusionClassifierExecute(&alg_handle_, &input_image_);
    if (result != CLASSIFY_SUCCESS)
    {
        LOG_ERROR("Perception occlusion detection OcclusionClassifierExecute fail, error code: {}", result);
        PublishException(SocExceptionLevel::ERROR,
                         SocExceptionValue::ALG_PERCEPTION_OCCLUSION_EXECUTE_ERROR_EXCEPTION);
        return false;
    }

    return true;
}

bool PerceptionOcclusionDetectionAlg::SetOcclusionAlgParam(OcclusionClsInputParams &params)
{
    int result = OcclusionClassifierSetParams(&alg_handle_, params);
    if (result != CLASSIFY_SUCCESS)
    {
        LOG_ERROR("OcclusionClassifierSetParams fail, error code: {}", result);
        PublishException(SocExceptionLevel::WARNING,
                         SocExceptionValue::ALG_PERCEPTION_OCCLUSION_PARAM_ERROR_EXCEPTION);
        return false;
    }

    LOG_INFO("Set occlusion detection alg params success!");
    PerceptionOcclusionDetectionAlgConfig config = Config<PerceptionOcclusionDetectionAlgConfig>::GetConfig();
    config.ignore_num = params.ignore_num;
    config.line1 = params.line1;
    config.line2 = params.line2;
    config.num_frames_mid = params.num_frames_mid;
    config.min_detected_frames_mid = params.min_detected_frames_mid;
    config.num_frames_bot = params.num_frames_bot;
    config.min_detected_frames_bot = params.min_detected_frames_bot;
    config.block_size = params.block_size;
    config.laplacian_thresh = params.laplacian_thresh;
    config.sobel_thresh = params.sobel_thresh;
    config.min_area = params.min_area;
    config.min_blocks_mid = params.min_blocks_mid;
    config.min_blocks_bot = params.min_blocks_bot;
    config.skip_frames = params.skip_frames;
    config.test_mode = params.test_mode;
    config.log_level = params.log_level;
    config.save_log = params.save_log;
    config.save_log_path = params.save_log_path;
    config.debug_mode = params.debug_mode;
    config.save_debug_img_path = params.save_debug_img_path;
    Config<PerceptionOcclusionDetectionAlgConfig>::SetConfig(config);
    LOG_INFO("New occlusion detection alg params: {}", config.toString().c_str());

    return true;
}

OcclusionClsInputParams PerceptionOcclusionDetectionAlg::GetOcclusionAlgParam()
{
    return OcclusionClassifierGetParams(&alg_handle_);
}

const char *PerceptionOcclusionDetectionAlg::GetOcclusionAlgVersion()
{
    return GetOcclusionClassifierVersion();
}

void PerceptionOcclusionDetectionAlg::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_debug_img_ = std::make_unique<iox_image_publisher>(
        iox::capro::ServiceDescription{kPerceptionOcclusionDetectImageIox[0],
                                       kPerceptionOcclusionDetectImageIox[1],
                                       kPerceptionOcclusionDetectImageIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
    pub_alg_result_ = std::make_unique<iox_occlusion_detect_result_publisher>(
        iox::capro::ServiceDescription{kPerceptionOcclusionDetectResultIox[0],
                                       kPerceptionOcclusionDetectResultIox[1],
                                       kPerceptionOcclusionDetectResultIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);

    pub_exception_ = std::make_unique<IceoryxPublisherMower<mower_msgs::msg::SocException>>("soc_exception");
}

void PerceptionOcclusionDetectionAlg::PreOcclusionDetection(const sensor_msgs__msg__Image_iox &image)
{
    sec_ = image.header.stamp.sec;
    nanosec_ = image.header.stamp.nanosec;
    frame_id_ = std::string(image.header.frame_id.c_str());
    encoding_ = std::string(image.encoding.c_str());
    timestamp_ms_ = sec_ * 1000 + nanosec_ / 1000000;

    input_image_.timestamp = timestamp_ms_;
    input_image_.data = (uint8_t *)image.data.data();
    input_image_.width = image.width;
    input_image_.height = image.height;
    input_image_.channels = 1;
    input_image_.size = input_image_.width * input_image_.height * 3 / 2; // YUV frame size
    input_image_.colorOrder = ColorOrder::OB_FMT_YUV420SP;
}

void PerceptionOcclusionDetectionAlg::PublishResult()
{
#if 0
    // detect camera is obstructed, publish error exception
    if (occlusion_detection_result_.occlusion_result == 1)
    {
        SocExceptionValue value = SocExceptionValue::ALG_PERCEPTION_OCCLUSION_CAMERA_IS_OBSTRUCTED;
        LOG_WARN("Perception occlusion detect camera is obstructed!, error code: {:X}", value);
        PublishException(SocExceptionLevel::ERROR, value);
    }
#endif

    if (pub_alg_result_->hasSubscribers())
    {
        auto loan = pub_alg_result_->loan();
        if (!loan.has_error())
        {
            auto &msg = loan.value();
            msg->timestamp_ms = occlusion_detection_result_.timestamp;
            msg->occlusion_result = occlusion_detection_result_.occlusion_result;
            msg.publish();
        }
    }
}

void PerceptionOcclusionDetectionAlg::PublishImage()
{
    if (!debug_img_.img.empty() && pub_debug_img_->hasSubscribers())
    {
        auto loan = pub_debug_img_->loan();
        if (!loan.has_error())
        {
            auto &msg = loan.value();
            msg->header.stamp.sec = sec_;
            msg->header.stamp.nanosec = nanosec_;
            msg->header.frame_id.unsafe_assign(debug_img_.frame_id.c_str());
            msg->width = debug_img_.width;
            msg->height = debug_img_.height;
            msg->step = msg->width * debug_img_.img.channels();
            msg->encoding.unsafe_assign(debug_img_.encoding.c_str());
            msg->is_bigendian = false;
            size_t img_size = (msg->step * msg->height > IOX_IMAGE_DATA_MAX) ? IOX_IMAGE_DATA_MAX
                                                                             : (msg->step * msg->height);
            msg->data.resize(img_size);
            memcpy(msg->data.data(), debug_img_.img.data, img_size);
            msg.publish();
        }
    }
}

void PerceptionOcclusionDetectionAlg::PublishResultThread()
{
    while (thread_running_.load())
    {
        std::unique_lock<std::mutex> lock(mutex_);
        cv_.wait(lock);
        if (!thread_running_)
        {
            break;
        }
        PublishResult();
        PublishImage();
    }
}

void PerceptionOcclusionDetectionAlg::PublishException(mower_msgs::msg::SocExceptionLevel level,
                                                       mower_msgs::msg::SocExceptionValue value)
{
    if (pub_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.timestamp = GetTimestampMs();
        exception.node_name = "perception_occlusion_detection_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_exception_->publishCopyOf(exception);
    }
}

} // namespace fescue_iox
