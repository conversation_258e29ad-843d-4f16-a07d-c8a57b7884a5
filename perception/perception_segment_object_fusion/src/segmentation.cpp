#include "segmentation.hpp"

#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"

using namespace mower_msgs::msg;

namespace fescue_iox
{

PerceptionSegmenter::PerceptionSegmenter(bool save_img_enable,
                                         const std::string &save_img_dir,
                                         const std::string &conf_file,
                                         const CameraBevParam &bev_param,
                                         bool bev_param_result)
    : save_img_enable_(save_img_enable)
    , save_img_dir_(save_img_dir)
    , conf_file_(conf_file)
{
    InitPublisher();
    InitAlg(bev_param, bev_param_result);
}

PerceptionSegmenter::~PerceptionSegmenter()
{
    LOG_WARN("PerceptionSegmenter start stop!");
    SegmenterRelease(seg_handle_);
    delete[] result_.data;
    delete[] debug_result_.data;
    delete[] seg_result_.InversePerspectMask.data;
    delete[] seg_result_.InversePerspectRgb.data;
    LOG_WARN("PerceptionSegmenter stop success!");
}

void PerceptionSegmenter::GetSegmenterInitResult(SegRgbInitResult &seg_init_result)
{
    seg_init_result.corners_on_src_img = seg_init_result_.corners_on_src_img;
    seg_init_result.corners_on_bev_img = seg_init_result_.corners_on_bev_img;
    seg_init_result.warpMatrixFlat = seg_init_result_.warpMatrixFlat;
    seg_init_result.segconfig = seg_init_result_.segconfig;
}

void PerceptionSegmenter::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;
    pub_color_ = std::make_unique<iox_image_publisher>(
        iox::capro::ServiceDescription{kPerceptionSegmenterColorImageIox[0],
                                       kPerceptionSegmenterColorImageIox[1],
                                       kPerceptionSegmenterColorImageIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
    pub_color_mask_ = std::make_unique<iox_image_publisher>(
        iox::capro::ServiceDescription{kPerceptionSegmenterColorMaskIox[0],
                                       kPerceptionSegmenterColorMaskIox[1],
                                       kPerceptionSegmenterColorMaskIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
    pub_inverse_perspect_mask_ = std::make_unique<iox_image_publisher>(
        iox::capro::ServiceDescription{kPerceptionSegmenterInversePerspectMaskIox[0],
                                       kPerceptionSegmenterInversePerspectMaskIox[1],
                                       kPerceptionSegmenterInversePerspectMaskIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
    pub_inverse_perspect_rgb_ = std::make_unique<iox_image_publisher>(
        iox::capro::ServiceDescription{kPerceptionSegmenterInversePerspectRgbIox[0],
                                       kPerceptionSegmenterInversePerspectRgbIox[1],
                                       kPerceptionSegmenterInversePerspectRgbIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
    pub_segmenter_result_ = std::make_unique<iox_segmenter_result_publisher>(
        iox::capro::ServiceDescription{kPerceptionSegmenterResultIox[0],
                                       kPerceptionSegmenterResultIox[1],
                                       kPerceptionSegmenterResultIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
    pub_seg_img_result_ = std::make_unique<iox_image_publisher>(
        iox::capro::ServiceDescription{"orbbec",
                                       "fescue_iox",
                                       "segmenter_img_result",
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
    pub_exception_ = std::make_unique<IceoryxPublisherMower<mower_msgs::msg::SocException>>("soc_exception");
}

void PerceptionSegmenter::InitAlgParam()
{
    std::string conf_path = GetDirectoryPath(conf_file_);
    if (!conf_path.empty())
    {
        LOG_INFO("PerceptionSegmenter create alg config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("PerceptionSegmenter create alg config path failed!!!");
        }
    }
    if (!Config<SegmentAlgConfig>::Init(conf_file_))
    {
        LOG_WARN("Init PerceptionSegmenter config parameters failed!");
    }
    SegmentAlgConfig config = Config<SegmentAlgConfig>::GetConfig();
    LOG_INFO("{}", config.toString().c_str());
    if (!Config<SegmentAlgConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set PerceptionSegmenter config parameters failed!");
    }
}

bool PerceptionSegmenter::InitAlg(const CameraBevParam &bev_param, bool bev_param_result)
{
#if 1
    if (!bev_param_result)
    {
        SocExceptionValue err_code = SocExceptionValue::ALG_SEGMENTATION_GET_BEV_PARAM_ERROR_EXCEPTION;
        LOG_ERROR("Perception segmentation algorithm bev param fail, error code: {:X}", static_cast<uint16_t>(err_code));
        PublishException(SocExceptionLevel::ERROR, err_code);
        return false;
    }
#endif

    InitAlgParam();

    result_.data = new uint8_t[MAX_IMG_BUFF_SIZE];
    debug_result_.data = new uint8_t[MAX_IMG_BUFF_SIZE];
    seg_result_.InversePerspectMask.data = new uint8_t[MAX_IMG_BUFF_SIZE];
    seg_result_.InversePerspectRgb.data = new uint8_t[MAX_IMG_BUFF_SIZE];

    SegmentAlgConfig config = Config<SegmentAlgConfig>::GetConfig();
    SegInitParams param;

    ConfigParam2AlgParam(config, param);

    if (bev_param_result)
    {
        param.blind_zone_dist = bev_param.blind_zone_dist;
        param.lLine_ptStart.x = bev_param.lLine_ptStart.x;
        param.lLine_ptStart.y = bev_param.lLine_ptStart.y;
        param.lLine_ptEnd.x = bev_param.lLine_ptEnd.x;
        param.lLine_ptEnd.y = bev_param.lLine_ptEnd.y;
        param.rLine_ptStart.x = bev_param.rLine_ptStart.x;
        param.rLine_ptStart.y = bev_param.rLine_ptStart.y;
        param.rLine_ptEnd.x = bev_param.rLine_ptEnd.x;
        param.rLine_ptEnd.y = bev_param.rLine_ptEnd.y;
        LOG_WARN("Perception segmentation using camera bev param, blind_zone_dist: {} left line: ({} {}) ({} {}) right line: ({} {}) ({} {})",
                 param.blind_zone_dist,
                 param.lLine_ptStart.x, param.lLine_ptStart.y, param.lLine_ptEnd.x, param.lLine_ptEnd.y,
                 param.rLine_ptStart.x, param.rLine_ptStart.y, param.rLine_ptEnd.x, param.rLine_ptEnd.y);
    }
    else
    {
        LOG_WARN("Perception segmentation using config file param, blind_zone_dist: {} left line: ({} {}) ({} {}) right line: ({} {}) ({} {})",
                 param.blind_zone_dist,
                 param.lLine_ptStart.x, param.lLine_ptStart.y, param.lLine_ptEnd.x, param.lLine_ptEnd.y,
                 param.rLine_ptStart.x, param.rLine_ptStart.y, param.rLine_ptEnd.x, param.rLine_ptEnd.y);
    }

    LOG_INFO("*************** pixelsToMeters {} *****************", param.pixelsToMeters);

    int result = SegmenterCreatFromStruct(&seg_handle_, param, seg_init_result_);
    if (result != SEGMENT_SUCCESS)
    {
        LOG_ERROR("Perception segment create from struct fail, error code: {:X}", result);
        PublishException(SocExceptionLevel::ERROR,
                         SocExceptionValue::ALG_SEGMENTATION_INIT_EXCEPTION);
        return false;
    }
    LOG_INFO("ob_mower_seg_version: {}", GetSegmenterVersion());

    return true;
}

bool PerceptionSegmenter::SetSegmenterParam(SegInitParams &param, SegRgbInitResult &seg_init_result)
{
    int result = SegmenterSetParams(&seg_handle_, param, seg_init_result);
    if (result != SEGMENT_SUCCESS)
    {
        LOG_ERROR("Set perception segment alg params fail, error code: {:X}", result);
        PublishException(SocExceptionLevel::WARNING,
                         SocExceptionValue::ALG_SEGMENTATION_PARAM_ERROR_EXCEPTION);
        return false;
    }

    LOG_INFO("Set segmenter alg params success!");
    SegmentAlgConfig config = Config<SegmentAlgConfig>::GetConfig();
    AlgParam2ConfigParam(param, config);
    Config<SegmentAlgConfig>::SetConfig(config);
    LOG_INFO("New segmenter alg params: {}", config.toString().c_str());
    return true;
}

SegInitParams PerceptionSegmenter::GetSegmenterParam()
{
    return SegmenterGetParams(&seg_handle_);
}

const char *PerceptionSegmenter::GetSegmenterAlgVersion()
{
    return GetSegmenterVersion();
}

bool PerceptionSegmenter::DoSegmenter(const sensor_msgs__msg__Image_iox &image)
{
    PrePerceptionSegmenter(image);

    TimeDiff diff;
    int result = SegmenterExecute_mask2idCode_boundaryMow(seg_handle_, input_image_, result_,
                                                          seg_result_, debug_result_,
                                                          std::to_string(timestamp_ms_), true);
    if (result != SEGMENT_SUCCESS)
    {
        LOG_ERROR("Perception segment SegmenterExecute_mask2idCode_boundaryMow fail, error code: {:X}", result);
        PublishException(SocExceptionLevel::ERROR,
                         SocExceptionValue::ALG_SEGMENTATION_EXECUTE_ERROR_EXCEPTION);
        return false;
    }
    if (diff.GetDiffMs() > 150)
    {
        LOG_WARN("Segmenter cost: {}ms, input: {} system: {} diff: {}ms", diff.GetDiffMs(),
                 input_image_.timestamp, GetTimestampMs(), GetTimestampMs() - input_image_.timestamp);
    }

    PostPerceptionSegmenter(image);

    return true;
}

void PerceptionSegmenter::PrePerceptionSegmenter(const sensor_msgs__msg__Image_iox &image)
{
    sec_ = image.header.stamp.sec;
    nanosec_ = image.header.stamp.nanosec;
    frame_id_ = std::string(image.header.frame_id.c_str());
    encoding_ = std::string(image.encoding.c_str());
    timestamp_ms_ = sec_ * 1000 + nanosec_ / 1000000;

    input_image_.timestamp = timestamp_ms_;
    input_image_.width = image.width;
    input_image_.height = image.height;
    input_image_.data = (uint8_t *)image.data.data();
    input_image_.channels = 1;
    input_image_.size = input_image_.width * input_image_.height * 3 / 2; // YUV FRAME size
    input_image_.colorOrder = ColorOrder::OB_FMT_YUV420SP;

    seg_result_.reset();
}

void PerceptionSegmenter::PostPerceptionSegmenter(const sensor_msgs__msg__Image_iox &image)
{
    // 发布算法结果
    PublishResult();
    // 发布bev视角mask，debug用
    PublishImage(pub_inverse_perspect_mask_, "mono8", seg_result_.InversePerspectMask);
    // 发布bev视角rgb，debug用
    PublishImage(pub_inverse_perspect_rgb_, "bgr8", seg_result_.InversePerspectRgb);
    // 发布彩色mask，debug用
    PublishImage(pub_color_mask_, "bgr8", debug_result_);
    PublishImage(pub_seg_img_result_, "mono8", result_);
    // 发布彩色原始图像，debug用
    // PublishOriginColorImage(pub_color_, image, "bgr8");
    // 保存图片
    if (save_img_enable_)
    {
        SaveImage(image);
    }
}

void PerceptionSegmenter::PublishResult()
{
    if (pub_segmenter_result_->hasSubscribers())
    {
        auto loan = pub_segmenter_result_->loan();
        if (!loan.has_error())
        {
            auto &msg = loan.value();
            msg->header.stamp.sec = sec_;
            msg->header.stamp.nanosec = nanosec_;
            msg->header.frame_id.unsafe_assign(frame_id_.c_str());
            msg->detect_status = static_cast<fescue_msgs_enum__DetectStatus>(seg_result_.BoundaryStatus);
            msg->left_detected = seg_result_.BoundaryLeft;
            msg->ahead_detected = seg_result_.BoundaryAhead;
            msg->right_detected = seg_result_.BoundaryRight;
            msg->left_min_distance = seg_result_.left_min_distance;
            msg->ahead_min_distance = seg_result_.ahead_min_distance;
            msg->right_min_distance = seg_result_.right_min_distance;
            // msg->error_code = static_cast<fescue_msgs_enum__SegmenterErrorCode>(seg_result_.errorCode);

            // 割草机位置
            msg->mower_point.x = seg_result_.mowerPosition.mowerpoint.x;
            msg->mower_point.y = seg_result_.mowerPosition.mowerpoint.y;

            // 传入算法的该帧图像的时间戳
            msg->input_timestamp = seg_result_.inputtimestamp;
            msg->output_timestamp = seg_result_.outputtimestamp;

            // 左、右边界向量
            msg->left_boundary_vector.start.x = seg_result_.leftBoundaryVector.start.x;
            msg->left_boundary_vector.start.y = seg_result_.leftBoundaryVector.start.y;
            msg->left_boundary_vector.end.x = seg_result_.leftBoundaryVector.end.x;
            msg->left_boundary_vector.end.y = seg_result_.leftBoundaryVector.end.y;
            msg->right_boundary_vector.start.x = seg_result_.rightBoundaryVector.start.x;
            msg->right_boundary_vector.start.y = seg_result_.rightBoundaryVector.start.y;
            msg->right_boundary_vector.end.x = seg_result_.rightBoundaryVector.end.x;
            msg->right_boundary_vector.end.y = seg_result_.rightBoundaryVector.end.y;

            // 左右盲区向量
            msg->left_dead_line_vector.start.x = seg_result_.deadLineLeft.start.x;
            msg->left_dead_line_vector.start.y = seg_result_.deadLineLeft.start.y;
            msg->left_dead_line_vector.end.x = seg_result_.deadLineLeft.end.x;
            msg->left_dead_line_vector.end.y = seg_result_.deadLineLeft.end.y;
            msg->right_dead_line_vector.start.x = seg_result_.deadLineRight.start.x;
            msg->right_dead_line_vector.start.y = seg_result_.deadLineRight.start.y;
            msg->right_dead_line_vector.end.x = seg_result_.deadLineRight.end.x;
            msg->right_dead_line_vector.end.y = seg_result_.deadLineRight.end.y;

            msg->pixels_to_meters = seg_init_result_.segconfig.pixelsToMeters;
            // BEV 投影 MASK
            msg->inverse_perspect_mask.encoding = "mono8";
            msg->inverse_perspect_mask.width = seg_result_.InversePerspectMask.width;
            msg->inverse_perspect_mask.height = seg_result_.InversePerspectMask.height;
            msg->inverse_perspect_mask.is_bigendian = false;
            msg->inverse_perspect_mask.step = seg_result_.InversePerspectMask.width * seg_result_.InversePerspectMask.channels;
            size_t img_size = (seg_result_.InversePerspectMask.size > IOX_IMAGE_DATA_MAX)
                                  ? IOX_IMAGE_DATA_MAX
                                  : seg_result_.InversePerspectMask.size;
            memcpy(msg->inverse_perspect_mask.data.data(), seg_result_.InversePerspectMask.data, img_size);

            // 草区域栅格图BEV视角
            msg->grass_region_result.width = seg_result_.bev_grass_region.width;
            msg->grass_region_result.height = seg_result_.bev_grass_region.height;
            msg->grass_region_result.resolution = seg_result_.bev_grass_region.resolution;
            size_t cells_size = msg->grass_region_result.width * msg->grass_region_result.height;
            cells_size = (cells_size > IOX_MAX_CELL_NUM) ? IOX_MAX_CELL_NUM : cells_size;
            for (size_t i = 0; i < cells_size; i++)
            {
                uint8_t type = static_cast<uint8_t>(seg_result_.bev_grass_region.cells_array[i]);
                msg->grass_region_result.cells_array.push_back(type);
            }

            // LOG_DEBUG("msg->grass_region_result.cells_array size: {} cells_size: {}",
            //           msg->grass_region_result.cells_array.size(), cells_size);

            // 非草地
            size_t obstacle_num = seg_result_.seg_obstacles.size();
            obstacle_num = (obstacle_num > IOX_MAX_OBSTACLE_NUM) ? IOX_MAX_OBSTACLE_NUM : obstacle_num;
            for (size_t i = 0; i < obstacle_num; i++)
            {
                fescue_msgs__msg__SegmentObstacle obstacle;
                obstacle.class_id = seg_result_.seg_obstacles[i].classID;
                obstacle.contour_type = static_cast<fescue_msgs_enum__Segmenter_ContourType>(seg_result_.seg_obstacles[i].contourType);
                size_t contour_points = seg_result_.seg_obstacles[i].obstacle_contour.size();
                if (contour_points > IOX_MAX_OBSTACLE_CONTOUR_POINT_NUM)
                {
                    contour_points = IOX_MAX_OBSTACLE_CONTOUR_POINT_NUM;
                }
                for (size_t j = 0; j < contour_points; j++)
                {
                    geometry_msgs__msg__Point_iox point;
                    point.x = seg_result_.seg_obstacles[i].obstacle_contour[j].x;
                    point.y = seg_result_.seg_obstacles[i].obstacle_contour[j].y;
                    obstacle.obstacle_contour.push_back(point);
                }
                msg->seg_obstacles.push_back(obstacle);
            }
            msg.publish();
        }
    }
}

void PerceptionSegmenter::PublishImage(const std::unique_ptr<iox_image_publisher> &pub_ptr,
                                       const std::string &encoding,
                                       const SegImageBuffer &image)
{
    if (image.size > 0 && pub_ptr->hasSubscribers())
    {
        auto loan = pub_ptr->loan();
        if (!loan.has_error())
        {
            auto &msg = loan.value();
            msg->header.stamp.sec = sec_;
            msg->header.stamp.nanosec = nanosec_;
            msg->header.frame_id.unsafe_assign(frame_id_.c_str());
            msg->width = image.width;
            msg->height = image.height;
            msg->step = msg->width * image.channels;
            msg->encoding.unsafe_assign(encoding.c_str());
            msg->is_bigendian = false;
            size_t img_size = (msg->step * msg->height > IOX_IMAGE_DATA_MAX) ? IOX_IMAGE_DATA_MAX : (msg->step * msg->height);
            msg->data.resize(img_size);
            memcpy(msg->data.data(), image.data, img_size);
            msg.publish();
        }
    }
}

void PerceptionSegmenter::PublishOriginColorImage(const std::unique_ptr<iox_image_publisher> &pub_ptr,
                                                  const sensor_msgs__msg__Image_iox &image,
                                                  const std::string &encoding)
{
    if (pub_ptr->hasSubscribers())
    {
        auto loan = pub_ptr->loan();
        if (!loan.has_error())
        {
            NV12ToMat(bgr_img_, image.data.data(), image.width, image.height);
            // 绘制中心线
            int center_x = bgr_img_.cols / 2;
            int center_y = bgr_img_.rows / 2;
            cv::Scalar color = cv::Scalar(0, 0, 255);
            int thickness = 1;
            cv::line(bgr_img_, cv::Point(0, center_y), cv::Point(bgr_img_.cols, center_y), color, thickness);
            cv::line(bgr_img_, cv::Point(center_x, 0), cv::Point(center_x, bgr_img_.rows), color, thickness);

            auto &msg = loan.value();
            msg->header.stamp.sec = sec_;
            msg->header.stamp.nanosec = nanosec_;
            msg->header.frame_id.unsafe_assign(frame_id_.c_str());
            msg->width = bgr_img_.cols;
            msg->height = bgr_img_.rows;
            msg->step = msg->width * bgr_img_.channels();
            msg->encoding.unsafe_assign(encoding.c_str());
            msg->is_bigendian = false;
            size_t img_size = (msg->step * msg->height > IOX_IMAGE_DATA_MAX) ? IOX_IMAGE_DATA_MAX : (msg->step * msg->height);
            msg->data.resize(img_size);
            memcpy(msg->data.data(), bgr_img_.data, img_size);
            msg.publish();
        }
    }
}

void PerceptionSegmenter::SaveImage(const sensor_msgs__msg__Image_iox &image)
{
    char file_name[128] = {0};
    NV12ToMat(bgr_img_, image.data.data(), image.width, image.height);
    snprintf(file_name, sizeof(file_name) - 1, "%s/%lu_segment_%dx%d_rgb888.png",
             save_img_dir_.c_str(), timestamp_ms_, bgr_img_.cols, bgr_img_.rows);
    cv::imwrite(file_name, bgr_img_);
}

void PerceptionSegmenter::ConfigParam2AlgParam(const SegmentAlgConfig &config, SegInitParams &param)
{
    param.target_width = config.target_width;
    param.target_height = config.target_height;
    param.prob_threshold = config.prob_threshold;
    param.model_path = config.model_path;
    param.min_perimeter_threshold = config.min_perimeter_threshold;
    param.edge_alarm_threshold = config.edge_alarm_threshold;
    param.point_inside.x = config.point_inside.x;
    param.point_inside.y = config.point_inside.y;
    param.lLine_ptStart.x = config.left_line.start.x;
    param.lLine_ptStart.y = config.left_line.start.y;
    param.lLine_ptEnd.x = config.left_line.end.x;
    param.lLine_ptEnd.y = config.left_line.end.y;
    param.rLine_ptStart.x = config.right_line.start.x;
    param.rLine_ptStart.y = config.right_line.start.y;
    param.rLine_ptEnd.x = config.right_line.end.x;
    param.rLine_ptEnd.y = config.right_line.end.y;
    param.gridMask_mode = config.grid_mask_mode;
    param.cell_width = config.cell_width;
    param.cell_height = config.cell_height;
    param.mower_width = config.mower_width;
    param.pixelsToMeters = config.pixels_to_meters;
    param.bev_width = config.bev_width;
    param.bev_height = config.bev_height;
    param.blind_zone_dist = config.blind_zone_dist;
    param.debugMode = config.debug_mode;
    param.saveResult = config.save_result;
    param.saveResultPath = config.save_result_path;
}

void PerceptionSegmenter::AlgParam2ConfigParam(const SegInitParams &param, SegmentAlgConfig &config)
{
    config.target_width = param.target_width;
    config.target_height = param.target_height;
    config.prob_threshold = param.prob_threshold;
    config.model_path = param.model_path;
    config.min_perimeter_threshold = param.min_perimeter_threshold;
    config.edge_alarm_threshold = param.edge_alarm_threshold;
    config.point_inside.x = param.point_inside.x;
    config.point_inside.y = param.point_inside.y;
    config.left_line.start.x = param.lLine_ptStart.x;
    config.left_line.start.y = param.lLine_ptStart.y;
    config.left_line.end.x = param.lLine_ptEnd.x;
    config.left_line.end.y = param.lLine_ptEnd.y;
    config.right_line.start.x = param.rLine_ptStart.x;
    config.right_line.start.y = param.rLine_ptStart.y;
    config.right_line.end.x = param.rLine_ptEnd.x;
    config.right_line.end.y = param.rLine_ptEnd.y;
    config.grid_mask_mode = param.gridMask_mode;
    config.cell_width = param.cell_width;
    config.cell_height = param.cell_height;
    config.mower_width = param.mower_width;
    config.pixels_to_meters = param.pixelsToMeters;
    config.bev_width = param.bev_width;
    config.bev_height = param.bev_height;
    config.blind_zone_dist = param.blind_zone_dist;
    config.debug_mode = param.debugMode;
    config.save_result = param.saveResult;
    config.save_result_path = param.saveResultPath;
}

void PerceptionSegmenter::PublishException(mower_msgs::msg::SocExceptionLevel level,
                                           mower_msgs::msg::SocExceptionValue value)
{
    if (pub_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.timestamp = GetTimestampMs();
        exception.node_name = "perception_segment_object_fusion_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_exception_->publishCopyOf(exception);
    }
}

} // namespace fescue_iox
