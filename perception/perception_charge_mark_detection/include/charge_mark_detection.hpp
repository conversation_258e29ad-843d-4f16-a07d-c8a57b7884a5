#pragma once

#include "charge_mark_detection_config.hpp"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "mower_msgs/msg/soc_exception.hpp"
#include "ob_mower_charge_mark.hpp"
#include "ob_mower_msgs/charge_result_struct.h"
#include "ob_mower_msgs/mark_detect_result__struct.h"
#include "opencv2/opencv.hpp"
#include "sensor_msgs/image__struct.h"
#include "utils/iceoryx_publisher_mower.hpp"

#include <string>
#include <vector>

namespace fescue_iox
{

class ChargeMarkDetectionAlg
{
    using iox_image_publisher = iox::popo::Publisher<sensor_msgs__msg__Image_iox>;
    using iox_charge_result_publisher = iox::popo::Publisher<fescue_msgs__msg__ChargeResult>;
    using iox_mark_result_publisher = iox::popo::Publisher<fescue_msgs__msg__MarkDetectResult>;

public:
    ChargeMarkDetectionAlg(const std::string &conf_file);
    ~ChargeMarkDetectionAlg();
    bool DoChargeMarkDetect(const sensor_msgs__msg__Image_iox &image);
    void PostChargeMarkDetect();
    bool SetAlgParam(ChargeMarkInputParams &params);
    ChargeMarkInputParams GetAlgParam();
    const char *GetAlgVersion();

private:
    bool InitAlg();
    void InitAlgParam();
    void InitPublisher();
    void PreChargeMarkDetect(const sensor_msgs__msg__Image_iox &image);
    void PublishChargeResult();
    void PublishMarkResult();
    void PublishDebugImage(const ImageBuffer &image, const std::string &encoding);
    void PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value);

private:
    std::unique_ptr<iox_charge_result_publisher> pub_charge_result_;
    std::unique_ptr<iox_mark_result_publisher> pub_mark_result_{nullptr};
    std::unique_ptr<iox_image_publisher> pub_debug_img_{nullptr};
    std::unique_ptr<IceoryxPublisherMower<mower_msgs::msg::SocException>> pub_exception_{nullptr};

    std::string conf_file_{"conf/perception_charge_mark_detection_node/charge_mark_detection.yaml"};
    uint64_t sec_;
    uint64_t nanosec_;
    uint64_t timestamp_ms_;
    std::string frame_id_{""};
    std::string encoding_{""};

    static constexpr int32_t MAX_IMG_BUFF_SIZE = 1280 * 800 * 3;
    CHARGE_MARK_DETECTOR_HANDLE alg_handle_{nullptr}; // 充电桩检测句柄
    ImageBuffer input_image_;                         // 算法输入数据
    ChargeMarkInfo output_result_;                    // 算法输出结果
};

} // namespace fescue_iox
