#include "test_server_node_config.hpp"

#include <iostream>
#include <sstream>
#include <typeinfo>

namespace fescue_iox
{

TestServerNodeConfig &TestServerNodeConfig::operator=(const TestServerNodeConfig &conf)
{
    if (this != &conf)
    {
        this->common_conf = conf.common_conf;
        this->server_port = conf.server_port;
        this->jpeg_quality = conf.jpeg_quality;
        this->max_element = conf.max_element;
    }
    return *this;
}

std::string TestServerNodeConfig::toString() const
{
    std::stringstream ss;
    ss << "------------------------- " << typeid(*this).name() << " -------------------------"
       << "\n";
    ss << "  log_dir: " << common_conf.log_dir << "\n";
    ss << "  console_log_level: " << common_conf.console_log_level << "\n";
    ss << "  file_log_level: " << common_conf.file_log_level << "\n";
    ss << "  server_port: " << server_port << "\n";
    ss << "  jpeg_quality: " << jpeg_quality << "\n";
    ss << "  max_element: " << max_element << "\n";
    ss << "------------------------- " << typeid(*this).name() << " -------------------------";
    return ss.str();
}

bool operator==(const TestServerNodeConfig &lhs, const TestServerNodeConfig &rhs)
{
    return lhs.common_conf == rhs.common_conf &&
           lhs.server_port == rhs.server_port &&
           lhs.max_element == rhs.max_element &&
           lhs.jpeg_quality == rhs.jpeg_quality;
}

bool operator!=(const TestServerNodeConfig &lhs, const TestServerNodeConfig &rhs)
{
    return !(lhs == rhs);
}

template <>
bool Config<TestServerNodeConfig>::LoadConfig(TestServerNodeConfig &conf, const std::string &conf_file)
{
    YAML::Node node;
    try
    {
        node = YAML::LoadFile(conf_file);
    }
    catch (const YAML::BadFile &e)
    {
        std::cerr << "TestServerNodeConfig load config fail (BadFile): " << e.what() << '\n';
        return false;
    }
    catch (const YAML::ParserException &e)
    {
        std::cerr << "TestServerNodeConfig load config fail (ParserException): " << e.what() << '\n';
        return false;
    }
    catch (const std::exception &e)
    {
        std::cerr << "TestServerNodeConfig load config fail (Unknown): " << e.what() << '\n';
        return false;
    }

    conf.common_conf.log_dir = GetYamlValue<std::string>(node, "log_dir", conf.common_conf.log_dir);
    conf.common_conf.console_log_level = GetYamlValue<std::string>(node, "console_log_level", conf.common_conf.console_log_level);
    conf.common_conf.file_log_level = GetYamlValue<std::string>(node, "file_log_level", conf.common_conf.file_log_level);
    conf.server_port = GetYamlValue<int>(node, "server_port", conf.server_port);
    conf.jpeg_quality = GetYamlValue<int>(node, "jpeg_quality", conf.jpeg_quality);
    conf.max_element = GetYamlValue<int>(node, "max_element", conf.max_element);

    return true;
}

template <>
bool Config<TestServerNodeConfig>::CreateConfig(const TestServerNodeConfig &conf, const std::string &conf_file)
{
    YAML::Node node;
    node["log_dir"] = conf.common_conf.log_dir;
    node["console_log_level"] = conf.common_conf.console_log_level;
    node["file_log_level"] = conf.common_conf.file_log_level;
    node["server_port"] = conf.server_port;
    node["jpeg_quality"] = conf.jpeg_quality;
    node["max_element"] = conf.max_element;
    return WriteYamlFile(conf_file, node);
}

} // namespace fescue_iox
