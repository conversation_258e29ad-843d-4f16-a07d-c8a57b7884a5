#include "test_server.h"

#include "json.hpp"
#include "json_fwd.hpp"
#include "request_common_handler_implement.h"
#include "request_device_handler_implement.h"
#include "request_handlers.h"
#include "request_localization_handler_implement.h"
#include "request_navigation_handler_implement.h"
#include "request_perception_handler_implement.h"
#include "request_sw_handler_implement.h"
#include "utils/logger.hpp"

#include <algorithm>
#include <arpa/inet.h>
#include <chrono>
#include <cstring>
#include <fcntl.h>
#include <iostream>
#include <map>
#include <mutex>
#include <netdb.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <thread>
#include <unistd.h>
#include <unordered_map>
#include <vector>

namespace fescue_iox
{

#define BUFFER_SIZE 1024
#define HEARTBEAT_TIMEOUT 30 // 心跳超时时间（秒）

static bool run_server{true};
static std::thread mower_server_thread;

struct Request
{
    std::string method;
    int contentLength;
    std::string content;
};

struct Response
{
    std::string method;
    std::string status;
    int contentLength;
    std::string content;
};

struct ClientInfo
{
    int fd{-1};
    std::thread thread;
    ~ClientInfo()
    {
        if (thread.joinable())
            thread.detach(); // 或 join()
    }
};

RequestHandlers handlers;
std::unordered_map<int, std::chrono::time_point<std::chrono::steady_clock>> clientLastHeartbeat;
std::unordered_map<int, std::shared_ptr<ClientInfo>> clientMap;
std::mutex clientMutex;

void ProcessSendSensorDataRequest(const RequestContent &request, bool &send_flag)
{
    std::cout << "SetSendSensorData: " << request << std::endl;
    try
    {
        nlohmann::json j = nlohmann::json::parse(request);
        send_flag = j["send_flag"];
        if (send_flag)
        {
            LOG_INFO("Start send sendor data!");
        }
        else
        {
            LOG_INFO("Stop send sendor data!");
        }
    }
    catch (const nlohmann::json::parse_error &ex)
    {
        std::cerr << "JSON Parse Error: " << ex.what() << std::endl;
    }
    catch (const nlohmann::json::type_error &ex)
    {
        std::cerr << "JSON Type Error: " << ex.what() << std::endl;
    }
}

std::pair<std::string, std::string> processRequest(const std::string method, const std::string &content)
{
    std::pair<std::string, std::string> result{"404", ""};
    auto getHandler = handlers.getHandler(method);
    if (getHandler)
    {
        result = getHandler->handle(content);
    }
    return result;
}

std::string serializeResponse(const Response &response)
{
    std::string serialized = "Method: " + response.method + "\r\n";
    serialized += "Status: " + response.status + "\r\n";
    serialized += "Content-Length: " + std::to_string(response.contentLength) + "\r\n\r\n";
    serialized += response.content;
    return serialized;
}

int receiveHeader(int sockfd, std::string &header, std::string &remaining_data)
{
    char buffer[BUFFER_SIZE] = {0};
    ssize_t bytes_received;

    while (true)
    {
        bytes_received = recv(sockfd, buffer, sizeof(buffer) - 1, MSG_DONTWAIT);
        if (bytes_received <= 0)
        {
            LOG_ERROR("[receiveHeader]: Connection closed or error occurred: {}", strerror(errno));
            return bytes_received;
        }
        remaining_data.append(buffer, bytes_received);
        size_t pos = remaining_data.find("\r\n\r\n");
        if (pos != std::string::npos)
        {
            header = remaining_data.substr(0, pos + 4);
            remaining_data = remaining_data.substr(pos + 4);
            break;
        }
    }

    return header.size();
}

// 解析 header, 获取 Method 和 Content-Length
Request parseHeader(const std::string &header)
{
    Request request;

    size_t methodPos = header.find("Method: ");
    if (methodPos != std::string::npos)
    {
        size_t endPos = header.find("\r\n", methodPos);
        request.method = header.substr(methodPos + 8, endPos - (methodPos + 8));
    }

    size_t lengthPos = header.find("Content-Length: ");
    if (lengthPos != std::string::npos)
    {
        size_t endPos = header.find("\r\n", lengthPos);
        request.contentLength = std::stoi(header.substr(lengthPos + 16, endPos - (lengthPos + 16)));
    }

    return request;
}

// 读取 HTTP Body 数据（优先使用 remaining_data）
int receiveBody(int sockfd, size_t content_length, std::string &remaining_data, std::string &body)
{
    // 1. 如果 remaining_data 已包含完整 body，直接取出前 content_length 部分
    if (remaining_data.size() >= content_length)
    {
        body = remaining_data.substr(0, content_length);
        remaining_data = remaining_data.substr(content_length); // 保留多余的数据
        return content_length;
    }

    // 2. 先使用 remaining_data
    body = std::move(remaining_data);
    remaining_data.clear();
    size_t total = body.size();
    char buffer[BUFFER_SIZE] = {0};

    // 3. 继续接收剩余 body 数据
    while (total < content_length)
    {
        ssize_t bytes = recv(sockfd, buffer, std::min(BUFFER_SIZE, (int)(content_length - total)), MSG_DONTWAIT);
        if (bytes <= 0)
        {
            LOG_ERROR("[receiveBody]: Connection closed or error occurred: {}", strerror(errno));
            return bytes;
        }
        body.append(buffer, bytes);
        total += bytes;
    }

    // 4. 处理超出 content_length 的情况
    if (body.size() > content_length)
    {
        remaining_data = body.substr(content_length); // 存储多出的数据
        body = body.substr(0, content_length);        // 只返回 content_length 长度的 body
    }

    return total;
}

void setSocketOptions(int sock_fd)
{
    struct timeval tv = {0, 5000}; // 5ms 超时
    setsockopt(sock_fd, SOL_SOCKET, SO_RCVTIMEO, &tv, sizeof(tv));
    struct linger lg = {1, 0}; // 关闭 Linger 防止关闭时阻塞
    setsockopt(sock_fd, SOL_SOCKET, SO_LINGER, &lg, sizeof(lg));
    int yes = 1;
    setsockopt(sock_fd, IPPROTO_TCP, TCP_NODELAY, &yes, sizeof(yes));
    // int size = 1024 * 1024; // 1MB 缓冲区
    int size = 1024; // 1K 缓冲区
    setsockopt(sock_fd, SOL_SOCKET, SO_RCVBUF, &size, sizeof(size));
}

void sendReportData(int cfd, const std::string &method)
{
    auto resp_content = processRequest(method, "");
    Response resp = {method, resp_content.first, static_cast<int>(resp_content.second.size()), resp_content.second};
    std::string respStr = serializeResponse(resp);
    send(cfd, (void *)respStr.c_str(), (int)respStr.size(), MSG_NOSIGNAL);
}

void sendMcuSensorData(int cfd)
{
    LOG_DEBUG("send MCU_SENSOR data to client!");
    sendReportData(cfd, "GET_MCU_SENSOR");
    usleep(10 * 1000);
    sendReportData(cfd, "GET_MCU_MOTOR_SPEED");
}

void ClientHandler(int cfd /*, sockaddr_in client_addr*/)
{
    LOG_INFO("Client thread started for FD: {}", cfd);
    setSocketOptions(cfd);
    fcntl(cfd, F_SETFL, fcntl(cfd, F_GETFL) | O_NONBLOCK);

    std::string remaining_data;
    auto last_heartbeat = std::chrono::steady_clock::now();
    bool send_sensor_data_flag{false};

    while (run_server)
    {
        // 心跳超时判断
        if (std::chrono::duration_cast<std::chrono::seconds>(std::chrono::steady_clock::now() - last_heartbeat).count() > HEARTBEAT_TIMEOUT)
        {
            LOG_WARN("Client {} heartbeat timeout, disconnecting", cfd);
            break;
        }

        fd_set readfds;
        FD_ZERO(&readfds);
        FD_SET(cfd, &readfds);
        struct timeval timeout = {0, 100 * 1000}; // 100ms

        int ret = select(cfd + 1, &readfds, nullptr, nullptr, &timeout);
        if (ret < 0)
        {
            LOG_WARN("Select error on client {}: {}", cfd, strerror(errno));
            break;
        }
        else if (ret == 0)
        {
            continue; // timeout, check heartbeat in next loop
        }

        if (FD_ISSET(cfd, &readfds))
        {
            std::string header;
            int hret = receiveHeader(cfd, header, remaining_data);
            if (hret < 0)
            {
                if (errno != EAGAIN && errno != EWOULDBLOCK && errno != EINTR)
                {
                    LOG_WARN("Client {} receiveHeader error: {}", cfd, strerror(errno));
                    break;
                }
                continue;
            }
            else if (hret == 0)
            {
                LOG_INFO("Client {} closed connection", cfd);
                break;
            }

            Request req = parseHeader(header);
            if (req.contentLength > 0)
            {
                int bret = receiveBody(cfd, req.contentLength, remaining_data, req.content);
                if (bret <= 0)
                {
                    LOG_INFO("Client {} closed during body read: {}", cfd, strerror(errno));
                    break;
                }
            }

            if (req.method == "HEARTBEAT")
            {
                last_heartbeat = std::chrono::steady_clock::now();
                {
                    std::lock_guard<std::mutex> lock(clientMutex);
                    clientLastHeartbeat[cfd] = last_heartbeat;
                }
                continue;
            }

            if (req.method == "SET_SEND_SENSOR_DATA")
            {
                ProcessSendSensorDataRequest(req.content, send_sensor_data_flag);
            }
            else
            {
                auto resp_content = processRequest(req.method, req.content);
                if (req.method != "SET_NAV_MANUAL_TWIST" && req.method != "SET_TEST_MODE")
                {
                    Response resp = {req.method, resp_content.first, static_cast<int>(resp_content.second.size()), resp_content.second};
                    std::string respStr = serializeResponse(resp);
                    ssize_t size = send(cfd, respStr.data(), respStr.size(), MSG_NOSIGNAL);
                    if (size != (ssize_t)respStr.size())
                    {
                        LOG_WARN("Send response to client {} failed", cfd);
                    }
                }
            }
        }

        if (send_sensor_data_flag)
        {
            sendMcuSensorData(cfd);
        }
    }

    close(cfd);
    LOG_INFO("Client {} disconnected", cfd);
    {
        std::lock_guard<std::mutex> lock(clientMutex);
        clientLastHeartbeat.erase(cfd);
        clientMap.erase(cfd);
    }
}

void MultitServerThread(int port)
{
    int server_fd = socket(AF_INET, SOCK_STREAM, 0);
    if (server_fd < 0)
    {
        LOG_ERROR("socket error");
        return;
    }

    int opt = 1;
    setsockopt(server_fd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));

    sockaddr_in addr{};
    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = INADDR_ANY;
    addr.sin_port = htons(port);

    if (bind(server_fd, (sockaddr *)&addr, sizeof(addr)) < 0)
    {
        LOG_ERROR("bind error");
        close(server_fd);
        return;
    }

    if (listen(server_fd, SOMAXCONN) < 0)
    {
        LOG_ERROR("listen error");
        close(server_fd);
        return;
    }

    LOG_INFO("Server listening on port {}", port);

    while (run_server)
    {
        fd_set readfds;
        FD_ZERO(&readfds);
        FD_SET(server_fd, &readfds);
        struct timeval timeout = {0, 100 * 1000}; // 100ms

        int ret = select(server_fd + 1, &readfds, nullptr, nullptr, &timeout);
        if (ret < 0)
        {
            LOG_WARN("select error on server socket: {}", strerror(errno));
            continue;
        }
        else if (ret == 0)
        {
            continue;
        }

        if (FD_ISSET(server_fd, &readfds))
        {
            sockaddr_in client_addr;
            socklen_t len = sizeof(client_addr);
            int cfd = accept(server_fd, (sockaddr *)&client_addr, &len);
            if (cfd >= 0)
            {
                LOG_INFO("Accepted new connection FD {} from {}:{}", cfd, inet_ntoa(client_addr.sin_addr), ntohs(client_addr.sin_port));
                auto client = std::make_shared<ClientInfo>();
                client->fd = cfd;
                client->thread = std::thread(ClientHandler, cfd);
                {
                    std::lock_guard<std::mutex> lock(clientMutex);
                    clientLastHeartbeat[cfd] = std::chrono::steady_clock::now();
                    clientMap[cfd] = std::move(client);
                    LOG_INFO("Client {} added!", cfd);
                }
            }
        }
    }

    for (auto &[fd, info] : clientMap)
    {
        if (info->thread.joinable())
        {
            info->thread.join();
        }
        close(fd);
    }

    close(server_fd);
    LOG_INFO("Server shutdown");
}

int RunMowerTestServer(int server_port,
                       std::function<void(McuSensorData &)> mcu_sensor_callback,
                       std::function<void(McuMotorSpeedData &)> motor_speed_callback)
{
    // Perception node
    handlers.setHandler("GET_PERCEPTION_SEGMENT_OBJECT_FUSION_NODE_PARAM", std::make_shared<GetNodeParamHandler>("get_perception_segment_object_fusion_param_request"));
    handlers.setHandler("SET_PERCEPTION_SEGMENT_OBJECT_FUSION_NODE_PARAM", std::make_shared<SetNodeParamHandler>("set_perception_segment_object_fusion_param_request"));
    handlers.setHandler("GET_PERCEPTION_CHARGE_MARK_DETECT_NODE_PARAM", std::make_shared<GetNodeParamHandler>("get_perception_charg_mark_detection_node_param_request"));
    handlers.setHandler("SET_PERCEPTION_CHARGE_MARK_DETECT_NODE_PARAM", std::make_shared<SetNodeParamHandler>("set_perception_charg_mark_detection_node_param_request"));
    handlers.setHandler("GET_PERCEPTION_OCCLUSION_DETECT_NODE_PARAM", std::make_shared<GetNodeParamHandler>("get_perception_occlusion_node_param_request"));
    handlers.setHandler("SET_PERCEPTION_OCCLUSION_DETECT_NODE_PARAM", std::make_shared<SetNodeParamHandler>("set_perception_occlusion_node_param_request"));

    // Perception alg
    handlers.setHandler("GET_PERCEPTION_FUSION_ALG_PARAM", std::make_shared<GetFusionParamHandler>());
    handlers.setHandler("SET_PERCEPTION_FUSION_ALG_PARAM", std::make_shared<SetFusionParamHandler>());
    handlers.setHandler("GET_PERCEPTION_SEGMENT_ALG_PARAM", std::make_shared<GetSegmentParamHandler>());
    handlers.setHandler("SET_PERCEPTION_SEGMENT_ALG_PARAM", std::make_shared<SetSegmentParamHandler>());
    handlers.setHandler("GET_PERCEPTION_OBJECT_DETECT_ALG_PARAM", std::make_shared<GetDetectObjectParamHandler>());
    handlers.setHandler("SET_PERCEPTION_OBJECT_DETECT_ALG_PARAM", std::make_shared<SetDetectObjectParamHandler>());
    handlers.setHandler("GET_PERCEPTION_OCCLUSION_DETECT_ALG_PARAM", std::make_shared<GetPerceptionOcclusionDetectionAlgHandler>());
    handlers.setHandler("SET_PERCEPTION_OCCLUSION_DETECT_ALG_PARAM", std::make_shared<SetPerceptionOcclusionDetectionAlgHandler>());
    handlers.setHandler("GET_PERCEPTION_CHARGE_MARK_DETECT_ALG_PARAM", std::make_shared<GetPerceptionChargeMarkDetectAlgHandler>());
    handlers.setHandler("SET_PERCEPTION_CHARGE_MARK_DETECT_ALG_PARAM", std::make_shared<SetPerceptionChargeMarkDetectAlgHandler>());

    // Localization node
    handlers.setHandler("GET_LOC_RECHARGE_NODE_PARAM", std::make_shared<GetNodeParamHandler>("get_localization_recharge_node_param_request"));
    handlers.setHandler("SET_LOC_RECHARGE_NODE_PARAM", std::make_shared<SetNodeParamHandler>("set_localization_recharge_node_param_request"));
    handlers.setHandler("GET_LOC_CROSSREGION_NODE_PARAM", std::make_shared<GetNodeParamHandler>("get_localization_cross_region_node_param_request"));
    handlers.setHandler("SET_LOC_CROSSREGION_NODE_PARAM", std::make_shared<SetNodeParamHandler>("set_localization_cross_region_node_param_request"));
    handlers.setHandler("GET_LOC_MOTION_DETECT_NODE_PARAM", std::make_shared<GetNodeParamHandler>("get_localization_motion_detection_node_param"));
    handlers.setHandler("SET_LOC_MOTION_DETECT_NODE_PARAM", std::make_shared<SetNodeParamHandler>("set_localization_motion_detection_node_param"));
    handlers.setHandler("GET_LOC_AREA_ESTIMATE_NODE_PARAM", std::make_shared<GetNodeParamHandler>("get_localization_area_estimation_node_param"));
    handlers.setHandler("SET_LOC_AREA_ESTIMATE_NODE_PARAM", std::make_shared<SetNodeParamHandler>("set_localization_area_estimation_node_param"));
    handlers.setHandler("GET_LOC_SLOPE_DETECT_NODE_PARAM", std::make_shared<GetNodeParamHandler>("get_localization_slope_detection_node_param"));
    handlers.setHandler("SET_LOC_SLOPE_DETECT_NODE_PARAM", std::make_shared<SetNodeParamHandler>("set_localization_slope_detection_node_param"));

    // Localization alg
    handlers.setHandler("GET_LOC_RECHARGE_ALG_PARAM", std::make_shared<GetQRCodeLocationParamHandler>());
    handlers.setHandler("SET_LOC_RECHARGE_ALG_PARAM", std::make_shared<SetQRCodeLocationParamHandler>());
    handlers.setHandler("GET_LOC_CROSSREGION_ALG_PARAM", std::make_shared<GetMarkLocationParamHandler>());
    handlers.setHandler("SET_LOC_CROSSREGION_ALG_PARAM", std::make_shared<SetMarkLocationParamHandler>());
    handlers.setHandler("GET_LOC_MOTION_DETECT_ALG_PARAM", std::make_shared<GetLocMotionDetectAlgParamHandler>());
    handlers.setHandler("SET_LOC_MOTION_DETECT_ALG_PARAM", std::make_shared<SetLocMotionDetectAlgParamHandler>());
    handlers.setHandler("GET_LOC_AREA_ESTIMATE_ALG_PARAM", std::make_shared<GetLocAreaEstimateAlgParamHandler>());
    handlers.setHandler("SET_LOC_AREA_ESTIMATE_ALG_PARAM", std::make_shared<SetLocAreaEstimateAlgParamHandler>());

    // Device
    handlers.setHandler("GET_CAMERA_NODE_PARAM", std::make_shared<GetCameraNodeParamHandler>());
    handlers.setHandler("SET_CAMERA_NODE_PARAM", std::make_shared<SetCameraNodeParamHandler>());
    handlers.setHandler("GET_UNION_RGB_PARAM", std::make_shared<GetUnionRGBCameraParamHandler>());
    handlers.setHandler("SET_UNION_RGB_PARAM", std::make_shared<SetUnionRGBCameraParamHandler>());
    handlers.setHandler("GET_SERIALPROTOCOL_PARAM", std::make_shared<GetSerialProtocolParamHandler>());
    handlers.setHandler("SET_SERIALPROTOCOL_PARAM", std::make_shared<SetSerialProtocolParamHandler>());
    // handlers.setHandler("GET_CPU_USAGE", std::make_shared<GetProcessCpuUsageHandler>());
    handlers.setHandler("GET_MCU_SENSOR", std::make_shared<GetMcuSensorDataHandler>(mcu_sensor_callback));
    handlers.setHandler("GET_MCU_MOTOR_SPEED", std::make_shared<GetMcuMotorSpeedDataHandler>(motor_speed_callback));
    handlers.setHandler("SET_TEST_MODE", std::make_shared<SetTestModeHandler>());
    handlers.setHandler("SET_DEV_SELF_CHECK", std::make_shared<SetDevSelfCheckHandler>());

    // Navigation node
    handlers.setHandler("SET_NAV_MOWER_NODE_PARAM", std::make_shared<SetNavigationMowerNodeParamHandler>());
    handlers.setHandler("GET_NAV_MOWER_NODE_PARAM", std::make_shared<GetNavigationMowerNodeParamHandler>());
    handlers.setHandler("SET_NAV_RECHARGE_NODE_PARAM", std::make_shared<SetNavigationRechargeNodeParamHandler>());
    handlers.setHandler("GET_NAV_RECHARGE_NODE_PARAM", std::make_shared<GetNavigationRechargeNodeParamHandler>());
    handlers.setHandler("SET_NAV_RANDOM_MOWER_NODE_PARAM", std::make_shared<SetNavigationRandomMowerNodeParamHandler>());
    handlers.setHandler("GET_NAV_RANDOM_MOWER_NODE_PARAM", std::make_shared<GetNavigationRandomMowerNodeParamHandler>());
    handlers.setHandler("SET_NAV_EDGE_FOLLOW_NODE_PARAM", std::make_shared<SetNavigationEdgeFollowNodeParamHandler>());
    handlers.setHandler("GET_NAV_EDGE_FOLLOW_NODE_PARAM", std::make_shared<GetNavigationEdgeFollowNodeParamHandler>());
    handlers.setHandler("SET_NAV_CROSS_REGION_NODE_PARAM", std::make_shared<SetNavigationCrossRegionNodeParamHandler>());
    handlers.setHandler("GET_NAV_CROSS_REGION_NODE_PARAM", std::make_shared<GetNavigationCrossRegionNodeParamHandler>());
    handlers.setHandler("SET_NAV_SPIRAL_MOWER_NODE_PARAM", std::make_shared<SetNavigationSpiralMowerNodeParamHandler>());
    handlers.setHandler("GET_NAV_SPIRAL_MOWER_NODE_PARAM", std::make_shared<GetNavigationSpiralMowerNodeParamHandler>());
    handlers.setHandler("SET_NAV_ESCAPE_NODE_PARAM", std::make_shared<SetNodeParamHandler>("set_navigation_escape_node_param_request"));
    handlers.setHandler("GET_NAV_ESCAPE_NODE_PARAM", std::make_shared<GetNodeParamHandler>("get_navigation_escape_node_param_request"));
    handlers.setHandler("SET_NAV_BEHAVIOR_NODE_PARAM", std::make_shared<SetNodeParamHandler>("set_navigation_behavior_node_param_request"));
    handlers.setHandler("GET_NAV_BEHAVIOR_NODE_PARAM", std::make_shared<GetNodeParamHandler>("get_navigation_behavior_node_param_request"));
    handlers.setHandler("NAV_MOWER_ALG_TEST", std::make_shared<NavMowerAlgTestHandler>());
    handlers.setHandler("SET_NAV_MANUAL_TWIST", std::make_shared<NavigationMowerManualTwistHandler>());

    // Navigation alg
    handlers.setHandler("SET_NAV_CROSS_REGION_ALG_PARAM", std::make_shared<SetNavigationCrossRegionAlgParamHandler>());
    handlers.setHandler("GET_NAV_CROSS_REGION_ALG_PARAM", std::make_shared<GetNavigationCrossRegionAlgParamHandler>());
    handlers.setHandler("SET_NAV_SPIRAL_MOWER_ALG_PARAM", std::make_shared<SetNavigationSpiralMowerAlgParamHandler>());
    handlers.setHandler("GET_NAV_SPIRAL_MOWER_ALG_PARAM", std::make_shared<GetNavigationSpiralMowerAlgParamHandler>());
    handlers.setHandler("SET_NAV_EDGE_FOLLOW_ALG_PARAM", std::make_shared<SetNavigationEdgeFollowAlgParamHandler>());
    handlers.setHandler("GET_NAV_EDGE_FOLLOW_ALG_PARAM", std::make_shared<GetNavigationEdgeFollowAlgParamHandler>());

    // SW
    handlers.setHandler("SW_GO_MOWER", std::make_shared<SWGoMowerHandler>());
    handlers.setHandler("SW_GO_CHARGE", std::make_shared<SWGoChargeHandler>());
    handlers.setHandler("GET_VERSION_INFO", std::make_shared<GetVersionInfoHandler>());

    // device
    handlers.setHandler("GET_CALIBRATION_BEV_NODE_MOWER_PARAM", std::make_shared<GetCalibrationNodeParamHandler>());
    handlers.setHandler("SET_CALIBRATION_BEV_NODE_MOWER_PARAM", std::make_shared<SetCalibrationNodeParamHandler>());
    handlers.setHandler("EXECUTE_CALIBRATION_BEV", std::make_shared<ExecuteCalibrationBevHandler>());

    mower_server_thread = std::thread(MultitServerThread, server_port);
    return 0;
}

void StopMowerTestServer()
{
    LOG_WARN("stopMowerTestServer start!");
    run_server = false;
    mower_server_thread.join();
    LOG_WARN("stopMowerTestServer OK!");
}

} // namespace fescue_iox
