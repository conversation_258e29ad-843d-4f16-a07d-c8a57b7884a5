#include "connect_pose_tracker.hpp"
#include "utils/time.hpp"
#include "utils/logger.hpp"

void TestConnectTracker() {
    fescue_iox::TrajectoryPose start_pose(1.0, 1.0, 0);
    fescue_iox::TrajectoryPose end_pose(1.0, 1.0, 1.57);
    fescue_iox::OccupancyResult occupancy_result;
    fescue_iox::ConnectPoseTracker connect_pose_tracker(start_pose, end_pose, occupancy_result);
    fescue_iox::TrajectoryPose cur_pose;
    fescue_iox::OdomResult odom_velocity;
    double time_now;
    double tracker_linear_velocity;
    double tracker_angular_velocity;

    time_now = 1.0 * fescue_iox::GetTimestampMs() / 1000.0;
    cur_pose.x = 1.0;
    cur_pose.y = 1.0;
    cur_pose.theta = 0;
    odom_velocity.linear = 0;
    connect_pose_tracker.Update(time_now, cur_pose, odom_velocity);
    tracker_linear_velocity = connect_pose_tracker.GetLinearVelocity();
    tracker_angular_velocity = connect_pose_tracker.GetAngularVelocity();
    auto result = connect_pose_tracker.GetResult();
    LOG_INFO("time_now: {}, linear_velocity: {:.2f}, angular_velocity: {:.2f}, result: {}", 
             time_now, tracker_linear_velocity, tracker_angular_velocity, fescue_iox::TrackerResultString(result));

    time_now = 1.0 * fescue_iox::GetTimestampMs() / 1000.0;
    cur_pose.x = 1.05;
    cur_pose.y = 1.0;
    cur_pose.theta = 0;
    odom_velocity.linear = 0.05;
    connect_pose_tracker.Update(time_now, cur_pose, odom_velocity);
    tracker_linear_velocity = connect_pose_tracker.GetLinearVelocity();
    tracker_angular_velocity = connect_pose_tracker.GetAngularVelocity();
    result = connect_pose_tracker.GetResult();
    LOG_INFO("time_now: {}, linear_velocity: {:.2f}, angular_velocity: {:.2f}, result: {}", 
             time_now, tracker_linear_velocity, tracker_angular_velocity, fescue_iox::TrackerResultString(result));

    time_now = 1.0 * fescue_iox::GetTimestampMs() / 1000.0;
    cur_pose.x = 1.3;
    cur_pose.y = 1.06;
    cur_pose.theta = 0.4;
    odom_velocity.linear = 0.15;
    connect_pose_tracker.Update(time_now, cur_pose, odom_velocity);
    tracker_linear_velocity = connect_pose_tracker.GetLinearVelocity();
    tracker_angular_velocity = connect_pose_tracker.GetAngularVelocity();
    result = connect_pose_tracker.GetResult();
    LOG_INFO("time_now: {}, linear_velocity: {:.2f}, angular_velocity: {:.2f}, result: {}", 
             time_now, tracker_linear_velocity, tracker_angular_velocity, fescue_iox::TrackerResultString(result));

    time_now = 1.0 * fescue_iox::GetTimestampMs() / 1000.0;
    cur_pose.x = 1.32;
    cur_pose.y = 1.06;
    cur_pose.theta = 0.4;
    odom_velocity.linear = 0.15;
    connect_pose_tracker.Update(time_now, cur_pose, odom_velocity);
    tracker_linear_velocity = connect_pose_tracker.GetLinearVelocity();
    tracker_angular_velocity = connect_pose_tracker.GetAngularVelocity();
    result = connect_pose_tracker.GetResult();
    LOG_INFO("time_now: {}, linear_velocity: {:.2f}, angular_velocity: {:.2f}, result: {}", 
             time_now, tracker_linear_velocity, tracker_angular_velocity, fescue_iox::TrackerResultString(result));
    
    time_now = 1.0 * fescue_iox::GetTimestampMs() / 1000.0;
    cur_pose.x = 1.0;
    cur_pose.y = 0.59;
    cur_pose.theta = 1.56;
    odom_velocity.linear = -0.15;
    connect_pose_tracker.Update(time_now, cur_pose, odom_velocity);
    tracker_linear_velocity = connect_pose_tracker.GetLinearVelocity();
    tracker_angular_velocity = connect_pose_tracker.GetAngularVelocity();
    result = connect_pose_tracker.GetResult();
    LOG_INFO("time_now: {}, linear_velocity: {:.2f}, angular_velocity: {:.2f}, result: {}", 
             time_now, tracker_linear_velocity, tracker_angular_velocity, fescue_iox::TrackerResultString(result));
    
    time_now = 1.0 * fescue_iox::GetTimestampMs() / 1000.0;
    cur_pose.x = 1.02;
    cur_pose.y = 0.61;
    cur_pose.theta = 1.57;
    odom_velocity.linear = -0.15;
    connect_pose_tracker.Update(time_now, cur_pose, odom_velocity);
    tracker_linear_velocity = connect_pose_tracker.GetLinearVelocity();
    tracker_angular_velocity = connect_pose_tracker.GetAngularVelocity();
    result = connect_pose_tracker.GetResult();
    LOG_INFO("time_now: {}, linear_velocity: {:.2f}, angular_velocity: {:.2f}, result: {}", 
             time_now, tracker_linear_velocity, tracker_angular_velocity, fescue_iox::TrackerResultString(result));
    
    time_now = 1.0 * fescue_iox::GetTimestampMs() / 1000.0;
    cur_pose.x = 0.99;
    cur_pose.y = 1.0;
    cur_pose.theta = 1.57;
    odom_velocity.linear = 0.15;
    connect_pose_tracker.Update(time_now, cur_pose, odom_velocity);
    tracker_linear_velocity = connect_pose_tracker.GetLinearVelocity();
    tracker_angular_velocity = connect_pose_tracker.GetAngularVelocity();
    result = connect_pose_tracker.GetResult();
    LOG_INFO("time_now: {}, linear_velocity: {:.2f}, angular_velocity: {:.2f}, result: {}", 
             time_now, tracker_linear_velocity, tracker_angular_velocity, fescue_iox::TrackerResultString(result));
    
    time_now = 1.0 * fescue_iox::GetTimestampMs() / 1000.0;
    cur_pose.x = 1.02;
    cur_pose.y = 1.0;
    cur_pose.theta = 1.57;
    odom_velocity.linear = 0.15;
    connect_pose_tracker.Update(time_now, cur_pose, odom_velocity);
    tracker_linear_velocity = connect_pose_tracker.GetLinearVelocity();
    tracker_angular_velocity = connect_pose_tracker.GetAngularVelocity();
    result = connect_pose_tracker.GetResult();
    LOG_INFO("time_now: {}, linear_velocity: {:.2f}, angular_velocity: {:.2f}, result: {}", 
             time_now, tracker_linear_velocity, tracker_angular_velocity, fescue_iox::TrackerResultString(result));
}

void TestConnectTracker2() {
    fescue_iox::TrajectoryPose start_pose(1.0, 1.0, 0);
    fescue_iox::TrajectoryPose end_pose(1.0, 1.0, 1.57);
    fescue_iox::OccupancyResult occupancy_result;
    fescue_iox::ConnectPoseTracker connect_pose_tracker(start_pose, end_pose, occupancy_result);
    fescue_iox::TrajectoryPose cur_pose;
    fescue_iox::OdomResult odom_velocity;
    double time_now;
    double tracker_linear_velocity;
    double tracker_angular_velocity;

    time_now = 1.0 * fescue_iox::GetTimestampMs() / 1000.0;
    cur_pose.x = 1.0;
    cur_pose.y = 1.0;
    cur_pose.theta = 0;
    odom_velocity.linear = 0;
    connect_pose_tracker.Update(time_now, cur_pose, odom_velocity);
    tracker_linear_velocity = connect_pose_tracker.GetLinearVelocity();
    tracker_angular_velocity = connect_pose_tracker.GetAngularVelocity();
    auto result = connect_pose_tracker.GetResult();
    LOG_INFO("****time_now: {}, linear_velocity: {:.2f}, angular_velocity: {:.2f}, result: {}", 
             time_now, tracker_linear_velocity, tracker_angular_velocity, fescue_iox::TrackerResultString(result));
    
    time_now = 1.0 * fescue_iox::GetTimestampMs() / 1000.0;
    cur_pose.x = 1.0;
    cur_pose.y = 1.0;
    cur_pose.theta = 0.5;
    odom_velocity.linear = 0;
    connect_pose_tracker.Update(time_now, cur_pose, odom_velocity);
    tracker_linear_velocity = connect_pose_tracker.GetLinearVelocity();
    tracker_angular_velocity = connect_pose_tracker.GetAngularVelocity();
    result = connect_pose_tracker.GetResult();
    LOG_INFO("****time_now: {}, linear_velocity: {:.2f}, angular_velocity: {:.2f}, result: {}", 
             time_now, tracker_linear_velocity, tracker_angular_velocity, fescue_iox::TrackerResultString(result));
    
    time_now = 1.0 * fescue_iox::GetTimestampMs() / 1000.0;
    cur_pose.x = 1.0;
    cur_pose.y = 1.0;
    cur_pose.theta = 1.4;
    odom_velocity.linear = 0;
    connect_pose_tracker.Update(time_now, cur_pose, odom_velocity);
    tracker_linear_velocity = connect_pose_tracker.GetLinearVelocity();
    tracker_angular_velocity = connect_pose_tracker.GetAngularVelocity();
    result = connect_pose_tracker.GetResult();
    LOG_INFO("****time_now: {}, linear_velocity: {:.2f}, angular_velocity: {:.2f}, result: {}", 
             time_now, tracker_linear_velocity, tracker_angular_velocity, fescue_iox::TrackerResultString(result));
    
    time_now = 1.0 * fescue_iox::GetTimestampMs() / 1000.0;
    cur_pose.x = 1.0;
    cur_pose.y = 1.0;
    cur_pose.theta = 1.52;
    odom_velocity.linear = 0;
    connect_pose_tracker.Update(time_now, cur_pose, odom_velocity);
    tracker_linear_velocity = connect_pose_tracker.GetLinearVelocity();
    tracker_angular_velocity = connect_pose_tracker.GetAngularVelocity();
    result = connect_pose_tracker.GetResult();
    LOG_INFO("****time_now: {}, linear_velocity: {:.2f}, angular_velocity: {:.2f}, result: {}", 
             time_now, tracker_linear_velocity, tracker_angular_velocity, fescue_iox::TrackerResultString(result));
    
    time_now = 1.0 * fescue_iox::GetTimestampMs() / 1000.0;
    cur_pose.x = 1.0;
    cur_pose.y = 1.0;
    cur_pose.theta = 1.6;
    odom_velocity.linear = 0;
    connect_pose_tracker.Update(time_now, cur_pose, odom_velocity);
    tracker_linear_velocity = connect_pose_tracker.GetLinearVelocity();
    tracker_angular_velocity = connect_pose_tracker.GetAngularVelocity();
    result = connect_pose_tracker.GetResult();
    LOG_INFO("****time_now: {}, linear_velocity: {:.2f}, angular_velocity: {:.2f}, result: {}", 
             time_now, tracker_linear_velocity, tracker_angular_velocity, fescue_iox::TrackerResultString(result));
}

int main(int argc, char *argv[]) {
    (void)argc;
    (void)argv;
    TestConnectTracker2();
    return 0;
}