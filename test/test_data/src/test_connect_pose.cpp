#include "connect_trajectory_generator.hpp"
#include <fstream>
#include <iomanip>
#include <cstdlib>
#include <iostream>
#include "json.hpp"
#include "utils/logger.hpp"

fescue_iox::TrajectoryPose start_pose;
fescue_iox::TrajectoryPose end_pose;
fescue_iox::OccupancyResult occupancy_result;
fescue_iox::TrajectoryMode trajectory_mode = fescue_iox::TrajectoryMode::TRAJECTORY_MODE_NORMAL;

void ParseInputData(const std::string& file_path) 
{
    // 判断文件是否存在
    if (!std::filesystem::exists(file_path)) {
        LOG_INFO("File does not exist: {}", file_path);
        return;
    }
    // 读取文件的第一行的字符串
    std::ifstream file(file_path);
    std::string json_str;
    std::getline(file, json_str);
    file.close();
    // 解析json对象
    nlohmann::json json_obj = nlohmann::json::parse(json_str);
    start_pose.x = json_obj["start_pose"]["x"];
    start_pose.y = json_obj["start_pose"]["y"];
    start_pose.theta = json_obj["start_pose"]["yaw"];
    end_pose.x = json_obj["end_pose"]["x"];
    end_pose.y = json_obj["end_pose"]["y"];
    end_pose.theta = json_obj["end_pose"]["yaw"];
    int16_t occupancy_width = json_obj["occupancy_result"]["width"];
    int16_t occupancy_height = json_obj["occupancy_result"]["height"];
    float occupancy_resolution = json_obj["occupancy_result"]["resolution"];
    const auto& occupancy_grid = json_obj["occupancy_result"]["grid"];
    for (int i = 0; i < occupancy_height; i++) {
        std::vector<uint8_t> row;
        for (int j = 0; j < occupancy_width; j++) {
            row.push_back(occupancy_grid[i * occupancy_width + j]);
        }
        occupancy_result.grid.push_back(row);
    }
    occupancy_result.width = occupancy_width;
    occupancy_result.height = occupancy_height;
    occupancy_result.resolution = occupancy_resolution;
    if (json_obj.contains("trajectory_mode")) {
        trajectory_mode = static_cast<fescue_iox::TrajectoryMode>(json_obj["trajectory_mode"]);
    }
}

double CalculateTrajectoryLength(const std::vector<fescue_iox::TrajectoryPose>& traj) {
    double length = 0;
    for (size_t i = 1; i < traj.size(); ++i) {
        length += std::hypot(traj[i].x - traj[i - 1].x, traj[i].y - traj[i - 1].y);
    }
    return length;
}

int main(int argc, char *argv[])
{
    std::shared_ptr<fescue_iox::ConnectTrajectoryGenerator> connect_trajectory_generator = nullptr;
    std::vector<double> fusion_pose_x;
    std::vector<double> fusion_pose_y;
    std::vector<double> fusion_pose_yaw;

    if (argc >= 3) {
        std::ifstream file(argv[2]);
        if (!file.is_open()) {
            std::cerr << "Failed to open file: " << argv[2] << std::endl;
            return 1;
        }
        std::string line;
        while (std::getline(file, line)) {
            try {
                nlohmann::json j = nlohmann::json::parse(line);
                if (!j.contains("fusion_pose")) {
                    continue;
                }
                const auto& fusion_pose = j["fusion_pose"];
                fusion_pose_x.push_back(fusion_pose["x"]);
                fusion_pose_y.push_back(fusion_pose["y"]);
                fusion_pose_yaw.push_back(fusion_pose["yaw"]);
            } catch (const nlohmann::json::parse_error& e) {
                std::cerr << "JSON parse error: " << e.what() << std::endl;
                continue;
            } catch (const nlohmann::json::exception& e) {
                std::cerr << "JSON error: " << e.what() << std::endl;
                continue;
            }
        }
        file.close();
        LOG_INFO("fusion_pose_x size: {}, fusion_pose_y size: {}, fusion_pose_yaw size: {}",
                 fusion_pose_x.size(), fusion_pose_y.size(), fusion_pose_yaw.size());
    }
    else {
        LOG_INFO("No input file provided");
    }

    // 导出fusion_pose数据到CSV
    std::string home_path(getenv("HOME"));
    std::string fusion_csv_path = home_path + "/compare_pose.csv";
    std::ofstream fusion_csv_file(fusion_csv_path);
    if (!fusion_csv_file.is_open()) {
        std::cerr << "Failed to open fusion_pose CSV file for writing." << std::endl;
    } else {
        fusion_csv_file << "x,y,yaw" << std::endl;
        for (size_t i = 0; i < fusion_pose_x.size(); ++i) {
            fusion_csv_file << std::setprecision(15) << fusion_pose_x[i] << ","
                            << fusion_pose_y[i] << ","
                            << fusion_pose_yaw[i] << std::endl;
        }
        fusion_csv_file.close();
        LOG_INFO("Fusion pose data saved to {}", fusion_csv_path);
    }

    if (argc >= 2) {
        ParseInputData(argv[1]);
    } else {
        // 手动构造数据
        LOG_INFO("Manual construct data");
        start_pose.x = 1.0;
        start_pose.y = 1.0;
        start_pose.theta = 0.0;
        end_pose.x = 1.0;
        end_pose.y = 1.0;
        end_pose.theta = 0.25;
    }
    connect_trajectory_generator = std::make_shared<fescue_iox::ConnectTrajectoryGenerator>(trajectory_mode);

    LOG_INFO("start_pose: x: {}, y: {}, theta: {}", start_pose.x, start_pose.y, start_pose.theta);
    LOG_INFO("end_pose: x: {}, y: {}, theta: {}", end_pose.x, end_pose.y, end_pose.theta);

    std::vector<std::vector<fescue_iox::TrajectoryPose>> connect_trajs = connect_trajectory_generator->ConnectPose(start_pose, end_pose, occupancy_result);
    std::cout << "connect_trajs size: " << connect_trajs.size() << std::endl;
    for (size_t i = 0; i < connect_trajs.size(); ++i) {
        std::cout << "connect_trajs[" << i << "] size: " << connect_trajs[i].size() 
                  << " length: " << CalculateTrajectoryLength(connect_trajs[i]) 
                  << std::endl;
        for (const auto& pose : connect_trajs[i]) {
            std::cout << "pose x: " << pose.x << " y: " << pose.y << " theta: " << pose.theta 
                      << " linear_velocity: " << pose.linear_velocity << " angular_velocity: " << pose.angular_velocity 
                      << std::endl;
        }
    }

    // 打开 CSV 文件（指定绝对路径）
    std::string connected_trajectories_path = home_path + "/connected_trajectories.csv";
    std::ofstream csv_file(connected_trajectories_path);
    if (!csv_file.is_open()) {
        std::cerr << "Failed to open CSV file for writing." << std::endl;
        return -1;
    }

    // 写入 CSV 表头
    csv_file << "trajectory_id,x,y,theta,linear_velocity,angular_velocity" << std::endl;

    // 遍历每条轨迹
    for (size_t traj_id = 0; traj_id < connect_trajs.size(); ++traj_id) {
        const auto& trajectory = connect_trajs[traj_id];
        // 遍历轨迹中的每个点
        for (const auto& pose : trajectory) {
            csv_file << traj_id << ","
                     << std::setprecision(15) << pose.x << ","
                     << pose.y << ","
                     << pose.theta << ","
                     << pose.linear_velocity << ","
                     << pose.angular_velocity << std::endl;
        }
    }

    csv_file.close();
    std::cout << "Trajectories saved to " << connected_trajectories_path << std::endl;
    return 0;
}
