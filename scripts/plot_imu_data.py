#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import argparse
import os

def plot_imu_data(input_file, output_dir=None):
    """
    读取IMU数据文件并绘制曲线
    
    Args:
        input_file: IMU数据文件路径
        output_dir: 输出图表的目录路径，如果不指定则不保存图片
    """
    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：文件 {input_file} 不存在")
        return
    
    # 读取CSV数据
    try:
        data = pd.read_csv(input_file)
        print(f"已成功读取数据文件，共 {len(data)} 条记录")
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return
    
    # 将时间戳转换为相对时间（秒）
    if 'timestamp' in data.columns:
        data['relative_time'] = (data['timestamp'] - data['timestamp'].iloc[0]) / 1000.0
    
    # 创建多个子图
    fig, axes = plt.subplots(2, 1, figsize=(12, 10), sharex=True)
    
    # 绘制线性加速度数据
    ax = axes[0]
    time_array = data['relative_time'].to_numpy()
    # ax.plot(time_array, data['accel_x'].to_numpy(), 'r-', label='raw accel_x')
    # ax.plot(time_array, data['accel_y'].to_numpy(), 'g-', label='raw accel_y')
    # ax.plot(time_array, data['accel_z'].to_numpy(), 'b-', label='raw accel_z')
    
    if 'filtered_accel_x' in data.columns:
        ax.plot(time_array, data['filtered_accel_x'].to_numpy(), 'r-', label='filter accel_x')
        ax.plot(time_array, data['filtered_accel_y'].to_numpy(), 'g-', label='filter accel_y')
        ax.plot(time_array, data['filtered_accel_z'].to_numpy(), 'b-', label='filter accel_z')
    
    ax.set_title('Linear acceleration data')
    ax.set_ylabel('acceleration (m/s²)')
    ax.legend(loc='upper right')
    ax.grid(True)
    
    # 绘制角速度数据
    ax = axes[1]
    # ax.plot(time_array, data['gyro_x'].to_numpy(), 'r-', label='raw gyro_x')
    # ax.plot(time_array, data['gyro_y'].to_numpy(), 'g-', label='raw gyro_y')
    ax.plot(time_array, data['gyro_z'].to_numpy(), 'b-', label='raw gyro_z')
    
    if 'filtered_gyro_x' in data.columns:
        # ax.plot(time_array, data['filtered_gyro_x'].to_numpy(), 'r-', label='filter gyro_x')
        # ax.plot(time_array, data['filtered_gyro_y'].to_numpy(), 'g-', label='filter gyro_y')
        ax.plot(time_array, data['filtered_gyro_z'].to_numpy(), 'r-', label='filter gyro_z')
    
    ax.set_title('Angular velocity data')
    ax.set_xlabel('Time (seconds)')
    ax.set_ylabel('Angular speed (rad/s)')
    ax.legend(loc='upper right')
    ax.grid(True)
    
    # 设置子图之间的间距
    plt.tight_layout()
    
    # 保存图片（如果指定了输出目录）
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        output_file = os.path.join(output_dir, 'imu_data_plot.png')
        plt.savefig(output_file, dpi=300)
        print(f"已保存图表到: {output_file}")
    
    # 显示图表
    plt.show()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='绘制IMU数据曲线')
    parser.add_argument('input_file', help='IMU数据文件路径')
    parser.add_argument('--output_dir', help='输出图表的目录路径（可选）')
    
    args = parser.parse_args()
    plot_imu_data(args.input_file, args.output_dir) 