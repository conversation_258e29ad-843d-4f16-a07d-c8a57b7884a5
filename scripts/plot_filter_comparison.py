#!/usr/bin/env python3
"""
Script to plot comparison curves for filtered vs raw data from stuck_recovery_filter_data.csv
Creates a two-column plot showing angular velocity and linear acceleration comparisons.
"""

import pandas as pd
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import numpy as np
import os

def load_data(csv_file):
    """Load data from CSV file"""
    try:
        data = pd.read_csv(csv_file)
        print(f"Loaded {len(data)} data points from {csv_file}")
        return data
    except FileNotFoundError:
        print(f"Error: File {csv_file} not found!")
        return None
    except Exception as e:
        print(f"Error loading data: {e}")
        return None

def convert_timestamp_to_seconds(data):
    """Convert timestamp to relative time in seconds"""
    if 'timestamp' in data.columns:
        # Convert timestamp to relative time (assuming milliseconds)
        start_time = data['timestamp'].iloc[0]
        data['time_seconds'] = (data['timestamp'] - start_time) / 1000.0
    else:
        # Create a simple time index if no timestamp
        data['time_seconds'] = np.arange(len(data)) * 0.01  # Assume 10ms intervals
    return data

def plot_comparison(data, output_file=None):
    """Create comparison plots with two columns"""

    # Set up the figure with two subplots (2 columns, 1 row)
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 6))
    fig.suptitle('Filtered vs Raw Data Comparison', fontsize=16, fontweight='bold')

    # Convert pandas series to numpy arrays to avoid compatibility issues
    time_data = data['time_seconds'].values
    raw_angular = data['raw_angular_velocity'].values
    filtered_angular = data['filtered_angular_velocity'].values
    raw_linear = data['raw_linear_acceleration'].values
    filtered_linear = data['filtered_linear_acceleration'].values

    # Plot 1: Angular Velocity Comparison
    ax1.plot(time_data, raw_angular,
             label='Raw Angular Velocity', color='red', alpha=0.7, linewidth=1)
    ax1.plot(time_data, filtered_angular,
             label='Filtered Angular Velocity', color='blue', linewidth=2)

    ax1.set_xlabel('Time (seconds)', fontsize=12)
    ax1.set_ylabel('Angular Velocity (rad/s)', fontsize=12)
    ax1.set_title('Angular Velocity: Raw vs Filtered', fontsize=14, fontweight='bold')
    ax1.legend(fontsize=10)
    ax1.grid(True, alpha=0.3)

    # Add statistics text box for angular velocity
    raw_std = np.std(raw_angular)
    filtered_std = np.std(filtered_angular)
    noise_reduction = ((raw_std - filtered_std) / raw_std) * 100

    stats_text1 = f'Raw Std: {raw_std:.6f}\nFiltered Std: {filtered_std:.6f}\nNoise Reduction: {noise_reduction:.1f}%'
    ax1.text(0.02, 0.98, stats_text1, transform=ax1.transAxes, fontsize=9,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    # Plot 2: Linear Acceleration Comparison
    ax2.plot(time_data, raw_linear,
             label='Raw Linear Acceleration', color='orange', alpha=0.7, linewidth=1)
    ax2.plot(time_data, filtered_linear,
             label='Filtered Linear Acceleration', color='green', linewidth=2)

    ax2.set_xlabel('Time (seconds)', fontsize=12)
    ax2.set_ylabel('Linear Acceleration (m/s²)', fontsize=12)
    ax2.set_title('Linear Acceleration: Raw vs Filtered', fontsize=14, fontweight='bold')
    ax2.legend(fontsize=10)
    ax2.grid(True, alpha=0.3)

    # Add statistics text box for linear acceleration
    raw_std_acc = np.std(raw_linear)
    filtered_std_acc = np.std(filtered_linear)
    noise_reduction_acc = ((raw_std_acc - filtered_std_acc) / raw_std_acc) * 100

    stats_text2 = f'Raw Std: {raw_std_acc:.6f}\nFiltered Std: {filtered_std_acc:.6f}\nNoise Reduction: {noise_reduction_acc:.1f}%'
    ax2.text(0.02, 0.98, stats_text2, transform=ax2.transAxes, fontsize=9,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    # Adjust layout to prevent overlap
    plt.tight_layout()

    # Save the plot if output file is specified
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"Plot saved to {output_file}")

    # Close the figure to free memory
    plt.close(fig)

    return fig

def plot_comparison_interactive(data, output_file=None):
    """Create comparison plots with interactive display"""
    # Temporarily switch to interactive backend
    import matplotlib.pyplot as plt

    # Set up the figure with two subplots (2 columns, 1 row)
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 6))
    fig.suptitle('Filtered vs Raw Data Comparison', fontsize=16, fontweight='bold')

    # Convert pandas series to numpy arrays to avoid compatibility issues
    time_data = data['time_seconds'].values
    raw_angular = data['raw_angular_velocity'].values
    filtered_angular = data['filtered_angular_velocity'].values
    raw_linear = data['raw_linear_acceleration'].values
    filtered_linear = data['filtered_linear_acceleration'].values

    # Plot 1: Angular Velocity Comparison
    ax1.plot(time_data, raw_angular,
             label='Raw Angular Velocity', color='red', alpha=0.7, linewidth=1)
    ax1.plot(time_data, filtered_angular,
             label='Filtered Angular Velocity', color='blue', linewidth=2)

    ax1.set_xlabel('Time (seconds)', fontsize=12)
    ax1.set_ylabel('Angular Velocity (rad/s)', fontsize=12)
    ax1.set_title('Angular Velocity: Raw vs Filtered', fontsize=14, fontweight='bold')
    ax1.legend(fontsize=10)
    ax1.grid(True, alpha=0.3)

    # Add statistics text box for angular velocity
    raw_std = np.std(raw_angular)
    filtered_std = np.std(filtered_angular)
    noise_reduction = ((raw_std - filtered_std) / raw_std) * 100

    stats_text1 = f'Raw Std: {raw_std:.6f}\nFiltered Std: {filtered_std:.6f}\nNoise Reduction: {noise_reduction:.1f}%'
    ax1.text(0.02, 0.98, stats_text1, transform=ax1.transAxes, fontsize=9,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    # Plot 2: Linear Acceleration Comparison
    ax2.plot(time_data, raw_linear,
             label='Raw Linear Acceleration', color='orange', alpha=0.7, linewidth=1)
    ax2.plot(time_data, filtered_linear,
             label='Filtered Linear Acceleration', color='green', linewidth=2)

    ax2.set_xlabel('Time (seconds)', fontsize=12)
    ax2.set_ylabel('Linear Acceleration (m/s²)', fontsize=12)
    ax2.set_title('Linear Acceleration: Raw vs Filtered', fontsize=14, fontweight='bold')
    ax2.legend(fontsize=10)
    ax2.grid(True, alpha=0.3)

    # Add statistics text box for linear acceleration
    raw_std_acc = np.std(raw_linear)
    filtered_std_acc = np.std(filtered_linear)
    noise_reduction_acc = ((raw_std_acc - filtered_std_acc) / raw_std_acc) * 100

    stats_text2 = f'Raw Std: {raw_std_acc:.6f}\nFiltered Std: {filtered_std_acc:.6f}\nNoise Reduction: {noise_reduction_acc:.1f}%'
    ax2.text(0.02, 0.98, stats_text2, transform=ax2.transAxes, fontsize=9,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    # Adjust layout to prevent overlap
    plt.tight_layout()

    # Save the plot if output file is specified
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"Plot saved to {output_file}")

    # Show the interactive plot
    plt.show()

    return fig

def print_data_summary(data):
    """Print summary statistics of the data"""
    print("\n" + "="*60)
    print("DATA SUMMARY")
    print("="*60)

    print(f"Total data points: {len(data)}")
    print(f"Time range: {data['time_seconds'].iloc[0]:.2f} to {data['time_seconds'].iloc[-1]:.2f} seconds")
    print(f"Duration: {data['time_seconds'].iloc[-1] - data['time_seconds'].iloc[0]:.2f} seconds")

    print("\nAngular Velocity Statistics:")
    print(f"  Raw - Mean: {np.mean(data['raw_angular_velocity']):.6f}, Std: {np.std(data['raw_angular_velocity']):.6f}")
    print(f"  Filtered - Mean: {np.mean(data['filtered_angular_velocity']):.6f}, Std: {np.std(data['filtered_angular_velocity']):.6f}")

    print("\nLinear Acceleration Statistics:")
    print(f"  Raw - Mean: {np.mean(data['raw_linear_acceleration']):.6f}, Std: {np.std(data['raw_linear_acceleration']):.6f}")
    print(f"  Filtered - Mean: {np.mean(data['filtered_linear_acceleration']):.6f}, Std: {np.std(data['filtered_linear_acceleration']):.6f}")

def main():
    """Main function"""
    import sys

    # Check for help
    if '--help' in sys.argv or '-h' in sys.argv:
        print("Usage: python3 plot_filter_comparison.py [options]")
        print("Options:")
        print("  -h, --help        Show this help message")
        print("  -i, --interactive Show interactive plot window")
        print("  (default)         Save plot to PNG file only")
        return

    # Check for command line arguments
    show_interactive = '--interactive' in sys.argv or '-i' in sys.argv

    if show_interactive:
        # Use interactive backend for development
        matplotlib.use('TkAgg')
        import matplotlib.pyplot as plt

    # Get the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    csv_file = os.path.join(script_dir, 'stuck_recovery_filter_data.csv')

    # Load the data
    data = load_data(csv_file)
    if data is None:
        return

    # Convert timestamp to seconds
    data = convert_timestamp_to_seconds(data)

    # Print data summary
    print_data_summary(data)

    # Create the comparison plot
    output_file = os.path.join(script_dir, 'filter_comparison_plot.png')

    if show_interactive:
        # For interactive mode, modify the plot function to show the plot
        plot_comparison_interactive(data, output_file)
    else:
        plot_comparison(data, output_file)

    print(f"\nPlot completed successfully!")
    if show_interactive:
        print("Note: Interactive plot window opened. Close it to continue.")
    else:
        print(f"Plot saved to: {output_file}")

if __name__ == "__main__":
    main()
