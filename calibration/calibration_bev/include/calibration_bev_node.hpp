#pragma once

#include "calibration_bev.hpp"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/client.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/server.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "mower_msgs/srv/calibration_bev.hpp"
#include "mower_msgs/srv/calibration_bev_result.hpp"
#include "mower_msgs/srv/camera_bev_params.hpp"
#include "mower_msgs/srv/camera_intrinsic.hpp"
#include "ob_mower_msgs/perception_localization_alg_ctrl.h"
#include "ob_mower_srvs/node_common_param_service.h"
#include "opencv2/opencv.hpp"
#include "sensor_msgs/image__struct.h"
#include "utils/heartbeat_publisher.hpp"
#include "utils/iceoryx_client_mower.hpp"
#include "utils/iceoryx_publisher_mower.hpp"
#include "utils/iceoryx_server_mower.hpp"
#include "utils/iceoryx_subscriber_mower.hpp"

#include <chrono>
#include <cmath>
#include <condition_variable>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

using namespace fescue_iox::ob_mower_srvs;

namespace fescue_iox
{

class CalibrationBevNode
{
    using get_node_param_request = GetNodeParamRequest;
    using get_node_param_response = GetNodeParamResponse;
    using set_node_param_request = SetNodeParamRequest;
    using set_node_param_response = SetNodeParamResponse;

public:
    CalibrationBevNode(const std::string &node_name);
    ~CalibrationBevNode();

private:
    void InitWorkingDirectory();
    void InitParam();
    void InitLogger();
    void InitAlgorithm();
    void InitSubscriber();
    void InitService();
    void EncodeImage(const cv::Mat &image, mower_msgs::srv::CalibrationBevImage &img);
    void InitHeartbeat();

private:
    void Deal1280x720Image(const sensor_msgs__msg__Image_iox &data);
    int ExecuteCalibrationBevRequest(mower_msgs::srv::BEVCalibParams &param, mower_msgs::srv::CalibrationBevImage &img);
    bool GetNodeParam(NodeParamData &data);
    bool SetNodeParam(const NodeParamData &data);
    bool GetCameraIntrinsicsParamFromCameraNode(RGBCameraIntrinsic &cam_intrinsics);
    bool SendCalibrationBevResultToCameraNode(BEVCalibParams &param);

private:
    // subscriber
    std::unique_ptr<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>> sub_1280x720_image_{nullptr};

    // service
    std::unique_ptr<IceoryxServerMower<get_node_param_request, get_node_param_response>> service_get_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_node_param_request, set_node_param_response>> service_set_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<mower_msgs::srv::CalibrationBevRequest, mower_msgs::srv::CalibrationBevResponse>> service_calibration_bev_{nullptr};

    std::mutex mutex_;
    std::condition_variable cv_;
    bool cali_start_{false};

    // config params
    std::string node_name_{"calibration_bev_node"};
    std::string log_dir_{""};
    std::string console_log_level_{""};
    std::string file_log_level_{""};
    std::string calibration_alg_conf_file_{};
    int compress_quality_{80};
    std::string compress_type_{"jpg"};

    // alg ptr
    std::unique_ptr<CalibrationBevAlg> calibration_bev_alg_{nullptr};
    cv::Mat cali_image_;
    std::unique_ptr<NodeHeartbeatPublisher> pub_heartbeat_{nullptr};

    static constexpr int CALIB_SUCCESS = 0;
    static constexpr int CALIB_ERR_NO_ALG = -1;
    static constexpr int CALIB_ERR_TIMEOUT = -4;
    static constexpr int CALIB_ERR_NO_IMAGE = -5;
    static constexpr int CALIB_ERR_INTRINSIC = -2;
    static constexpr int CALIB_ERR_SEND_RESULT = -3;
    static constexpr auto CALIB_WAIT_TIMEOUT = std::chrono::seconds(2);
};

} // namespace fescue_iox