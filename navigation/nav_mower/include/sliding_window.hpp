#ifndef SLIDING_WINDOW_HPP
#define SLIDING_WINDOW_HPP

#include "utils/logger.hpp"

#include <cstdint>
#include <deque>

namespace fescue_iox
{

// Movement data structure
struct MovementData
{
    uint64_t timestamp;         // Timestamp (ms)
    float angular_displacement; // Angular displacement (rad) - the angular change in this frame
    float angular_velocity;     // Angular velocity (rad/s)

    MovementData()
        : timestamp(0)
        , angular_displacement(0.0f)
        , angular_velocity(0.0f)
    {
    }
};

// Sliding window management structure
struct SlidingWindow
{
    uint64_t window_duration_ms_;   // Window duration
    float rotation_threshold_;      // Rotation threshold
    std::deque<MovementData> data_; // Data within the window
    float total_rotation_;          // Total rotation within the window
    uint64_t last_update_time_;     // Last update time

    SlidingWindow(uint64_t duration_ms, float threshold);

    // Add new data and update the window
    void AddData(const MovementData &new_data);

    // Clean up expired data
    void CleanupExpiredData(uint64_t current_time);

    // Check if stuck
    bool IsStuck() const;

    // Check if there is sufficient data
    bool HasSufficientData(uint64_t current_time) const;

    // Reset window
    void Reset();
};

} // namespace fescue_iox

#endif // SLIDING_WINDOW_HPP
