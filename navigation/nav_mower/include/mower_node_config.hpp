#pragma once

#include "utils/common_config.hpp"
#include "utils/config.hpp"

#include <string>

namespace fescue_iox
{

/**
 * @brief NavigationMowerNode configuration parameters
 * @param common_conf Common configuration parameters
 * @param chassis_type Chassis type, customer: User chassis, orbbec: Orbbec chassis
 * @param run_frequency Running frequency, unit Hz
 */
struct NavigationMowerNodeConfig
{
    CommonConfig common_conf;
    std::string chassis_type{"customer"};
    std::string mower_alg_conf_file{"conf/navigation_mower_node/mower.yaml"};
    std::string slope_ctrl_alg_conf_file{"conf/navigation_mower_node/slope_control.yaml"};

    NavigationMowerNodeConfig() = default;
    ~NavigationMowerNodeConfig() = default;
    NavigationMowerNodeConfig(const NavigationMowerNodeConfig &config) = default;
    NavigationMowerNodeConfig &operator=(const NavigationMowerNodeConfig &config);
    std::string toString() const;
};

bool operator==(const NavigationMowerNodeConfig &lhs, const NavigationMowerNodeConfig &rhs);
bool operator!=(const NavigationMowerNodeConfig &lhs, const NavigationMowerNodeConfig &rhs);

} // namespace fescue_iox
