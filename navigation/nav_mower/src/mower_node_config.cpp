#include "mower_node_config.hpp"

namespace fescue_iox
{

NavigationMowerNodeConfig &NavigationMowerNodeConfig::operator=(const NavigationMowerNodeConfig &config)
{
    if (this != &config)
    {
        common_conf = config.common_conf;
        chassis_type = config.chassis_type;
        mower_alg_conf_file = config.mower_alg_conf_file;
        slope_ctrl_alg_conf_file = config.slope_ctrl_alg_conf_file;
    }
    return *this;
}

std::string NavigationMowerNodeConfig::toString() const
{
    std::stringstream ss;
    ss << "------------------------- " << typeid(*this).name() << " -------------------------\r\n";
    ss << "  console_log_level: " << common_conf.console_log_level << "\n";
    ss << "  file_log_level: " << common_conf.file_log_level << "\n";
    ss << "  log_dir: " << common_conf.log_dir << "\n";
    ss << "  chassis_type: " << chassis_type << "\n";
    ss << "  mower_alg_conf_file: " << mower_alg_conf_file << "\n";
    ss << "  slope_ctrl_alg_conf_file: " << slope_ctrl_alg_conf_file << "\n";
    ss << "------------------------- " << typeid(*this).name() << " -------------------------";
    return ss.str();
}

bool operator==(const NavigationMowerNodeConfig &lhs, const NavigationMowerNodeConfig &rhs)
{
    return lhs.common_conf == rhs.common_conf &&
           lhs.chassis_type == rhs.chassis_type &&
           lhs.slope_ctrl_alg_conf_file == rhs.slope_ctrl_alg_conf_file &&
           lhs.mower_alg_conf_file == rhs.mower_alg_conf_file;
}

bool operator!=(const NavigationMowerNodeConfig &lhs, const NavigationMowerNodeConfig &rhs)
{
    return !(lhs == rhs);
}

template <>
bool Config<NavigationMowerNodeConfig>::LoadConfig(NavigationMowerNodeConfig &conf, const std::string &conf_file)
{
    YAML::Node node;
    try
    {
        node = YAML::LoadFile(conf_file);
    }
    catch (const YAML::BadFile &e)
    {
        std::cerr << "NavigationMowerNodeConfig load config fail (BadFile): " << e.what() << '\n';
        return false;
    }
    catch (const YAML::ParserException &e)
    {
        std::cerr << "NavigationMowerNodeConfig load config fail (ParserException): " << e.what() << '\n';
        return false;
    }
    catch (const std::exception &e)
    {
        std::cerr << "NavigationMowerNodeConfig load config fail (Unknown): " << e.what() << '\n';
        return false;
    }

    conf.common_conf.log_dir = GetYamlValue<std::string>(node, "log_dir", conf.common_conf.log_dir);
    conf.common_conf.console_log_level = GetYamlValue<std::string>(node, "console_log_level", conf.common_conf.console_log_level);
    conf.common_conf.file_log_level = GetYamlValue<std::string>(node, "file_log_level", conf.common_conf.file_log_level);
    conf.chassis_type = GetYamlValue<std::string>(node, "chassis_type", conf.chassis_type);
    conf.mower_alg_conf_file = GetYamlValue<std::string>(node, "mower_alg_conf_file", conf.mower_alg_conf_file);
    conf.slope_ctrl_alg_conf_file = GetYamlValue<std::string>(node, "slope_ctrl_alg_conf_file", conf.slope_ctrl_alg_conf_file);

    return true;
}

template <>
bool Config<NavigationMowerNodeConfig>::CreateConfig(const NavigationMowerNodeConfig &conf, const std::string &conf_file)
{
    YAML::Node node;
    node["log_dir"] = conf.common_conf.log_dir;
    node["console_log_level"] = conf.common_conf.console_log_level;
    node["file_log_level"] = conf.common_conf.file_log_level;
    node["chassis_type"] = conf.chassis_type;
    node["mower_alg_conf_file"] = conf.mower_alg_conf_file;
    node["slope_ctrl_alg_conf_file"] = conf.slope_ctrl_alg_conf_file;
    return WriteYamlFile(conf_file, node);
}

} // namespace fescue_iox
