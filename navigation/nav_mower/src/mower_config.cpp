#include "mower_config.hpp"

#include <sstream>

namespace fescue_iox
{

NavigationMowerAlgConfig &NavigationMowerAlgConfig::operator=(const NavigationMowerAlgConfig &config)
{
    if (this != &config)
    {
        is_enable_unstake_mode = config.is_enable_unstake_mode;
        unstake_distance = config.unstake_distance;
        unstake_adjust_yaw = config.unstake_adjust_yaw;
        unstake_vel_linear = config.unstake_vel_linear;
        unstake_vel_angular = config.unstake_vel_angular;

        mower_linear = config.mower_linear;
        mower_angular = config.mower_angular;
        // perception_drive_cooldown_time = config.perception_drive_cooldown_time;
        edge_mode_direction = config.edge_mode_direction;
        cross_region_adjust_yaw = config.cross_region_adjust_yaw;
        cross_region_adjust_displace = config.cross_region_adjust_displace;
        mark_distance_threshold = config.mark_distance_threshold;
        camera_2_center_dis = config.camera_2_center_dis;

        edge_perception_drive_cooldown_time_threshold = config.edge_perception_drive_cooldown_time_threshold;
        qr_detection_cooldown_time_threshold = config.qr_detection_cooldown_time_threshold;
        mark_detection_cooldown_time_threshold = config.mark_detection_cooldown_time_threshold;

        // test
        test_linear_speed = config.test_linear_speed;
        test_angular_speed = config.test_angular_speed;
        test_duration_ms = config.test_duration_ms;
        // Stuck detection data logging control
        enable_stuck_detection_data_logging = config.enable_stuck_detection_data_logging;
    }

    return *this;
}

std::string NavigationMowerAlgConfig::toString() const
{
    std::stringstream ss;
    ss << "------------------------- " << typeid(*this).name() << " -------------------------\r\n";
    ss << "  is_enable_unstake_mode: " << is_enable_unstake_mode << "\n";
    ss << "  unstake_distance: " << unstake_distance << "\n";
    ss << "  unstake_adjust_yaw: " << unstake_adjust_yaw << "\n";
    ss << "  unstake_vel_linear: " << unstake_vel_linear << "\n";
    ss << "  unstake_vel_angular: " << unstake_vel_angular << "\n";

    ss << "  mower_linear: " << mower_linear << "\n";
    ss << "  mower_angular: " << mower_angular << "\n";
    // ss << "  perception_drive_cooldown_time: " << perception_drive_cooldown_time << "\n";
    ss << "  edge_mode_direction: " << edge_mode_direction << "\n";
    ss << "  cross_region_adjust_yaw: " << cross_region_adjust_yaw << "\n";
    ss << "  cross_region_adjust_displace: " << cross_region_adjust_displace << "\n";
    ss << "  mark_distance_threshold: " << mark_distance_threshold << "\n";
    ss << "  camera_2_center_dis: " << camera_2_center_dis << "\n";

    ss << "  edge_perception_drive_cooldown_time_threshold: " << edge_perception_drive_cooldown_time_threshold << "\n";
    ss << "  qr_detection_cooldown_time_threshold: " << qr_detection_cooldown_time_threshold << "\n";
    ss << "  mark_detection_cooldown_time_threshold: " << mark_detection_cooldown_time_threshold << "\n";

    // test
    ss << "  test_linear_speed: " << test_linear_speed << "\n";
    ss << "  test_angular_speed: " << test_angular_speed << "\n";
    ss << "  test_duration_ms: " << test_duration_ms << "\n";
    ss << "  enable_stuck_detection_data_logging: " << enable_stuck_detection_data_logging << "\n";
    ss << "------------------------- " << typeid(*this).name() << " -------------------------";
    return ss.str();
}

bool operator==(const NavigationMowerAlgConfig &lhs, const NavigationMowerAlgConfig &rhs)
{
    return lhs.is_enable_unstake_mode == rhs.is_enable_unstake_mode &&
           lhs.unstake_distance == rhs.unstake_distance &&
           lhs.unstake_adjust_yaw == rhs.unstake_adjust_yaw &&
           lhs.unstake_vel_linear == rhs.unstake_vel_linear &&
           lhs.unstake_vel_angular == rhs.unstake_vel_angular &&

           lhs.mower_linear == rhs.mower_linear &&
           lhs.mower_angular == rhs.mower_angular &&
           //    lhs.perception_drive_cooldown_time == rhs.perception_drive_cooldown_time &&
           lhs.edge_mode_direction == rhs.edge_mode_direction &&
           lhs.cross_region_adjust_yaw == rhs.cross_region_adjust_yaw &&
           lhs.cross_region_adjust_displace == rhs.cross_region_adjust_displace &&
           lhs.mark_distance_threshold == rhs.mark_distance_threshold &&
           lhs.camera_2_center_dis == rhs.camera_2_center_dis &&
           lhs.edge_perception_drive_cooldown_time_threshold == rhs.edge_perception_drive_cooldown_time_threshold &&
           lhs.qr_detection_cooldown_time_threshold == rhs.qr_detection_cooldown_time_threshold &&
           lhs.mark_detection_cooldown_time_threshold == rhs.mark_detection_cooldown_time_threshold &&

           // test
           lhs.test_duration_ms == rhs.test_duration_ms &&
           lhs.test_angular_speed == rhs.test_angular_speed &&
           lhs.test_linear_speed == rhs.test_linear_speed &&
           lhs.enable_stuck_detection_data_logging == rhs.enable_stuck_detection_data_logging;
}

bool operator!=(const NavigationMowerAlgConfig &lhs, const NavigationMowerAlgConfig &rhs)
{
    return !(lhs == rhs);
}

template <>
bool Config<NavigationMowerAlgConfig>::LoadConfig(NavigationMowerAlgConfig &config, const std::string &conf_file)
{
    YAML::Node node;
    try
    {
        node = YAML::LoadFile(conf_file);
    }
    catch (const YAML::BadFile &e)
    {
        std::cerr << "NavigationMowerAlgConfig load config fail (BadFile): " << e.what() << '\n';
        return false;
    }
    catch (const YAML::ParserException &e)
    {
        std::cerr << "NavigationMowerAlgConfig load config fail (ParserException): " << e.what() << '\n';
        return false;
    }
    catch (const std::exception &e)
    {
        std::cerr << "NavigationMowerAlgConfig load config fail (Unknown): " << e.what() << '\n';
        return false;
    }

    config.is_enable_unstake_mode = GetYamlValue<bool>(node, "is_enable_unstake_mode", config.is_enable_unstake_mode);
    config.unstake_distance = GetYamlValue<float>(node, "unstake_distance", config.unstake_distance);
    config.unstake_adjust_yaw = GetYamlValue<float>(node, "unstake_adjust_yaw", config.unstake_adjust_yaw);
    config.unstake_vel_linear = GetYamlValue<float>(node, "unstake_vel_linear", config.unstake_vel_linear);
    config.unstake_vel_angular = GetYamlValue<float>(node, "unstake_vel_angular", config.unstake_vel_angular);

    config.mower_linear = GetYamlValue<float>(node, "mower_linear", config.mower_linear);
    config.mower_angular = GetYamlValue<float>(node, "mower_angular", config.mower_angular);
    // config.perception_drive_cooldown_time = GetYamlValue<int>(node, "perception_drive_cooldown_time", config.perception_drive_cooldown_time);
    config.edge_mode_direction = GetYamlValue<int>(node, "edge_mode_direction", config.edge_mode_direction);
    config.cross_region_adjust_yaw = GetYamlValue<float>(node, "cross_region_adjust_yaw", config.cross_region_adjust_yaw);
    config.cross_region_adjust_displace = GetYamlValue<float>(node, "cross_region_adjust_displace", config.cross_region_adjust_displace);
    config.mark_distance_threshold = GetYamlValue<float>(node, "mark_distance_threshold", config.mark_distance_threshold);
    config.camera_2_center_dis = GetYamlValue<float>(node, "camera_2_center_dis", config.camera_2_center_dis);

    config.edge_perception_drive_cooldown_time_threshold = GetYamlValue<int>(node, "edge_perception_drive_cooldown_time_threshold", config.edge_perception_drive_cooldown_time_threshold);
    config.qr_detection_cooldown_time_threshold = GetYamlValue<int>(node, "qr_detection_cooldown_time_threshold", config.qr_detection_cooldown_time_threshold);
    config.mark_detection_cooldown_time_threshold = GetYamlValue<int>(node, "mark_detection_cooldown_time_threshold", config.mark_detection_cooldown_time_threshold);

    // test
    config.test_linear_speed = GetYamlValue<float>(node, "test_linear_speed", config.test_linear_speed);
    config.test_angular_speed = GetYamlValue<float>(node, "test_angular_speed", config.test_angular_speed);
    config.test_duration_ms = GetYamlValue<uint64_t>(node, "test_duration_ms", config.test_duration_ms);
    // Stuck detection data logging control
    config.enable_stuck_detection_data_logging = GetYamlValue<bool>(node, "enable_stuck_detection_data_logging", config.enable_stuck_detection_data_logging);

    return true;
}

template <>
bool Config<NavigationMowerAlgConfig>::CreateConfig(const NavigationMowerAlgConfig &conf, const std::string &conf_file)
{
    YAML::Node node;

    node["is_enable_unstake_mode"] = conf.is_enable_unstake_mode;
    node["unstake_distance"] = conf.unstake_distance;
    node["unstake_adjust_yaw"] = conf.unstake_adjust_yaw;
    node["unstake_vel_linear"] = conf.unstake_vel_linear;
    node["unstake_vel_angular"] = conf.unstake_vel_angular;

    node["mower_linear"] = conf.mower_linear;
    node["mower_angular"] = conf.mower_angular;
    // node["perception_drive_cooldown_time"] = conf.perception_drive_cooldown_time;
    node["edge_mode_direction"] = conf.edge_mode_direction;

    node["cross_region_adjust_yaw"] = conf.cross_region_adjust_yaw;
    node["cross_region_adjust_displace"] = conf.cross_region_adjust_displace;
    node["mark_distance_threshold"] = conf.mark_distance_threshold;
    node["camera_2_center_dis"] = conf.camera_2_center_dis;

    node["edge_perception_drive_cooldown_time_threshold"] = conf.edge_perception_drive_cooldown_time_threshold;
    node["qr_detection_cooldown_time_threshold"] = conf.qr_detection_cooldown_time_threshold;
    node["mark_detection_cooldown_time_threshold"] = conf.mark_detection_cooldown_time_threshold;

    // test
    node["test_linear_speed"] = conf.test_linear_speed;
    node["test_angular_speed"] = conf.test_angular_speed;
    node["test_duration_ms"] = conf.test_duration_ms;
    // Stuck detection data logging control
    node["enable_stuck_detection_data_logging"] = conf.enable_stuck_detection_data_logging;

    return WriteYamlFile(conf_file, node);
}

} // namespace fescue_iox
