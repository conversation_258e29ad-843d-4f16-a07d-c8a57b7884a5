#pragma once

#include "data_type.hpp"
#include "geometry_msgs/twist__struct.h"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/client.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/server.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "mower_msgs/msg/slope_detection_result.hpp"
#include "ob_mower_msgs/nav_alg_ctrl__struct.h"
#include "ob_mower_msgs/nav_running_state__struct.h"
#include "ob_mower_msgs/nav_spiral_mower_final_result.h"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "ob_mower_srvs/nav_spiral_mower_alg_param_service__struct.h"
#include "ob_mower_srvs/nav_spiral_mower_node_param_service__struct.h"
#include "opencv2/opencv.hpp"
#include "spiral_mower.hpp"
#include "spiral_mower_config.hpp"
#include "spiral_mower_node_config.hpp"
#include "utils/heartbeat_publisher.hpp"
#include "utils/iceoryx_client_mower.hpp"
#include "utils/iceoryx_publisher_mower.hpp"
#include "utils/iceoryx_server_mower.hpp"
#include "utils/iceoryx_subscriber_mower.hpp"

#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{
class NavigationSpiralMowerNode
{
    // node param
    using iox_get_node_param_request = fescue_msgs__srv__GetNavigationSpiralMowerNodeParam_Request;
    using iox_get_node_param_response = fescue_msgs__srv__GetNavigationSpiralMowerNodeParam_Response;
    using iox_set_node_param_request = fescue_msgs__srv__SetNavigationSpiralMowerNodeParam_Request;
    using iox_set_node_param_response = fescue_msgs__srv__SetNavigationSpiralMowerNodeParam_Response;

    // alg param
    using iox_set_alg_param_request = fescue_msgs__srv__SetNavigationSpiralMowerAlgParam_Request;
    using iox_set_alg_param_response = fescue_msgs__srv__SetNavigationSpiralMowerAlgParam_Response;
    using iox_get_alg_param_request = fescue_msgs__srv__GetNavigationSpiralMowerAlgParam_Request;
    using iox_get_alg_param_response = fescue_msgs__srv__GetNavigationSpiralMowerAlgParam_Response;

public:
    NavigationSpiralMowerNode(const std::string &node_name);
    ~NavigationSpiralMowerNode();

private:
    void InitWorkingDirectory();
    void InitParam();
    void InitLogger();
    void InitPublisher();
    void InitSubscriber();
    void InitAlgorithm();
    void InitAlgorithmParam();
    void DeinitAlgorithm();
    void InitService();
    void InitHeartbeat();

private:
    void DealFusionResult(const fescue_msgs__msg__PerceptionFusionResult &msg);
    void DealNavAlgCtrlResult(const fescue_msgs__msg__NavigationAlgoCtrlData &msg);
    void DealNavRunningState(const fescue_msgs__msg__NavigationRunningStateData &data);
    void DealSlopeResult(const mower_msgs::msg::LawnmowerSlopeStatus &data);
    bool DealSetNodeParam(const fescue_msgs__msg__NavigationSpiralMowerNodeParam &data);
    bool DealGetNodeParam(fescue_msgs__msg__NavigationSpiralMowerNodeParam &data);
    bool DealSetAlgParam(const fescue_msgs__msg__NavigationSpiralMowerAlgParam &data);
    bool DealGetAlgParam(fescue_msgs__msg__NavigationSpiralMowerAlgParam &data);
    float GetBEVBlindZoneDist();
    void SpiralMowerThread();
    void PublishResult(const SpiralMowerAlgResult &result);
    void SetVelPublisherProhibit(bool prohibit)
    {
        if (spiral_mower_alg_)
        {
            spiral_mower_alg_->SetVelPublisherProhibit(prohibit);
        }
    }

private:
    // 订阅
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__PerceptionFusionResult>> sub_fusion_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>> sub_nav_alg_ctrl_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationRunningStateData>> sub_nav_running_state_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::LawnmowerSlopeStatus>> sub_slope_result_{nullptr};

    // 发布
    std::unique_ptr<IceoryxPublisherMower<ob_mower_msgs::NavSpiralMowerFinalResult>> pub_spiral_mower_result_{nullptr};

    // 服务
    std::unique_ptr<IceoryxServerMower<iox_set_node_param_request, iox_set_node_param_response>> service_set_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<iox_get_node_param_request, iox_get_node_param_response>> service_get_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<iox_set_alg_param_request, iox_set_alg_param_response>> service_set_alg_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<iox_get_alg_param_request, iox_get_alg_param_response>> service_get_alg_param_{nullptr};

    // 数据
    std::mutex fusion_result_mtx_;
    PerceptionFusionResult fusion_result_;
    std::mutex yaw_mtx_;
    float yaw_;

private:
    std::atomic_bool spiral_mower_enable_{false};
    std::thread spiral_mower_thread_;
    std::atomic_bool thread_running_{true};

    std::string node_name_{"navigation_spiral_mower_node"};
    std::string log_dir_{"/userdata/log"};
    std::string console_log_level_{"info"};
    std::string file_log_level_{"warn"};
    std::string spiral_mower_alg_conf_file_{"conf/navigation_spiral_mower_node/spiral_mower.yaml"};

    SpiralMowerAlgParam spiral_mower_alg_param_;
    std::unique_ptr<NavigationSpiralMowerAlg> spiral_mower_alg_;
    std::unique_ptr<NodeHeartbeatPublisher> pub_heartbeat_{nullptr};
};

} // namespace fescue_iox