#include "spiral_mower_node.hpp"

#include "mower_msgs/srv/camera_bev_params.hpp"
#include "mower_sdk_version.h"
#include "process_fusion.hpp"
#include "spiral_mower_node_config.hpp"
#include "utils/dir.hpp"
#include "utils/iceoryx_client.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

namespace fescue_iox
{
NavigationSpiralMowerNode::NavigationSpiralMowerNode(const std::string &node_name)
    : node_name_(node_name)
{
    InitWorkingDirectory();
    InitParam();
    InitLogger();
    InitSubscriber();
    InitPublisher();
    InitService();
    InitAlgorithm();
    InitHeartbeat();
}

NavigationSpiralMowerNode::~NavigationSpiralMowerNode()
{
    DeinitAlgorithm();
    LOG_WARN("NavigationCrossRegionNode exit!");
}

void NavigationSpiralMowerNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void NavigationSpiralMowerNode::InitParam()
{
    const std::string conf_file{"conf/navigation_spiral_mower_node/navigation_spiral_mower_node.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        LOG_INFO("NavigationSpiralMowerNode create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("NavigationSpiralMowerNode create config path failed!!!");
        }
    }
    if (!Config<NavigationSpiralMowerNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init NavigationSpiralMowerNode config parameters failed!");
    }
    NavigationSpiralMowerNodeConfig config = Config<NavigationSpiralMowerNodeConfig>::GetConfig();
    LOG_INFO("[navigation_spiral_mower_node] git tag: {}", _GIT_TAG_);
    LOG_INFO("[navigation_spiral_mower_node] git version: {}", _GIT_VERSION_);
    LOG_INFO("[navigation_spiral_mower_node] compile time: {}", _COMPILE_TIME_);
    LOG_INFO("{}", config.toString().c_str());
    log_dir_ = config.common_conf.log_dir;
    console_log_level_ = config.common_conf.console_log_level;
    file_log_level_ = config.common_conf.file_log_level;
    spiral_mower_alg_conf_file_ = config.spiral_mower_alg_conf_file;
    Config<NavigationSpiralMowerNodeConfig>::SetConfig(config, true);
    CreateDirectories(log_dir_);
}

void NavigationSpiralMowerNode::InitAlgorithmParam()
{
    std::string conf_path = GetDirectoryPath(spiral_mower_alg_conf_file_);
    if (!conf_path.empty())
    {
        LOG_INFO("Navigation spiral mower algo create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("Navigation spiral mower algo create config path failed!!!");
        }
    }
    if (!Config<NavigationSpiralMowerAlgConfig>::Init(spiral_mower_alg_conf_file_))
    {
        LOG_WARN("Init Navigation spiral mower algo config parameters failed!");
    }
    NavigationSpiralMowerAlgConfig config = Config<NavigationSpiralMowerAlgConfig>::GetConfig();
    LOG_INFO("{}", config.toString().c_str());
    spiral_mower_alg_param_.spiral_rotate_angle = config.spiral_rotate_angle;
    spiral_mower_alg_param_.forward_speed = config.forward_speed;
    spiral_mower_alg_param_.trun_corner_speed_min = config.trun_corner_speed_min;
    spiral_mower_alg_param_.angular_accuracy = config.angular_accuracy;
    Config<NavigationSpiralMowerAlgConfig>::SetConfig(config, true);
}

void NavigationSpiralMowerNode::InitLogger()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void NavigationSpiralMowerNode::InitHeartbeat()
{
    pub_heartbeat_ = std::make_unique<NodeHeartbeatPublisher>();
    pub_heartbeat_->start();
}

void NavigationSpiralMowerNode::InitPublisher()
{
    pub_spiral_mower_result_ = std::make_unique<IceoryxPublisherMower<ob_mower_msgs::NavSpiralMowerFinalResult>>(
        "navigation_spiral_mower_final_result");
}

void NavigationSpiralMowerNode::InitSubscriber()
{
    sub_slope_result_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::LawnmowerSlopeStatus>>(
        "localization_slope_detection_result", 1,
        [this](const mower_msgs::msg::LawnmowerSlopeStatus &data, const std::string &event) {
            (void)event;
            DealSlopeResult(data);
        });
    sub_fusion_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__PerceptionFusionResult>>(
        "fusion_result", 1,
        [this](const fescue_msgs__msg__PerceptionFusionResult &data, const std::string &event) {
            (void)event;
            DealFusionResult(data);
        });
    sub_nav_alg_ctrl_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>>(
        "navigation_nav_alg_ctrl", 10,
        [this](const fescue_msgs__msg__NavigationAlgoCtrlData &data, const std::string &event) {
            (void)event;
            DealNavAlgCtrlResult(data);
        });
    sub_nav_running_state_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavigationRunningStateData>>(
        "navigation_running_state", 10,
        [this](const fescue_msgs__msg__NavigationRunningStateData &data, const std::string &event) {
            (void)event;
            DealNavRunningState(data);
        });
}

void NavigationSpiralMowerNode::InitAlgorithm()
{
    InitAlgorithmParam();
    float dead_zone = GetBEVBlindZoneDist();
    spiral_mower_alg_ = std::make_unique<NavigationSpiralMowerAlg>(spiral_mower_alg_param_, dead_zone);
    thread_running_.store(true);
    spiral_mower_thread_ = std::thread(&NavigationSpiralMowerNode::SpiralMowerThread, this);
}

void NavigationSpiralMowerNode::DeinitAlgorithm()
{
    if (spiral_mower_alg_)
    {
        spiral_mower_alg_->ProhibitVelPublisher();
    }
    thread_running_.store(false);
    if (spiral_mower_thread_.joinable())
    {
        spiral_mower_thread_.join();
    }
}

void NavigationSpiralMowerNode::InitService()
{
    service_set_node_param_ = std::make_unique<IceoryxServerMower<iox_set_node_param_request, iox_set_node_param_response>>(
        "set_navigation_spiral_node_param_request", 10U,
        [this](const iox_set_node_param_request &request, iox_set_node_param_response &response) {
            response.success = DealSetNodeParam(request.data);
            LOG_INFO("Set navigation spiral mower node param execute {}", response.success);
        });
    service_get_node_param_ = std::make_unique<IceoryxServerMower<iox_get_node_param_request, iox_get_node_param_response>>(
        "get_navigation_spiral_node_param_request", 10U,
        [this](const iox_get_node_param_request &request, iox_get_node_param_response &response) {
            (void)request;
            response.success = DealGetNodeParam(response.data);
            LOG_INFO("Get navigation spiral mower node param execute {}", response.success);
        });
    service_set_alg_param_ = std::make_unique<IceoryxServerMower<iox_set_alg_param_request, iox_set_alg_param_response>>(
        "set_navigation_spiral_alg_param_request", 10U,
        [this](const iox_set_alg_param_request &request, iox_set_alg_param_response &response) {
            response.success = DealSetAlgParam(request.data);
            LOG_INFO("Set navigation spiral mower alg param execute {}", response.success);
        });
    service_get_alg_param_ = std::make_unique<IceoryxServerMower<iox_get_alg_param_request, iox_get_alg_param_response>>(
        "get_navigation_spiral_alg_param_request", 10U,
        [this](const iox_get_alg_param_request &request, iox_get_alg_param_response &response) {
            (void)request;
            response.success = DealGetAlgParam(response.data);
            LOG_INFO("Get navigation spiral mower alg param execute {}", response.success);
        });
}

bool NavigationSpiralMowerNode::DealSetNodeParam(const fescue_msgs__msg__NavigationSpiralMowerNodeParam &data)
{
    console_log_level_ = std::string(data.console_log_level.c_str());
    file_log_level_ = std::string(data.file_log_level.c_str());
    InitLogger();
    NavigationSpiralMowerNodeConfig config = Config<NavigationSpiralMowerNodeConfig>::GetConfig();
    config.common_conf.console_log_level = console_log_level_;
    config.common_conf.file_log_level = file_log_level_;
    Config<NavigationSpiralMowerNodeConfig>::SetConfig(config, true);
    LOG_INFO("New NavigationSpiralMowerNode params: {}", config.toString().c_str());
    return true;
}

bool NavigationSpiralMowerNode::DealGetNodeParam(fescue_msgs__msg__NavigationSpiralMowerNodeParam &data)
{
    NavigationSpiralMowerNodeConfig config = Config<NavigationSpiralMowerNodeConfig>::GetConfig();
    data.console_log_level.unsafe_assign(config.common_conf.console_log_level.c_str());
    data.file_log_level.unsafe_assign(config.common_conf.file_log_level.c_str());
    return true;
}

bool NavigationSpiralMowerNode::DealSetAlgParam(const fescue_msgs__msg__NavigationSpiralMowerAlgParam &data)
{
    if (!spiral_mower_alg_)
    {
        return false;
    }

    NavigationSpiralMowerAlgConfig config = Config<NavigationSpiralMowerAlgConfig>::GetConfig();
    config.spiral_rotate_angle = data.spiral_rotate_angle;
    config.forward_speed = data.forward_speed;
    config.trun_corner_speed_min = data.trun_corner_speed_min;
    config.angular_accuracy = data.angular_accuracy;
    LOG_INFO("New NavigationSpiralMowerAlg params: {}", config.toString().c_str());
    Config<NavigationSpiralMowerAlgConfig>::SetConfig(config, true);

    SpiralMowerAlgParam param;
    param.spiral_rotate_angle = data.spiral_rotate_angle;
    param.angular_accuracy = data.angular_accuracy;
    param.forward_speed = data.forward_speed;
    param.trun_corner_speed_min = data.trun_corner_speed_min;
    return spiral_mower_alg_->SetSpiralMowerAlgParam(param);
}

bool NavigationSpiralMowerNode::DealGetAlgParam(fescue_msgs__msg__NavigationSpiralMowerAlgParam &data)
{
    if (!spiral_mower_alg_)
    {
        return false;
    }

    SpiralMowerAlgParam param = spiral_mower_alg_->GetSpiralMowerAlgParam();
    data.spiral_rotate_angle = param.spiral_rotate_angle;
    data.angular_accuracy = param.angular_accuracy;
    data.forward_speed = param.forward_speed;
    data.trun_corner_speed_min = param.trun_corner_speed_min;
    return true;
}

void NavigationSpiralMowerNode::DealFusionResult(const fescue_msgs__msg__PerceptionFusionResult &msg)
{
    std::lock_guard<std::mutex> lock(fusion_result_mtx_);
    GetFusionOccupancyResult(msg, fusion_result_.occupancy_grid);
}

void NavigationSpiralMowerNode::DealNavAlgCtrlResult(const fescue_msgs__msg__NavigationAlgoCtrlData &msg)
{
    for (size_t i = 0; i < msg.data.size(); i++)
    {
        if (msg.data[i].type == FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_SPIRAL_MOWER)
        {
            switch (msg.data[i].state)
            {
            case FESCUE_MSGS_ENUM_NAV_ALGO_STATE_DISABLE:
                spiral_mower_enable_.store(false);
                SetVelPublisherProhibit(true);
                break;
            case FESCUE_MSGS_ENUM_NAV_ALGO_STATE_ENABLE:
                spiral_mower_enable_.store(true);
                SetVelPublisherProhibit(false);
                break;
            default:
                break;
            }
            break;
        }
    }
}

void NavigationSpiralMowerNode::DealNavRunningState(const fescue_msgs__msg__NavigationRunningStateData &data)
{
    if (spiral_mower_alg_)
    {
        spiral_mower_alg_->SetAlgoRunningState(static_cast<MowerRunningState>(data.state));
    }
}

void NavigationSpiralMowerNode::DealSlopeResult(const mower_msgs::msg::LawnmowerSlopeStatus &data)
{
    LOG_DEBUG("timestamp: {} yaw: {:.2f}", data.timestamp_ms, data.yaw);
    std::lock_guard<std::mutex> lock(yaw_mtx_);
    yaw_ = data.yaw;
}

float NavigationSpiralMowerNode::GetBEVBlindZoneDist()
{
    float bev_zone_dist = 0.15; // defaut value is 0.15
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::CameraBevParamsRequest,
                                                      mower_msgs::srv::CameraBevParamsResponse>>("get_union_rgb_camera_bev_params");

    auto response_handler = [](const mower_msgs::srv::CameraBevParamsResponse &response_receive,
                               mower_msgs::srv::CameraBevParamsResponse &response_output) -> bool {
        response_output.bev_params.scotoma_distance_ = response_receive.bev_params.scotoma_distance_;
        response_output.success = response_receive.success;
        return response_output.success;
    };

    mower_msgs::srv::CameraBevParamsRequest request_input;
    mower_msgs::srv::CameraBevParamsResponse response_output;
    if (client->SendRequest(request_input, response_output, nullptr, response_handler))
    {
        bev_zone_dist = response_output.bev_params.scotoma_distance_;
        LOG_INFO("Spiral mower alg get bev zone dist success, use {:.2f}!", bev_zone_dist);
    }
    else
    {
        LOG_WARN("Spiral mower alg get bev zone dist fail!, use default {:.2f}", bev_zone_dist);
    }
    return bev_zone_dist;
}

void NavigationSpiralMowerNode::SpiralMowerThread()
{
    PerceptionFusionResult fusion_result;
    float yaw = 0.0;

    while (thread_running_.load())
    {
        if (spiral_mower_enable_.load())
        {
            {
                std::scoped_lock lock(fusion_result_mtx_, yaw_mtx_);
                fusion_result = fusion_result_;
                yaw = yaw_;
            }
            if (spiral_mower_alg_)
            {
                auto result = spiral_mower_alg_->Run(fusion_result, yaw);
                if (result.spiral_completed)
                {
                    PublishResult(result);
                    spiral_mower_enable_.store(false);
                }
            }
        }
        else
        {
            LOG_DEBUG_THROTTLE(2000, "NavigationSpiralMowerAlg is disable!");
            if (spiral_mower_alg_)
            {
                spiral_mower_alg_->ResetSpiralMowerFlags();
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }
}

void NavigationSpiralMowerNode::PublishResult(const SpiralMowerAlgResult &result)
{
    if (pub_spiral_mower_result_)
    {
        ob_mower_msgs::NavSpiralMowerFinalResult final_result;
        final_result.timestamp = GetTimestampMs();
        final_result.completed = result.spiral_completed;
        final_result.success = result.spiral_result;
        pub_spiral_mower_result_->publishCopyOf(final_result);
    }
}

} // namespace fescue_iox
