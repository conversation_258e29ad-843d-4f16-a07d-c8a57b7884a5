#ifndef NAVIGATION_EDGE_FOLLOW_NODE_HPP
#define NAVIGATION_EDGE_FOLLOW_NODE_HPP

#include "data_type.hpp"
#include "edge_follow.hpp"
#include "edge_follow_config.hpp"
#include "geometry_msgs/twist__struct.h"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/client.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/server.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "mower_msgs/msg/slope_detection_result.hpp"
#include "mower_msgs/msg/soc_imu.hpp"
#include "mower_msgs/srv/camera_bev_params.hpp"
#include "ob_mower_msgs/nav_alg_ctrl__struct.h"
#include "ob_mower_msgs/nav_edge_follow_data__struct.h"
#include "ob_mower_msgs/nav_escape_result.h"
#include "ob_mower_msgs/nav_running_state__struct.h"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "ob_mower_srvs/nav_edge_follow_alg_param_service.h"
#include "ob_mower_srvs/nav_edge_follow_node_param_service__struct.h"
#include "opencv2/opencv.hpp"
#include "path_track.hpp"
#include "slope_control.hpp"
#include "thirdparty/math/eigen3/Eigen/Dense"
#include "thirdparty/ob_mower_fusion/include/common_defs.h"
#include "utils/heartbeat_publisher.hpp"
#include "utils/iceoryx_client_mower.hpp"
#include "utils/iceoryx_publisher_mower.hpp"
#include "utils/iceoryx_server_mower.hpp"
#include "utils/iceoryx_subscriber_mower.hpp"
#include "velocity_publisher.hpp"
#include "velocity_smooth.hpp"

#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{

class NavigationEdgeFollowNode
{
    using get_node_param_request = fescue_msgs__srv__GetNavigationEdgeFollowNodeParam_Request;
    using get_node_param_response = fescue_msgs__srv__GetNavigationEdgeFollowNodeParam_Response;
    using set_node_param_request = fescue_msgs__srv__SetNavigationEdgeFollowNodeParam_Request;
    using set_node_param_response = fescue_msgs__srv__SetNavigationEdgeFollowNodeParam_Response;

    using get_alg_param_request = ob_mower_srvs::GetEdgeFollowAlgParamRequest;
    using get_alg_param_response = ob_mower_srvs::GetEdgeFollowAlgParamResponse;
    using set_alg_param_request = ob_mower_srvs::SetEdgeFollowAlgParamRequest;
    using set_alg_param_response = ob_mower_srvs::SetEdgeFollowAlgParamResponse;

public:
    NavigationEdgeFollowNode(const std::string &node_name);
    ~NavigationEdgeFollowNode();

private:
    void InitWorkingDirectory();
    void InitParam();
    void InitAlgorithmParam();
    void ConfigParamToAlgorithmParam(const NavigationEdgeFollowAlgConfig &config, EdgeFollowAlgParam &param);
    void InitAlgorithm();
    void DeinitAlgorithm();
    void InitLogger();
    void InitHeartbeat();
    void InitSubscriber();
    void InitPublisher();
    void InitService();
    void EdgeFollowThread();

private:
    void DealOdomTwistResult(const geometry_msgs__msg__Twist_iox &msg);
    void DealNavAlgCtrlResult(const fescue_msgs__msg__NavigationAlgoCtrlData &msg);
    void DealNavRunningState(const fescue_msgs__msg__NavigationRunningStateData &data);
    void DealPerceptionFusionResult(const fescue_msgs__msg__PerceptionFusionResult &msg);
    void DealEscapeResult(const ob_mower_msgs::NavEscapeResult &data);
    void DealSocImu(const mower_msgs::msg::SocImu &data);
    bool GetEdgeFollowNodeParam(fescue_msgs__msg__NavigationEdgeFollowNodeParam &data);
    bool SetEdgeFollowNodeParam(const fescue_msgs__msg__NavigationEdgeFollowNodeParam &data);
    bool DealGetAlgParam(ob_mower_srvs::EdgeFollowAlgParam &data);
    bool DealSetAlgParam(const ob_mower_srvs::EdgeFollowAlgParam &data);
    void DealSlopeDetectionResult(const mower_msgs::msg::LawnmowerSlopeStatus &data);
    float GetBEVBlindZoneDist();

private:
    // 订阅
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__PerceptionFusionResult>> sub_fusion_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<geometry_msgs__msg__Twist_iox>> sub_odom_twist_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>> sub_nav_alg_ctrl_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationRunningStateData>> sub_nav_running_state_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<ob_mower_msgs::NavEscapeResult>> sub_escape_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::SocImu>> sub_soc_imu_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::LawnmowerSlopeStatus>> sub_loc_slope_detection_result_{nullptr};
    // services
    std::unique_ptr<IceoryxServerMower<get_node_param_request, get_node_param_response>>
        service_get_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_node_param_request, set_node_param_response>> service_set_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<get_alg_param_request, get_alg_param_response>> service_get_alg_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_alg_param_request, set_alg_param_response>> service_set_alg_param_{nullptr};

private:
    std::atomic_bool edge_follow_enable_{false};

    std::mutex fusion_result_mutex_;
    PerceptionFusionResult fusion_result_;
    std::mutex odom_result_mutex_;
    OdomResult odom_result_;
    std::mutex imu_mtx_;
    std::mutex escape_result_mutex_;
    std::mutex slope_detection_result_mtx_;
    ob_mower_msgs::NavEscapeResult escape_result_;
    mower_msgs::msg::LawnmowerSlopeStatus slope_detection_result_;
    ImuData imu_data_;
    std::vector<ImuData> imu_datas_;
    std::thread edge_follow_thread_;
    std::atomic_bool thread_running_{true};
    bool bias_complete_{false};
    Eigen::Vector3d bias_{Eigen::Vector3d::Zero()};
    // Parameters
    std::string node_name_{"navigation_edge_follow_node"};
    std::string log_dir_{"/userdata/log"};
    std::string console_log_level_{"info"};
    std::string file_log_level_{"warn"};
    std::string edge_follow_alg_conf_file_{"conf/navigation_edge_follow_node/edge_follow.yaml"};
    EdgeFollowAlgParam edge_follow_alg_param_;
    std::unique_ptr<NavigationEdgeFollowAlg> edge_follow_alg_{nullptr};
    std::unique_ptr<NodeHeartbeatPublisher> pub_heartbeat_{nullptr};
};

} // namespace fescue_iox

#endif
