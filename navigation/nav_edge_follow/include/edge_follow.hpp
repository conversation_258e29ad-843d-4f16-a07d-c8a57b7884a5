#ifndef NAVIGATION_EDGE_FOLLOW_ALG_HPP
#define NAVIGATION_EDGE_FOLLOW_ALG_HPP

#include "data_type.hpp"
#include "geometry_msgs/twist__struct.h"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/client.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/server.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "mower_msgs/msg/slope_detection_result.hpp"
#include "mower_msgs/msg/soc_exception.hpp"
#include "ob_mower_msgs/nav_alg_ctrl__struct.h"
#include "ob_mower_msgs/nav_escape_result.h"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "opencv2/opencv.hpp"
#include "path_track.hpp"
#include "predict_trajectory.hpp"
#include "velocity_publisher.hpp"
#include "velocity_smooth.hpp"

#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{

struct EdgeFollowAlgParam
{
    std::string edge_follow_alg_type{"predict_trajectory"};
    std::string flAction_type{"straight_right"};
    bool vel_smooth_enable{false};

    float edge_follow_linear{0.2};
    float edge_follow_angular{0.3};

    int dividing_line_left{200};  // 198
    int dividing_line_right{280}; // 300

    int max_obstacle_avoid_duration_times{500}; // 避障走直线持续发送速度次数
    int max_turn_right_times{500};              // 右转次数最大次数
    float min_vel_angular{0.05};                // 最小有效角速度
    int max_zero_vel_times{10};
    float dead_zone{0.15};
    // 定义感兴趣区域(ROI)，以矩形的左上角点（x, y）和宽度、高度
    int x{2};        // 左上角点的x坐标
    int y{160};      // 左上角点的y坐标
    int width{396};  // 矩形区域的宽度
    int height{196}; // 矩形区域的高度

    // Ocuupancy grid parameters
    float acc{0.005}; // 0.0125,0.015
    float slow_acc{0.015};
    float kp_r{0.125};
    float kp_l{0.125};      // 0.125
    float wheel_base{0.35}; // 0.4
    int left_line_x{11};
    int right_line_x{36};     // 28//32
    int car_left_line_x{11};  // 11
    int car_right_line_x{28}; // 28
    float resolution{0.025};
    float look_ahead_dis{0.5};
    float danger_dis{0.3}; // 0.3
    int grid_width{40};
    int grid_height{36};
    float v_max{0.3};
    int max_fl_time{3}; // 2000
    float fl_back_right_target_dis{-0.15};
    float fl_turn_right_target{0.61};  // 1.4//0.53
    float fl_forward_target_dis{0.58}; // 0.48//0.58
    float fl_forward_r_target_dis{0.30};
    float fl_go_straight_linear{0.2};
    float fl_go_straight_angluar{0.3};
    float fl_turn_r_linear{0.05};
    float fl_turn_r_angular{0.2};
    float fl_spot_turn_r_angular{0.3};
    float fl_turn_right_target_new{1.05};
    float fl_turn_r_angular_new{0.5};
    float fl_tuning_linear{0.15};
    float fl_tuning_angular{0.2};
    int max_ao_turn_l_spot_num{2000};
    int max_ao_turn_l_num{2000};
    float ao_turn_l_spot_angular{0.5};
    float ao_turn_l_wheel_r{0.05};
    float ao_turn_l_wheel_l{0.2};
    int max_ao_time{20000};

    // 其它算法配置文件
    std::string predict_trajectory_conf{"conf/navigation_common/navigation_predict_trajectory.yaml"};
    std::string velocity_smooth_conf{"conf/navigation_common/navigation_velocity_smooth.yaml"};
    std::string path_track_conf{"conf/navigation_common/navigation_path_track.yaml"};
};

struct EdgeFollowAlgResult
{
    float linear{0};
    float angular{0};
    EdgeFollowAlgResult() = default;
    EdgeFollowAlgResult(float linear, float angular)
    {
        this->linear = linear;
        this->angular = angular;
    }
};

class NavigationEdgeFollowAlg
{
    using iox_exception_publisher = iox::popo::Publisher<mower_msgs::msg::SocException>;
    using iox_nav_alg_ctrl_publisher = iox::popo::Publisher<fescue_msgs__msg__NavigationAlgoCtrlData>;

public:
    NavigationEdgeFollowAlg(const EdgeFollowAlgParam &param);
    ~NavigationEdgeFollowAlg();
    // void DoEdgeFollow(const PerceptionFusionResult &fusion_result, const OdomResult &odom_result, const ob_mower_msgs::NavEscapeResult &escape_result);
    void DoEdgeFollow(const PerceptionFusionResult &fusion_result,
                      const OdomResult &odom_result, const ob_mower_msgs::NavEscapeResult &escape_result,
                      const mower_msgs::msg::LawnmowerSlopeStatus &slope_detection_result);
    void ResetEdgeFollowFlags();
    bool SetAlgParam(const EdgeFollowAlgParam &param);
    bool GetAlgParam(EdgeFollowAlgParam &param) const;
    void SetAlgoRunningState(MowerRunningState state);
    void SetVelPublisherProhibit(bool prohibit)
    {
        if (vel_publisher_)
        {
            vel_publisher_->SetProhibitFlag(prohibit);
        }
    }
    const char *GetVersion();

private:
    enum STATE
    {
        FIND_LINE,
        GO_STRAIGHT,
        AVOID_OBS,
        ESCAPE,
    } exec_state_ = STATE::FIND_LINE;
    void InitAlgorithmParam();
    void InitPredictTrajectoryAlgParam();
    void InitVelocitySmoothAlgParam();
    void PredictTrajectoryConfigParamToPredictTrajectoryAlgParam(const NavigationPredictTrajectoryAlgConfig &config, PredictTrajectoryAlgParam &param);
    void InitAlgorithm();
    void SetEdgeFollowFlags(bool status);
    void PublishVelocity(float linear_vel, float angular_vel);
    void PauseVelocity();
    void ResumeVelocity();

    VelocitySmoothAlgResult DealVelocitySmooth(float linear, float angular);
    PredictTrajectoryAlgResult DealPredictTrajectory(cv::Mat &imageROI, const OdomResult &odom_result);
    PredictTrajectoryAlgResult DealPredictTrajectory(cv::Mat &imageROI);
    EdgeFollowAlgResult DealEdgeFollowWithPredictTrajectory(const PerceptionFusionResult &fusion_result, const OdomResult &odom_result);
    EdgeFollowAlgResult DealEdgeFollowWithPathTrack(const PerceptionFusionResult &fusion_result, const OdomResult &odom_result);
    EdgeFollowAlgResult DealEdgeFollowWithBEV(const PerceptionFusionResult &fusion_result, const OdomResult &odom_result, const ob_mower_msgs::NavEscapeResult &escape_result);
    EdgeFollowAlgResult DealEdgeFollowWithBEVInflate(const PerceptionFusionResult &fusion_result, const OdomResult &odom_result, const ob_mower_msgs::NavEscapeResult &escape_result);
    std::vector<cv::Point> GetEdgeFollowPath(const BoundaryResult &boundary_result);
    int DealEdgeFollowTrackPath(const std::vector<cv::Point> &path, float pixels_to_meters, const Point &robot_pose, PathTrackAlgResult &result);
    /*fsm occupancy grid follow*/
    void CheckGrid(const std::vector<std::vector<uint8_t>> grid); // depend on grid change exec_state
    void CheckInflateGrid(const std::vector<std::vector<uint8_t>> grid, const std::vector<std::vector<uint8_t>> original_grid);
    void CheckLoop(const ob_mower_msgs::NavEscapeResult escape_result);
    void ResetObsFlags();
    void FlAction_sr(float &linear, float &angular);
    void FlAction_br(float &linear, float &angular);
    void FlAction(float &linear, float &angular);
    void GsAction(float &linear, float &angular);
    void AoAction(float &linear, float &angular);
    void EsAction(float &linear, float &angular);
    void ChangeState(STATE new_state, string pos_call);
    void InitPublisher();
    void PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value);
    void PublishNavAlgCtrl(bool stop);
    std::vector<std::vector<uint8_t>> InflateObstacles(const std::vector<std::vector<uchar>> &grid, int inflation_radius = 1);

private:
    std::unique_ptr<iox_exception_publisher> pub_exception_;
    std::unique_ptr<iox_nav_alg_ctrl_publisher> pub_nav_alg_ctrl_{nullptr};
    // 运行状态量
    MowerRunningState mower_running_state_{MowerRunningState::STOP}; // 0-未开始，1-运行中，2-暂停

    bool is_detect_boundary_{false}; // 是否已经检测到边界
    bool is_vel_smooth_velocity_valid_{false};
    bool is_turn_right_times_greater_threshold_{false}; // 绕障碍物右转是否达到
    int skip_bev_image_nums_{0};
    int obstacle_avoid_duration_times_{0}; // 避障走直线持续次数
    int all_grass_go_ahead_times_{0};
    int turn_right_times_{0};     // 右转计数
    int new_turn_right_times_{0}; // 右转计数
    int zero_vel_times_{0};
    std::atomic_bool is_start_edge_follow_{false};

    // 沿边参数
    float edge_follow_linear_{0.2};
    float edge_follow_angular_{0.3};
    int dividing_line_left_{198};
    int dividing_line_right_{300};
    int max_obstacle_avoid_duration_times_{500}; // 避障走直线持续发送速度次数
    int max_turn_right_times_{500};              // 右转次数最大次数
    float min_vel_angular_{0.05};                // 最小有效角速度
    int max_zero_vel_times_{10};
    int max_all_grass_go_ahead_times_{500};
    // 定义感兴趣区域(ROI)，以矩形的左上角点（x, y）和宽度、高度
    int x_{2};        // 左上角点的x坐标
    int y_{160};      // 左上角点的y坐标
    int width_{396};  // 矩形区域的宽度
    int height_{196}; // 矩形区域的高度
    // double wheel_base_{0.4};
    float last_yaw_{0};
    float cur_yaw_{0};
    double wheel_v_{0.2};
    bool clockwise_{false};
    int last_state_ = -1; // 0:go head,1:turn left,2:turn right
    bool record_yaw_complete_{false};
    bool start_obs_avoid_{false};
    bool finsh_obs_avoid_{false};
    bool start_find_line_{false};
    bool finish_find_line_{false};
    bool is_go_ahead_{false};
    bool is_obs_avoid_{false};
    bool is_find_line_{false};
    bool is_turn_right_{false};
    float find_line_go_straight_{0};
    int all_grass_time{0};
    std::chrono::high_resolution_clock::time_point find_line_t1_;
    std::chrono::high_resolution_clock::time_point find_line_t2_;
    std::chrono::high_resolution_clock::time_point find_line_t3_;
    std::chrono::high_resolution_clock::time_point find_line_t2_time_out_;
    std::chrono::high_resolution_clock::time_point find_line_t3_time_out_;
    std::chrono::high_resolution_clock::time_point escape_rotation_t_;
    std::chrono::high_resolution_clock::time_point escape_rotation_time_out_;
    std::chrono::high_resolution_clock::time_point stop_escape_t_;
    double fl_time_out_{30.0};
    double es_time_out_{30.0};
    float last_linear_{0};
    int turn_right_bev_ = {0};
    float dead_zone_{0.15};
    float acc_{0.005};      // 0.0125,0.015
    float slow_acc_{0.032}; // 0.015
    float kp_r_{0.125};
    float kp_l_{0.125}; // 0.125
    float kp_dis_{1.0};
    float k_w_{-1.0};

    std::string edge_follow_alg_type_{"predict_trajectory"};
    bool vel_smooth_enable_{false};
    std::string predict_trajectory_conf_{""};
    std::string velocity_smooth_conf_{""};
    std::string path_track_conf_{""};

    PredictTrajectoryAlgParam predict_trajectory_alg_param_;
    std::unique_ptr<PredictTrajectory> predict_trajectory_{nullptr};
    VelocitySmoothAlgParam velocity_smooth_alg_param_;
    std::unique_ptr<NavigationVelocitySmoothAlg> velocity_smooth_alg_{nullptr};
    PathTrackAlgParam path_track_alg_param_;
    std::unique_ptr<NavigationPathTrackAlg> path_track_alg_{nullptr};

    std::unique_ptr<VelocityPublisher> vel_publisher_{nullptr};

    /*fsm occupancy grid follow*/
    std::string flAction_type_{"straight_right"};
    float wheel_base_{0.35};
    int left_line_x_{11};
    int right_line_x_{36};       // 28//32
    int right_line_x_flate_{30}; // 30
    int car_left_line_x_{11};    // 11
    int car_right_line_x_{28};   // 28
    float resolution_{0.025};
    float inflate_m_{0.15}; // 20cm
    float look_ahead_dis_{0.5};
    float danger_dis_{0.3}; // 0.3
    int grid_width_{40};
    int grid_height_{36};
    float v_max_{0.3}; // if forward_obs_dis < 0.3 then v = 0 ; else v = 0.3/0.85*forward_obs_dis-0.3*0.3/0.85;
    float last_line_{0};

    bool obs_in_danger_{false};
    bool obs_in_forward_{false};
    bool obs_in_right_{false};
    bool obs_in_interested_{false};
    bool obs_in_left_{false};
    bool obs_in_right_original_{false};

    int forward_obs_x_{0};
    int forward_obs_y_{0};
    int forward_grid_num_{0};
    int forward_obs_num_{0};

    int danger_obs_x_{0};
    int danger_obs_y_{0};
    int danger_grid_num_{0};
    int danger_obs_num_{0};
    int danger_partial_obs_x_{0};
    int danger_partial_obs_y_{0};
    int danger_partial_grid_num_{0};
    int danger_partial_obs_num_{0};

    int right_obs_x_{0};
    int right_obs_y_{0};
    int right_grid_num_{0};
    int right_obs_num_{0};
    int right_grid_num_original_{0};
    int right_obs_num_original_{0};

    int left_obs_x_{0};
    int left_obs_y_{0};
    int left_grid_num_{0};
    int left_obs_num_{0};

    int inte_obs_x_{0};
    int inte_obs_y_{0};
    int inte_grid_num_{0};
    int inte_obs_num_{0};

    float max_fl_v_{0.3};
    int fl_go_ahead_num_{0};
    int max_fl_go_ahead_num_{77143};
    int fl_turn_r_spot_num_{0};
    int max_fl_turn_r_spot_num_{500};
    int fl_turn_r_num_{0};
    int max_fl_turn_r_num_{500};
    float fl_turn_r_spot_angular_{0.2};
    float fl_turn_r_wheel_l_{0.05};
    float fl_turn_r_wheel_r_{0.2};
    int fl_time_{0};
    int max_fl_time_{3}; // 20000
    bool fl_tuning_complete_{false};
    bool start_fl_tuning_{false};

    float max_tuning_straight_dis_{0.4};
    bool fl_slow_complete_{false};
    bool fl_tuning_back_complete_{false};
    float fl_back_right_target_dis_{-0.15};
    float fl_back_right_dis_{0};
    float fl_turn_right_target_{0.61}; // 1.4//0.53
    float fl_turn_right_target_new_{1.05};
    float fl_tuning_dis_{0};
    float fl_tuning_angle_{0};
    float fl_tuning_target_{0.3};
    float fl_tuning_linear_{0.15};
    float fl_tuning_angular_{0.2}; // 0.5
    float fl_tuning_back_linear_{0.1};
    float fl_tuning_back_dis_target_{0.1}; // rad
    float fl_tuning_angle_target_{0.53};

    float fl_theta_{0};
    bool start_fl_back_right_{false};
    bool fl_back_right_complete_{false};
    float last_angular_{0};
    float fl_forward_target_dis_{0.58}; // 0.48//0.53
    float fl_forward_target_dis_new_{0.48};
    float fl_forward_r_target_dis_{0.30};
    float fl_go_straight_linear_{0.2};
    float fl_go_straight_angluar_{0.3};
    float fl_turn_r_linear_{0.05};
    float fl_turn_r_angular_{0.2};
    float fl_turn_r_angular_new_{0.5};
    float fl_spot_turn_r_angular_{0.3};
    int ao_turn_l_spot_num_{0};
    int max_ao_turn_l_spot_num_{2000};
    int ao_turn_l_num_{0};
    int max_ao_turn_l_num_{2000};
    float ao_turn_l_spot_angular_{0.5};
    float ao_turn_l_wheel_r_{0.05};
    float ao_turn_l_wheel_l_{0.2};
    int ao_time_{0};
    int max_ao_time_{20000};
    bool ao_slow_complete_{false};
    int loop_time_{0};
    int max_loop_time_{3};
    uint64_t last_esacpe_time_{0};
    uint64_t escape_delta_time_{300};
    bool is_escape_{false};
    bool escape_rotation_complete_{false};
    bool escape_rotation_start_{false};
    float escape_rotation_angle_{0};
    float escape_rotation_target_{1.57};
    bool stop_escape_{false};
    bool mark_escape_time_{false};
    double escape_cool_time_{60.0};
    float right_obs_num_original_threshold_{0.5};
    //
    bool wait_for_bias_{false};
    bool is_start_wait_{false};
    std::chrono::high_resolution_clock::time_point start_wait_t_;
    double wait_for_bias_time_{2.0};
};

} // namespace fescue_iox

#endif
