#include "recharge_node.hpp"

#include "mower_sdk_version.h"
#include "recharge_node_config.hpp"
#include "utils/dir.hpp"
#include "utils/file.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

namespace fescue_iox
{

NavigationRechargeNode::NavigationRechargeNode(const std::string &node_name)
    : node_name_(node_name)
{
    last_mcu_exception_time_ = std::chrono::steady_clock::now();

    InitWorkingDirectory();
    InitParam();
    InitLogger();
    InitSubscriber();
    InitPublisher();
    InitService();
    InitAlgorithm();
    InitHeartbeat();
}

NavigationRechargeNode::~NavigationRechargeNode()
{
    DeinitAlgorithm();

    last_mcu_exception_time_ = std::chrono::steady_clock::now();
    LOG_WARN("NavigationRechargeNode exit!");
}

void NavigationRechargeNode::InitParam()
{
    const std::string conf_file{"conf/navigation_recharge_node/navigation_recharge_node.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        LOG_INFO("NavigationRechargeNode create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("NavigationRechargeNode create config path failed!!!");
        }
    }
    if (!Config<NavigationRechargeNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init NavigationRechargeNode config parameters failed!");
    }
    NavigationRechargeNodeConfig config = Config<NavigationRechargeNodeConfig>::GetConfig();
    LOG_INFO("[navigation_recharge_node] git tag: {}", _GIT_TAG_);
    LOG_INFO("[navigation_recharge_node] git version: {}", _GIT_VERSION_);
    LOG_INFO("[navigation_recharge_node] compile time: {}", _COMPILE_TIME_);
    LOG_INFO("{}", config.toString().c_str());

    log_dir_ = config.common_conf.log_dir;
    console_log_level_ = config.common_conf.console_log_level;
    file_log_level_ = config.common_conf.file_log_level;
    recharge_alg_conf_file_ = config.recharge_alg_conf_file;

    if (!Config<NavigationRechargeNodeConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set NavigationRechargeNodeConfig config parameters failed!");
    }

    CreateDirectories(log_dir_);
}

void NavigationRechargeNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void NavigationRechargeNode::InitLogger()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void NavigationRechargeNode::InitAlgorithmParam()
{
    std::string conf_path = GetDirectoryPath(recharge_alg_conf_file_);
    if (!conf_path.empty())
    {
        LOG_INFO("Navigation recharge algo create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("Navigation recharge algo create config path failed!!!");
        }
    }
    if (!Config<NavigationRechargeAlgConfig>::Init(recharge_alg_conf_file_))
    {
        LOG_WARN("Init Navigation recharge algo config parameters failed!");
    }
    NavigationRechargeAlgConfig config = Config<NavigationRechargeAlgConfig>::GetConfig();
    LOG_INFO("{}", config.toString().c_str());
    ConfigParamToAlgorithmParam(config, recharge_alg_param_);
    if (!Config<NavigationRechargeAlgConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set Navigation recharge algo config parameters failed!");
    }
}

void NavigationRechargeNode::ConfigParamToAlgorithmParam(const NavigationRechargeAlgConfig &config, RechargeAlgParam &param)
{
    param.try_max_num = config.try_max_num;     // 重复探索最大次数
    param.no_qr_max_num = config.no_qr_max_num; // 记录得不到QR码最大次数
    param.save_data_num = config.save_data_num; // 保存数据帧数
    param.save_terminal_num = config.save_terminal_num;
    param.head_center_min_dist = config.head_center_min_dist;                   // 充电桩头像素偏差
    param.head_center_max_dist = config.head_center_max_dist;                   // 回充充电桩可调像素距离阈值
    param.station_qr_direction_min_dist = config.station_qr_direction_min_dist; // 二维码检测中心和充电桩检测中心相对方位最小像素阈值

    param.qr_code_clear_angle = config.qr_code_clear_angle;         // 1.13(65度)
    param.qr_code_detect_angle_1 = config.qr_code_detect_angle_1;   // 0.2618(15度), 0.1745(10度)，0.0873(5度)
    param.qr_code_min_distance = config.qr_code_min_distance;       // 离二维码最近距离 0.65 m
    param.start_recharge_distance = config.start_recharge_distance; // 开始回充距离 1.5 m
    param.qr_code_x_min_dist = config.qr_code_x_min_dist;           // 调整y和yaw时x的最大距离阈值 0.92 m
    param.qr_code_y_min_dist = config.qr_code_y_min_dist;
    param.circle_r_dist = config.circle_r_dist;
    param.explore_distance = config.explore_distance;
    param.explore_vel = config.explore_vel;
    param.kp_y = config.kp_y;
    param.kp_yaw = config.kp_yaw;
    param.kp_perception = config.kp_perception;
    param.recharge_adjust_linear = config.recharge_adjust_linear;
    param.recharge_pile_linear = config.recharge_pile_linear;
    param.recharge_pile_angular = config.recharge_pile_angular;
}

void NavigationRechargeNode::InitHeartbeat()
{
    pub_heartbeat_ = std::make_unique<NodeHeartbeatPublisher>();
    pub_heartbeat_->start();
}

void NavigationRechargeNode::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_nav_alg_ctrl_ = std::make_unique<iox_nav_alg_ctrl_publisher>(
        iox::capro::ServiceDescription{kNavigationNavAlgCtrlIox[0],
                                       kNavigationNavAlgCtrlIox[1],
                                       kNavigationNavAlgCtrlIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);

    pub_nav_recharge_final_result_ = std::make_unique<iox_nav_recharge_result_publisher>(
        iox::capro::ServiceDescription{kNavigationRechargeFinalResultIox[0],
                                       kNavigationRechargeFinalResultIox[1],
                                       kNavigationRechargeFinalResultIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);

    pub_recharge_state_ = std::make_unique<iox_recharge_state_publisher>(
        iox::capro::ServiceDescription{kNavigationRechargeStateIox[0],
                                       kNavigationRechargeStateIox[1],
                                       kNavigationRechargeStateIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
    // 发布 cross region 运行状态
    pub_cross_region_state_ = std::make_unique<iox_cross_region_state_publisher>(
        iox::capro::ServiceDescription{kNavigationCrossRegionStateIox[0],
                                       kNavigationCrossRegionStateIox[1],
                                       kNavigationCrossRegionStateIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
}

void NavigationRechargeNode::InitSubscriber()
{
    sub_charge_detect_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__ChargeResult>>(
        "detect_charge_result", 1, [this](const fescue_msgs__msg__ChargeResult &data, const std::string &event) {
            (void)event;
            DealChargeDetectResult(data);
        });

    sub_qrcode_loc_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__QrCodeResult>>(
        "detect_qrcode_result", 1, [this](const fescue_msgs__msg__QrCodeResult &data, const std::string &event) {
            (void)event;
            DealQRCodeLocationResult(data);
        });

    sub_nav_alg_ctrl_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>>(
        "navigation_nav_alg_ctrl", 10, [this](const fescue_msgs__msg__NavigationAlgoCtrlData &data, const std::string &event) {
            (void)event;
            DealNavAlgCtrlResult(data);
        });

    sub_charge_pile_dock_status_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::ChargePileDockStatus>>(
        "mcu_charge_pile_dock_status", 1, [this](const mower_msgs::msg::ChargePileDockStatus &data, const std::string &event) {
            (void)event;
            DealChargePileDockStatus(data);
        });

    // 订阅nav运行状态
    sub_nav_running_state_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavigationRunningStateData>>(
        "navigation_running_state", 10, [this](const fescue_msgs__msg__NavigationRunningStateData &data, const std::string &event) {
            (void)event;
            DealNavRunningState(data);
        });

    sub_mcu_sensor_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuSensor>>(
        "mcu_sensor", 1, [this](const mower_msgs::msg::McuSensor &data, const std::string &event) {
            (void)event;
            DealMCUSensor(data);
        });

    sub_mcu_mission_info_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuMissionInfo>>(
        "mcu_mission_info", 10, [this](const mower_msgs::msg::McuMissionInfo &data, const std::string &event) {
            (void)event;
            DealMcuMissionInfo(data);
        });

    // 订阅mcu exception
    sub_mcu_exception_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuException>>(
        "mcu_exception", 10, [this](const mower_msgs::msg::McuException &data, const std::string &event) {
            (void)event;
            DealMCUException(data);
        });
    sub_mark_loc_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__MarkLocationResult>>(
        "mark_location_result", 1, [this](const fescue_msgs__msg__MarkLocationResult &data, const std::string &event) {
            (void)event;
            DealMarkLocationResult(data);
        });
    sub_cross_region_state_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__CrossRegionStateData>>(
        "navigation_cross_region_state", 10, [this](const fescue_msgs__msg__CrossRegionStateData &data, const std::string &event) {
            (void)event;
            DealCrossRegionState(data);
        });
}

void NavigationRechargeNode::InitAlgorithm()
{
    InitAlgorithmParam();
    recharge_alg_ = std::make_unique<NavigationRechargeAlg>(recharge_alg_param_);
    recharge_alg_->SetFeatureSelectCallback([this](const std::vector<FeatureSelectData> &data) {
        this->DealFeatureSelectCallback(data);
    });
    recharge_alg_->SetCrossRegionRunningStateCallback([this](CrossRegionRunningState state) -> void {
        this->DealCrossRegionRunningStateCallback(state);
    });
    thread_running_.store(true);
    recharge_thread_ = std::thread(&NavigationRechargeNode::RechargeThread, this);

    recharge_alg_->SetRechargeRunningStateCallback([this](RechargeRunningState state) {
        this->DealRechargeRunningStateCallback(state);
    });
}

void NavigationRechargeNode::DealCrossRegionRunningStateCallback(CrossRegionRunningState state)
{
    if (pub_cross_region_state_)
    {
        fescue_msgs__msg__CrossRegionStateData data;
        data.state = static_cast<fescue_msgs_enum__CrossRegionState>(state);
        pub_cross_region_state_->publishCopyOf(data).or_else([](auto &error) {
            std::cerr << "NavigationMowerNode pub_cross_region_state_ Unable to publishCopyOf, error: "
                      << error << std::endl;
        });
    }
}

void NavigationRechargeNode::DealCrossRegionState(const fescue_msgs__msg__CrossRegionStateData &msg)
{
    std::lock_guard<std::mutex> lck(cross_region_mtx_);
    LOG_ERROR("################# CrossRegionRunning state : {}", asStringLiteral(static_cast<CrossRegionRunningState>(msg.state)));
    cross_region_state_ = static_cast<CrossRegionRunningState>(msg.state);
}

void NavigationRechargeNode::DealMarkLocationResult(const fescue_msgs__msg__MarkLocationResult &msg)
{
    std::lock_guard<std::mutex> lck(mark_loc_mtx_);
    mark_loc_result_.timestamp = msg.timestamp_ms;
    mark_loc_result_.mark_perception_status = msg.mark_perception_status;
    mark_loc_result_.mark_perception_direction = msg.mark_perception_direction;
    mark_loc_result_.detect_status = static_cast<int>(msg.detect_status);
    mark_loc_result_.roi_confidence = msg.roi_confidence;
    mark_loc_result_.target_direction = msg.target_direction;
    mark_loc_result_.mark_id = msg.mark_id;
    mark_loc_result_.xyzrpw.x = msg.pose.position.x;
    mark_loc_result_.xyzrpw.y = msg.pose.position.y;
    mark_loc_result_.xyzrpw.z = msg.pose.position.z;
    mark_loc_result_.xyzrpw.r = msg.roll;
    mark_loc_result_.xyzrpw.p = msg.pitch;
    mark_loc_result_.xyzrpw.w = msg.yaw;
    mark_loc_result_.mark_id_distance.clear();
    for (size_t i = 0; i < msg.mark_id_dis.size(); i++)
    {
        MarkIdDistance mark_id_dis;
        mark_id_dis.mark_id = msg.mark_id_dis[i].id;
        mark_id_dis.distance = msg.mark_id_dis[i].distance;
        mark_loc_result_.mark_id_distance.push_back(mark_id_dis);
    }
}

void NavigationRechargeNode::InitService()
{
    service_get_node_param_ = std::make_unique<IceoryxServerMower<get_node_param_request, get_node_param_response>>(
        "get_navigation_recharge_node_param_request", 10U,
        [this](const get_node_param_request &request, get_node_param_response &response) {
            (void)request;
            response.success = GetRechargeNodeParam(response.data);
            LOG_INFO("Get navigation recharge node param execute {}", response.success);
        });

    service_set_node_param_ = std::make_unique<IceoryxServerMower<set_node_param_request, set_node_param_response>>(
        "set_navigation_recharge_node_param_request", 10U,
        [this](const set_node_param_request &request, set_node_param_response &response) {
            response.success = SetRechargeNodeParam(request.data);
            LOG_INFO("Set navigation recharge node param execute {}", response.success);
        });
}

void NavigationRechargeNode::DealRechargeRunningStateCallback(RechargeRunningState state)
{
    if (pub_recharge_state_)
    {
        fescue_msgs__msg__RechargeStateData data;
        data.state = static_cast<fescue_msgs_enum__RechargeState>(state);
        pub_recharge_state_->publishCopyOf(data)
            .or_else([](auto &error) {
                std::cerr << "NavigationRechargeNode pub_recharge_state_ Unable to publishCopyOf, error: "
                          << error << std::endl;
            });
    }
}

void NavigationRechargeNode::DeinitAlgorithm()
{
    if (recharge_alg_)
    {
        recharge_alg_->ProhibitVelPublisher();
    }

    thread_running_.store(false);
    if (recharge_thread_.joinable())
    {
        recharge_thread_.join();
    }
}

void NavigationRechargeNode::CheckMCUExeceptionTimeout()
{
    std::lock_guard<std::mutex> lock(mcu_exception_mutex_);
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_mcu_exception_time_).count();
    if (duration > 1000)
    {
        mcu_exception_status_ = McuExceptionStatus::NORMAL;
        LOG_WARN_THROTTLE(3000, "[BehaviorThread]  MCUException 数据超时超过 1 秒，状态恢复为 NORMAL");
    }
}
void NavigationRechargeNode::ResetSubData()
{
    charge_station_result_.Reset();
    qrcode_loc_result_.Reset();
    mark_loc_result_.Reset();
    mcu_exception_status_ = McuExceptionStatus::NORMAL;
    cross_region_state_ = CrossRegionRunningState::UNDEFINED;
}
void NavigationRechargeNode::RechargeThread()
{
    ChargeStationDetectResult station_result;
    QRCodeLocationResult qrcode_loc_result;
    McuExceptionStatus mcu_exception_status;
    MarkLocationResult mark_loc_result;
    CrossRegionRunningState cross_region_state;

    while (thread_running_.load())
    {
        if (recharge_enable_.load())
        {
            CheckMCUExeceptionTimeout();
            {
                std::scoped_lock lock(station_mtx_, qrcode_loc_mtx_, mcu_exception_mutex_, mark_loc_mtx_, cross_region_mtx_);
                station_result = charge_station_result_;
                qrcode_loc_result = qrcode_loc_result_;
                mcu_exception_status = mcu_exception_status_;
                mark_loc_result = mark_loc_result_;
                cross_region_state = cross_region_state_;
            }
            if (recharge_alg_)
            {
                auto result = recharge_alg_->DoRecharge(mark_loc_result, cross_region_state, station_result, qrcode_loc_result, mcu_exception_status);
                if (result.recharge_completed)
                {
                    PublishRechargeFinalResult(result);
                    recharge_enable_.store(false);
                }
            }
        }
        else
        {
            LOG_DEBUG_THROTTLE(2000, "NavigationRechargeAlg disable!");
            if (recharge_alg_)
            {
                if (is_in_mow_)
                {
                    LOG_WARN_THROTTLE(2000, "In Mower Process, NavigationRechargeAlg Should Reset Beacon");
                    recharge_alg_->ResetCrossRegionBeaconStatus();
                }
                recharge_alg_->ResetRechargeFlags(false);
                recharge_alg_->ResetCrossRegionFlags();
                recharge_alg_->single_region_recharge_flag_ = false;
                ResetSubData();
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
    }
}

void NavigationRechargeNode::DealChargeDetectResult(const fescue_msgs__msg__ChargeResult &msg)
{
    std::lock_guard<std::mutex> lock(station_mtx_);
    charge_station_result_.timestamp_ms = msg.timestamp;
    charge_station_result_.is_chargestation = msg.is_charge;
    charge_station_result_.is_head = msg.is_head;
    charge_station_result_.range = msg.range;
    charge_station_result_.direction = static_cast<ChargeStationDirection>(msg.direction);
    charge_station_result_.pose = static_cast<ChargeStationPose>(msg.pose);
    if (msg.is_charge)
    {
        charge_station_result_.station_box.clear();
        for (size_t i = 0; i < IOX_MAX_STATION_BOX_SIZE; i++)
        {
            charge_station_result_.station_box.push_back(msg.station_box[i]);
        }
        charge_station_result_.charge_station_center_error = int((msg.station_box[2] + msg.station_box[4]) / 2) - 320;
    }
    if (msg.is_head)
    {
        charge_station_result_.head_box.clear();
        for (size_t i = 0; i < IOX_MAX_HEAD_BOX_SIZE; i++)
        {
            charge_station_result_.head_box.push_back(msg.head_box[i]);
        }
        charge_station_result_.head_center_error = int((msg.head_box[2] + msg.head_box[4]) / 2) - 320;
        if (recharge_alg_)
        {
            recharge_alg_->SetChargeStationResult(charge_station_result_);
        }
    }
}

void NavigationRechargeNode::DealQRCodeLocationResult(const fescue_msgs__msg__QrCodeResult &msg)
{
    std::lock_guard<std::mutex> lock(qrcode_loc_mtx_);
    if (writer_ && msg.status == 2)
    {
        static double time = 0;
        time += 0.01;
        char data[512] = {0};
        int len = snprintf(data, sizeof(data) - 1, "%f %f %f %f %f\r\n", time, msg.pose.position.x, msg.pose.position.y, msg.pose.position.z, msg.yaw);
        writer_->Write((uint8_t *)data, len);
    }
    LOG_DEBUG("ORIN QR Code:({} {} {}) rpw({} {} {})",
              qrcode_loc_result_.xyzrpw.x, qrcode_loc_result_.xyzrpw.y, qrcode_loc_result_.xyzrpw.z,
              qrcode_loc_result_.xyzrpw.r, qrcode_loc_result_.xyzrpw.p, qrcode_loc_result_.xyzrpw.y);
    // QRCodeLocationResult qrcode_loc_result_;
    qrcode_loc_result_.timestamp_ms = msg.timestamp_ms;
    qrcode_loc_result_.mark_perception_status = msg.mark_perception_status;
    qrcode_loc_result_.mark_perception_direction = msg.mark_perception_direction;
    qrcode_loc_result_.detect_status = static_cast<QRCodeDetectStatus>(msg.status);
    for (size_t i = 0; i < msg.qrcode_dis.size(); i++)
    {
        std::pair<int, float> pair{msg.qrcode_dis[i].id, msg.qrcode_dis[i].distance};
        qrcode_loc_result_.v_markID_dis.push_back(pair);
    }
    qrcode_loc_result_.markID = msg.mark_id;
    qrcode_loc_result_.target_direction = msg.target_direction;
    qrcode_loc_result_.xyzrpw.x = msg.pose.position.x;
    qrcode_loc_result_.xyzrpw.y = msg.pose.position.y;
    qrcode_loc_result_.xyzrpw.z = msg.pose.position.z;
    qrcode_loc_result_.xyzrpw.r = msg.roll;
    qrcode_loc_result_.xyzrpw.p = msg.pitch;
    qrcode_loc_result_.xyzrpw.w = msg.yaw;
    if (recharge_alg_)
    {
        recharge_alg_->SetQRCodeLocationResult(qrcode_loc_result_);
    }
    // qrcode_loc_result_queue_.push(std::move(qrcode_loc_result_));
}

void NavigationRechargeNode::DealNavAlgCtrlResult(const fescue_msgs__msg__NavigationAlgoCtrlData &msg)
{
    for (size_t i = 0; i < msg.data.size(); i++)
    {
        if (msg.data[i].type == FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_RECHARGE)
        {
            switch (msg.data[i].state)
            {
            case FESCUE_MSGS_ENUM_NAV_ALGO_STATE_DISABLE:
                recharge_enable_.store(false);
                SetRechargeVelPublisherProhibit(true);
                break;
            case FESCUE_MSGS_ENUM_NAV_ALGO_STATE_ENABLE:
                recharge_enable_.store(true);
                SetRechargeVelPublisherProhibit(false);
                break;
            default:
                break;
            }
            break;
        }
    }
}

void NavigationRechargeNode::DealChargePileDockStatus(const mower_msgs::msg::ChargePileDockStatus &msg)
{
    (void)msg;
}

void NavigationRechargeNode::DealMcuMissionInfo(const mower_msgs::msg::McuMissionInfo &data)
{
    if (recharge_alg_)
    {
        if ((data.mission_type == mower_msgs::srv::MowerMissionType::RANDOM_MOW) || (data.mission_type == mower_msgs::srv::MowerMissionType::SPIRAL_MOW))
        {
            is_in_mow_ = true;
        }
        else
        {
            is_in_mow_ = false;
        }
    }
}

void NavigationRechargeNode::DealMCUSensor(const mower_msgs::msg::McuSensor &data)
{
    LOG_DEBUG("charge_terminal_status is: {}", data.charge_terminal_status);
    if (recharge_alg_)
    {
        recharge_alg_->SetMCUSensor(data);
    }
}
void NavigationRechargeNode::DealMCUException(const mower_msgs::msg::McuException &data)
{
    std::lock_guard<std::mutex> lock(mcu_exception_mutex_);
    last_mcu_exception_time_ = std::chrono::steady_clock::now();

    switch (data.exception_value)
    {
    case mower_msgs::msg::McuExceptionValue::COLLISION_BELOW_3_SECOND_EXCEPTION: // collision below 3 seconds 动作：后退，转向继续行进
    {
        LOG_DEBUG("[DealMCUException] collision below 3 seconds 动作：后退，转向继续行进");
        mcu_exception_status_ = McuExceptionStatus::COLLISION;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::COLLISION_ABOVE_3_SECOND_EXCEPTION: // collision above 3 seconds 动作：行进电机后退，并转向执行避障动作
    {
        LOG_DEBUG("[DealMCUException] collision above 3 seconds 动作：行进电机后退，并转向执行避障动作");
        mcu_exception_status_ = McuExceptionStatus::COLLISION;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::SINGLE_LIFT_SENSOR_TRIGGER_0_3_TO_3_SECONDS_EXCEPTION: // single lift sensor trigger 0.3-3 seconds 动作：行走电机继续保持原状态
    {
        LOG_DEBUG("[DealMCUException] single lift sensor trigger 0.3-3 seconds 动作：行走电机继续保持原状态");
        mcu_exception_status_ = McuExceptionStatus::NORMAL;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::SINGLE_LIFT_SENSOR_TRIGGER_3_TO_10_SECONDS_EXCEPTION: // single lift sensor trigger 3-10 seconds 动作：停止割草电机，行进电机后退尝试解除提升
    {
        LOG_DEBUG("[DealMCUException] single lift sensor trigger 3-10 seconds 动作：停止割草电机，行进电机后退尝试解除提升");
        mcu_exception_status_ = McuExceptionStatus::LIFTING;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::DOUBLE_LIFT_SENSOR_TRIGGER_BELOW_10_SECONDS_EXCEPTION: // double lift sensor trigger below 10 seconds 动作：停止割草电机，行进电机后退尝试解除提升
    {
        LOG_DEBUG("[DealMCUException] double lift sensor trigger below 10 seconds 动作：停止割草电机，行进电机后退尝试解除提升");
        mcu_exception_status_ = McuExceptionStatus::LIFTING;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::NO_EXCEPTION:
    {
        // LOG_DEBUG("[DealMCUException] no exception");
        mcu_exception_status_ = McuExceptionStatus::NORMAL;
        break;
    }

    default:
        LOG_DEBUG("[DealMCUException] No safety abnormalities");
        mcu_exception_status_ = McuExceptionStatus::UNKNOWN;
        break;
    }
}

void NavigationRechargeNode::DealNavRunningState(const fescue_msgs__msg__NavigationRunningStateData &data)
{
    if (recharge_alg_)
    {
        recharge_alg_->SetAlgoRunningState(static_cast<MowerRunningState>(data.state));
    }
}

void NavigationRechargeNode::DealFeatureSelectCallback(const std::vector<FeatureSelectData> &data)
{
    if (!data.empty() && pub_nav_alg_ctrl_)
    {
        size_t data_size = data.size() > MAX_NAVIGATION_ALGO_NUM ? MAX_NAVIGATION_ALGO_NUM : data.size();
        fescue_msgs__msg__NavigationAlgoCtrlData nav_alg_ctrl_data;
        nav_alg_ctrl_data.sender.unsafe_assign("NavigationRechargeNode");
        for (size_t i = 0; i < data_size; i++)
        {
            fescue_msgs__msg__NavigationAlgoCtrlInfo info;
            info.type = static_cast<fescue_msgs__enum__NavigationAlgoType>(data.at(i).alg_id);
            info.state = static_cast<fescue_msgs__enum__NavigationAlgoState>(data.at(i).alg_status);
            nav_alg_ctrl_data.data.push_back(info);
        }
        pub_nav_alg_ctrl_->publishCopyOf(nav_alg_ctrl_data)
            .or_else([](auto &error) { std::cerr << "NavigationRechargeNode nav_alg_ctrl_data Unable to publishCopyOf, error: " << error << std::endl; });
    }
}

bool NavigationRechargeNode::GetRechargeNodeParam(fescue_msgs__msg__NavigationRechargeNodeParam &data)
{
    data.console_log_level.unsafe_assign(console_log_level_.c_str());
    data.file_log_level.unsafe_assign(file_log_level_.c_str());
    return true;
}

bool NavigationRechargeNode::SetRechargeNodeParam(const fescue_msgs__msg__NavigationRechargeNodeParam &data)
{
    console_log_level_ = std::string(data.console_log_level.c_str());
    file_log_level_ = std::string(data.file_log_level.c_str());
    InitLogger();
    NavigationRechargeNodeConfig config = Config<NavigationRechargeNodeConfig>::GetConfig();
    config.common_conf.console_log_level = console_log_level_;
    config.common_conf.file_log_level = file_log_level_;
    if (!Config<NavigationRechargeNodeConfig>::SetConfig(config))
    {
        LOG_WARN("Set NavigationRechargeNode config parameters failed!");
        return false;
    }
    LOG_INFO("New NavigationRechargeNode params: {}", config.toString().c_str());
    return true;
}

void NavigationRechargeNode::PublishRechargeFinalResult(const RechargeAlgResult &result)
{
    if (pub_nav_recharge_final_result_)
    {
        LOG_WARN("PublishRechargeFinalResult completed is {}!", result.recharge_completed);
        fescue_msgs__msg__NavRechargeFinalResult recharge_result;
        recharge_result.completed = result.recharge_completed;
        recharge_result.result = result.result;
        pub_nav_recharge_final_result_->publishCopyOf(recharge_result)
            .or_else([](auto &error) {
                std::cerr << "NavigationRechargeNode Unable to publishCopyOf recharge final result, error: " << error << std::endl;
            });
    }
}

} // namespace fescue_iox