#include "cross_region_config.hpp"

#include <sstream>

namespace fescue_iox
{

NavigationCrossRegionAlgConfig &NavigationCrossRegionAlgConfig::operator=(const NavigationCrossRegionAlgConfig &config)
{
    if (this != &config)
    {
        // Multi-region channel parameters
        cross_region_linear = config.cross_region_linear;
        cross_region_angular = config.cross_region_angular;
        max_distance_threshold = config.max_distance_threshold;
        min_distance_threshold = config.min_distance_threshold;
        cross_region_special_linear = config.cross_region_special_linear;
        cross_region_special_angular = config.cross_region_special_angular;
        dis_tolerance = config.dis_tolerance;
        cross_region_angle_compensation = config.cross_region_angle_compensation;

        channel_stop_pose_x = config.channel_stop_pose_x;
        grass_count_threshold = config.grass_count_threshold;
        edge_mode_direction = config.edge_mode_direction;
        channel_width = config.channel_width;
        camera_2_center_dis = config.camera_2_center_dis;
        adjust_mode_x_direction_threshold = config.adjust_mode_x_direction_threshold;

        // Beacon detection variables
        mark_distance_threshold = config.mark_distance_threshold;
        perception_drive_cooldown_time_threshold = config.perception_drive_cooldown_time_threshold;

        cross_region_adjust_displace = config.cross_region_adjust_displace;
        channel_fixed_distance = config.channel_fixed_distance;
    }

    return *this;
}

std::string NavigationCrossRegionAlgConfig::toString() const
{
    std::stringstream ss;
    ss << "------------------------- " << typeid(*this).name() << " -------------------------\r\n";
    ss << "  cross_region_linear: " << cross_region_linear << "\n";
    ss << "  cross_region_angular: " << cross_region_angular << "\n";
    ss << "  max_distance_threshold: " << max_distance_threshold << "\n";
    ss << "  min_distance_threshold: " << min_distance_threshold << "\n";
    ss << "  cross_region_special_linear: " << cross_region_special_linear << "\n";
    ss << "  cross_region_special_angular: " << cross_region_special_angular << "\n";
    ss << "  dis_tolerance: " << dis_tolerance << "\n";
    ss << "  cross_region_angle_compensation: " << cross_region_angle_compensation << "\n";

    ss << "  channel_stop_pose_x: " << channel_stop_pose_x << "\n";
    ss << "  grass_count_threshold: " << grass_count_threshold << "\n";
    ss << "  edge_mode_direction: " << edge_mode_direction << "\n";
    ss << "  channel_width: " << channel_width << "\n";
    ss << "  camera_2_center_dis: " << camera_2_center_dis << "\n";
    ss << "  adjust_mode_x_direction_threshold: " << adjust_mode_x_direction_threshold << "\n";

    ss << "  mark_distance_threshold: " << mark_distance_threshold << "\n";
    ss << "  perception_drive_cooldown_time_threshold: " << perception_drive_cooldown_time_threshold << "\n";

    ss << "  cross_region_adjust_displace: " << cross_region_adjust_displace << "\n";
    ss << "  channel_fixed_distance: " << channel_fixed_distance << "\n";

    ss << "------------------------- " << typeid(*this).name() << " -------------------------";
    return ss.str();
}

bool operator==(const NavigationCrossRegionAlgConfig &lhs, const NavigationCrossRegionAlgConfig &rhs)
{
    return lhs.cross_region_linear == rhs.cross_region_linear &&
           lhs.cross_region_angular == rhs.cross_region_angular &&
           lhs.max_distance_threshold == rhs.max_distance_threshold &&
           lhs.min_distance_threshold == rhs.min_distance_threshold &&
           lhs.cross_region_special_linear == rhs.cross_region_special_linear &&
           lhs.cross_region_special_angular == rhs.cross_region_special_angular &&
           lhs.dis_tolerance == rhs.dis_tolerance &&
           lhs.cross_region_angle_compensation == rhs.cross_region_angle_compensation &&

           lhs.channel_stop_pose_x == rhs.channel_stop_pose_x &&
           lhs.grass_count_threshold == rhs.grass_count_threshold &&
           lhs.edge_mode_direction == rhs.edge_mode_direction &&
           lhs.channel_width == rhs.channel_width &&
           lhs.camera_2_center_dis == rhs.camera_2_center_dis &&
           lhs.adjust_mode_x_direction_threshold == rhs.adjust_mode_x_direction_threshold &&

           lhs.mark_distance_threshold == rhs.mark_distance_threshold &&
           lhs.perception_drive_cooldown_time_threshold == rhs.perception_drive_cooldown_time_threshold &&

           lhs.cross_region_adjust_displace == rhs.cross_region_adjust_displace &&
           lhs.channel_fixed_distance == rhs.channel_fixed_distance;
}

bool operator!=(const NavigationCrossRegionAlgConfig &lhs, const NavigationCrossRegionAlgConfig &rhs)
{
    return !(lhs == rhs);
}

template <>
bool Config<NavigationCrossRegionAlgConfig>::LoadConfig(NavigationCrossRegionAlgConfig &config, const std::string &conf_file)
{
    YAML::Node node;
    try
    {
        node = YAML::LoadFile(conf_file);
    }
    catch (const YAML::BadFile &e)
    {
        std::cerr << "NavigationCrossRegionAlgConfig load config fail (BadFile): " << e.what() << '\n';
        return false;
    }
    catch (const YAML::ParserException &e)
    {
        std::cerr << "NavigationCrossRegionAlgConfig load config fail (ParserException): " << e.what() << '\n';
        return false;
    }
    catch (const std::exception &e)
    {
        std::cerr << "NavigationCrossRegionAlgConfig load config fail (Unknown): " << e.what() << '\n';
        return false;
    }

    config.cross_region_linear = GetYamlValue<float>(node, "cross_region_linear", config.cross_region_linear);
    config.cross_region_angular = GetYamlValue<float>(node, "cross_region_angular", config.cross_region_angular);
    config.max_distance_threshold = GetYamlValue<float>(node, "max_distance_threshold", config.max_distance_threshold);
    config.min_distance_threshold = GetYamlValue<float>(node, "min_distance_threshold", config.min_distance_threshold);
    config.cross_region_special_linear = GetYamlValue<float>(node, "cross_region_special_linear", config.cross_region_special_linear);
    config.cross_region_special_angular = GetYamlValue<float>(node, "cross_region_special_angular", config.cross_region_special_angular);
    config.dis_tolerance = GetYamlValue<int>(node, "dis_tolerance", config.dis_tolerance);
    config.cross_region_angle_compensation = GetYamlValue<float>(node, "cross_region_angle_compensation", config.cross_region_angle_compensation);

    config.channel_stop_pose_x = GetYamlValue<float>(node, "channel_stop_pose_x", config.channel_stop_pose_x);
    config.grass_count_threshold = GetYamlValue<int>(node, "grass_count_threshold", config.grass_count_threshold);
    config.edge_mode_direction = GetYamlValue<int>(node, "edge_mode_direction", config.edge_mode_direction);
    config.channel_width = GetYamlValue<float>(node, "channel_width", config.channel_width);
    config.camera_2_center_dis = GetYamlValue<float>(node, "camera_2_center_dis", config.camera_2_center_dis);
    config.adjust_mode_x_direction_threshold = GetYamlValue<float>(node, "adjust_mode_x_direction_threshold", config.adjust_mode_x_direction_threshold);

    config.mark_distance_threshold = GetYamlValue<float>(node, "mark_distance_threshold", config.mark_distance_threshold);
    config.perception_drive_cooldown_time_threshold = GetYamlValue<int>(node, "perception_drive_cooldown_time_threshold", config.perception_drive_cooldown_time_threshold);

    config.cross_region_adjust_displace = GetYamlValue<float>(node, "cross_region_adjust_displace", config.cross_region_adjust_displace);
    config.channel_fixed_distance = GetYamlValue<float>(node, "channel_fixed_distance", config.channel_fixed_distance);

    return true;
}

template <>
bool Config<NavigationCrossRegionAlgConfig>::CreateConfig(const NavigationCrossRegionAlgConfig &conf, const std::string &conf_file)
{
    YAML::Node node;

    node["cross_region_linear"] = conf.cross_region_linear;
    node["cross_region_angular"] = conf.cross_region_angular;
    node["max_distance_threshold"] = conf.max_distance_threshold;
    node["min_distance_threshold"] = conf.min_distance_threshold;
    node["cross_region_special_linear"] = conf.cross_region_special_linear;
    node["cross_region_special_angular"] = conf.cross_region_special_angular;
    node["dis_tolerance"] = conf.dis_tolerance;
    node["cross_region_angle_compensation"] = conf.cross_region_angle_compensation;

    node["channel_stop_pose_x"] = conf.channel_stop_pose_x;
    node["grass_count_threshold"] = conf.grass_count_threshold;
    node["edge_mode_direction"] = conf.edge_mode_direction;
    node["channel_width"] = conf.channel_width;
    node["camera_2_center_dis"] = conf.camera_2_center_dis;
    node["adjust_mode_x_direction_threshold"] = conf.adjust_mode_x_direction_threshold;

    node["mark_distance_threshold"] = conf.mark_distance_threshold;
    node["perception_drive_cooldown_time_threshold"] = conf.perception_drive_cooldown_time_threshold;

    node["cross_region_adjust_displace"] = conf.cross_region_adjust_displace;
    node["channel_fixed_distance"] = conf.channel_fixed_distance;

    return WriteYamlFile(conf_file, node);
}

} // namespace fescue_iox
