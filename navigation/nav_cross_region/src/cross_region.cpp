#include "cross_region.hpp"

#include "cross_region_config.hpp"
#include "imu_data_processor.hpp"
#include "mower_sdk_version.h"
#include "nav_utils.hpp"
#include "process_fusion.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

using namespace mower_msgs::msg;

namespace fescue_iox
{

NavigationCrossRegionAlg::NavigationCrossRegionAlg(const CrossRegionAlgParam &param)
    : vel_publisher_(std::make_unique<VelocityPublisher>("CrossRegion"))
{
    last_cooldown_time_ = std::chrono::steady_clock::now();
    straight_start_time_ = std::chrono::steady_clock::now();
    beacon_pairing_error_start_time_ = std::chrono::steady_clock::now();

    last_distance_update_time_ = std::chrono::steady_clock::time_point{};
    last_grass_distance_update_time_ = std::chrono::steady_clock::time_point{};

    linear_start_time_ = std::chrono::steady_clock::now();
    last_imu_time_ = std::chrono::steady_clock::now();
    timed_movement_start_time_ = std::chrono::steady_clock::now();

    bias_samples_.clear();
    SetCrossRegionAlgParam(param);
    InitPublisher();

    // 初始化IMU数据处理器
    InitializeImuProcessor();
}

NavigationCrossRegionAlg::~NavigationCrossRegionAlg()
{
    PublishVelocity(0.0, 0.0, 1000); // Keep publishing for 1s
    ShutdownImuProcessor();
    LOG_WARN("NavigationCrossRegionAlg exit!");
}

void NavigationCrossRegionAlg::SetMotorSpeedData(const MotorSpeedData &motor_speed_data)
{
    std::lock_guard<std::mutex> lock(motor_speed_mtx_);
    motor_speed_data_ = motor_speed_data;
    // LOG_DEBUG("[MowerAlg] [SetMotorSpeedData] motor_speed_left({}), motor_speed_right({})",
    //           motor_speed_data.motor_speed_left, motor_speed_data.motor_speed_right);
}

void NavigationCrossRegionAlg::DataConversion(MarkLocationResult &mark_loc_result)
{
    LOG_INFO_THROTTLE(5000, "[CrossRegion]  Coordinate transformation MarkLocation result: detect_status: {} mark_perception_status: {} mark_perception_direction: {} roi_confidence: {} "
                            "target_direction : {} markID : {} v_markID_dis.size : {} xyz({} {} {}) yaw({})",
                      mark_loc_result.detect_status, mark_loc_result.mark_perception_status, mark_loc_result.mark_perception_direction,
                      mark_loc_result.roi_confidence, mark_loc_result.target_direction, mark_loc_result.mark_id, mark_loc_result.mark_id_distance.size(),
                      mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
                      Radians2Degrees(mark_loc_result.xyzrpw.w));

    for (const auto &mark_id_distance : mark_loc_result.mark_id_distance)
    {
        LOG_INFO_THROTTLE(5000, "[CrossRegion] mark_id_distance beacon and distance: mark_id({}), distance({})",
                          mark_id_distance.mark_id, mark_id_distance.distance);
    }

    if (mark_loc_result.roi_confidence >= 60 && mark_loc_result.roi_confidence <= 100) // 60~100
    {
        mark_loc_result.roi_confidence = 1; // 1 Indicates in ROI area
    }
    else if (mark_loc_result.roi_confidence < 60 && mark_loc_result.roi_confidence >= 0) // 0~60
    {
        mark_loc_result.roi_confidence = 0; // 0 Indicates not in ROI area
    }
    else
    {
        mark_loc_result.roi_confidence = -1; //-1 Indicates detection failed
    }

    // Convert the coordinate system from mark as the right-hand coordinate system, camera relative to mark, to base_link relative to mark
    if (mark_loc_result.detect_status == 2)
    {
        // Input fixed coordinates of camera relative to base_link
        Pose_Mark camera_to_base_link = {camera_2_center_dis_, 0.0, 0.0, 0.0, 0.0, 0.0};

        // Input coordinates of camera relative to mark
        Pose_Mark camera_to_mark = {mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
                                    mark_loc_result.xyzrpw.r, mark_loc_result.xyzrpw.p, mark_loc_result.xyzrpw.w};

        // Calculate coordinates of base_link relative to mark
        Pose_Mark base_link_to_mark = calculateBaseLinkRelativeToMark(camera_to_mark, camera_to_base_link);

        mark_loc_result.xyzrpw.x = base_link_to_mark.x;
        mark_loc_result.xyzrpw.y = base_link_to_mark.y;
        mark_loc_result.xyzrpw.z = base_link_to_mark.z;
        mark_loc_result.xyzrpw.r = base_link_to_mark.roll;
        mark_loc_result.xyzrpw.p = base_link_to_mark.pitch;
        mark_loc_result.xyzrpw.w = base_link_to_mark.yaw;

        LOG_INFO_THROTTLE(5000, "[CrossRegion]  Coordinate transformation MarkLocation result: detect_status({}) mark_perception_status({}) mark_perception_direction({}) "
                                "roi_confidence({}) target_direction({}) markID({}) v_markID_dis.size({}) xyz({} {} {}) yaw({})",
                          mark_loc_result.detect_status,
                          mark_loc_result.mark_perception_status, mark_loc_result.mark_perception_direction,
                          mark_loc_result.roi_confidence, mark_loc_result.target_direction,
                          mark_loc_result.mark_id, mark_loc_result.mark_id_distance.size(),
                          mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
                          Radians2Degrees(mark_loc_result.xyzrpw.w));
    }
}

void NavigationCrossRegionAlg::SetCrossRegionAlgParam(const CrossRegionAlgParam &param)
{
    cross_region_linear_ = param.cross_region_linear;
    cross_region_angular_ = param.cross_region_angular;
    max_distance_threshold_ = param.max_distance_threshold;
    min_distance_threshold_ = param.min_distance_threshold;
    cross_region_special_linear_ = param.cross_region_special_linear;
    cross_region_special_angular_ = param.cross_region_special_angular;
    dis_tolerance_ = param.dis_tolerance;
    cross_region_angle_compensation_ = param.cross_region_angle_compensation;
    channel_stop_pose_x_ = param.channel_stop_pose_x;
    grass_count_threshold_ = param.grass_count_threshold;
    edge_mode_direction_ = param.edge_mode_direction;
    channel_width_ = param.channel_width;
    camera_2_center_dis_ = param.camera_2_center_dis;
    adjust_mode_x_direction_threshold_ = param.adjust_mode_x_direction_threshold;
    mark_distance_threshold_ = param.mark_distance_threshold;
    perception_drive_cooldown_time_threshold_ = param.perception_drive_cooldown_time_threshold;
    cross_region_adjust_displace_ = param.cross_region_adjust_displace;
    channel_fixed_distance_ = param.channel_fixed_distance;
}

void NavigationCrossRegionAlg::GetCrossRegionAlgParam(CrossRegionAlgParam &param)
{
    param.cross_region_linear = cross_region_linear_;
    param.cross_region_angular = cross_region_angular_;
    param.max_distance_threshold = max_distance_threshold_;
    param.min_distance_threshold = min_distance_threshold_;
    param.cross_region_special_linear = cross_region_special_linear_;
    param.cross_region_special_angular = cross_region_special_angular_;
    param.dis_tolerance = dis_tolerance_;
    param.cross_region_angle_compensation = cross_region_angle_compensation_;
    param.channel_stop_pose_x = channel_stop_pose_x_;
    param.grass_count_threshold = grass_count_threshold_;
    param.edge_mode_direction = edge_mode_direction_;
    param.channel_width = channel_width_;
    param.camera_2_center_dis = camera_2_center_dis_;
    param.adjust_mode_x_direction_threshold = adjust_mode_x_direction_threshold_;
    param.mark_distance_threshold = mark_distance_threshold_;
    param.perception_drive_cooldown_time_threshold = perception_drive_cooldown_time_threshold_;
    param.cross_region_adjust_displace = cross_region_adjust_displace_;
    param.channel_fixed_distance = channel_fixed_distance_;
}

void NavigationCrossRegionAlg::SetMarkLocationResult(const MarkLocationResult &mark_loc_result)
{
    if (apply_coordinate_transform_)
    {
        std::lock_guard<std::mutex> lck(mark_loc_mutex_);
        mark_loc_result_ = mark_loc_result;
        mark_loc_result_law_ = mark_loc_result;
        DataConversion(mark_loc_result_);
    }
}

void NavigationCrossRegionAlg::SetAlgoRunningState(MowerRunningState state)
{
    LOG_INFO_THROTTLE(1000, "NavigationCrossRegionAlg running state: {}", static_cast<int>(state));
    mower_running_state_ = state;
    if (state == MowerRunningState::RUNNING)
    {
        ResumeVelocity();
    }
    else if (state == MowerRunningState::PAUSE)
    {
        PauseVelocity();
    }
    else
    {
        LOG_ERROR("[NavigationCrossRegionAlg] Unknown state {}!", static_cast<int>(state));
    }
}

const char *NavigationCrossRegionAlg::GetVersion()
{
    return "V1.2.0";
}

void NavigationCrossRegionAlg::ProhibitVelPublisher()
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(0, 0);
        vel_publisher_->SetProhibitFlag(true);
    }
}

void NavigationCrossRegionAlg::PublishVelocity(float linear, float angular, uint64_t duration_ms)
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(linear, angular, duration_ms);
        if (duration_ms > 0)
        {
            while (!vel_publisher_->IsExecutionCompleted())
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
    }
}

void NavigationCrossRegionAlg::PublishZeroVelocity()
{
    PublishVelocity(0, 0);
}

void NavigationCrossRegionAlg::PauseVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->PauseVelocity();
    }
}

void NavigationCrossRegionAlg::ResumeVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->ResumeVelocity();
    }
}

void NavigationCrossRegionAlg::ShowMowerRunningInfo(const MarkLocationResult &mark_loc_result)
{
    (void)mark_loc_result;
    // Print time difference
    LOG_WARN_THROTTLE(2000, "[CrossRegion] Perception-driven cooldown timer (seconds): ({})!", perception_drive_duration_.count());
}

CrossRegionAlgResult NavigationCrossRegionAlg::DoCrossRegion(PerceptionFusionResult &fusion_result,
                                                             MarkLocationResult &mark_loc_result,
                                                             ImuData &imu_data)
{
    if (mower_running_state_ == MowerRunningState::PAUSE)
    {
        LOG_WARN_THROTTLE(3000, "[CrossRegion] DoCrossRegion() is PAUSE!");
        return CrossRegionAlgResult(false, CrossRegionStatus::InProgress);
    }

    // Perception-driven cooldown time
    ShowMowerRunningInfo(mark_loc_result);

    float x_first = 0.0;
    float y_first = 0.0;
    float yaw_first = 0.0;
    CrossRegionAlgResult cross_region_result_3(false, CrossRegionStatus::InProgress);
    CrossRegionAlgResult cross_region_result_4(false, CrossRegionStatus::InProgress);

    DealCrossRegionPhase_1(mark_loc_result);
    DealCrossRegionPhase_2(mark_loc_result, x_first, y_first, yaw_first);
    cross_region_result_3 = DealCrossRegionPhase_3(mark_loc_result, x_first, y_first, yaw_first);

    DealStage4EnteringPassage(x_first, y_first, yaw_first);
    cross_region_result_4 = DealCrossRegionPhase_4(mark_loc_result, fusion_result, imu_data);

    return cross_region_result_3.cross_region_completed ? cross_region_result_3 : cross_region_result_4;
}

void NavigationCrossRegionAlg::DealCrossRegionPhase_1(MarkLocationResult &mark_loc_result)
{
    if (!phase_1_completed_ && !phase_2_completed_ && !phase_3_completed_)
    {
        FindBeaconsPhase_1(mark_loc_result);
    }
}

void NavigationCrossRegionAlg::DealCrossRegionPhase_2(MarkLocationResult &mark_loc_result,
                                                      float &x_first, float &y_first, float &yaw_first)
{
    if (phase_1_completed_ && !phase_2_completed_ && !phase_3_completed_)
    {
        EdgeFollowDisable();

        if (mark_loc_result.detect_status != 2 || mark_loc_result.mark_perception_direction != 0) // Beacon cannot calculate pose or the mower is not facing the beacon
        {
            LOG_INFO("[CrossRegion] [Phase 2] Beacon cannot calculate pose, start Phase 1, find beacon along the edge");
            phase_1_completed_ = false;
            PublishZeroVelocity();
        }
        else
        {
            LOG_INFO("[CrossRegion] [Phase 2] Get precise beacon QR code pose");
            GetPrecisePositionOfBeacon(mark_loc_result, x_first, y_first, yaw_first, phase_2_completed_);

            // // Within the confidence region
            // if (mark_loc_result.roi_confidence == 1)
            // {
            //     LOG_INFO("[CrossRegion] [Phase 2] Beacon appears in the confidence region");
            //     GetPrecisePositionOfBeacon(mark_loc_result, x_first, y_first, yaw_first, phase_2_completed_);
            // }
            // else // Outside the confidence region
            // {
            //     LOG_INFO("[CrossRegion] [Phase 2] Beacon appears outside the confidence region");
            //     CorrectionOutsideConfidence(mark_loc_result);
            // }
        }
    }
}

CrossRegionAlgResult NavigationCrossRegionAlg::DealCrossRegionPhase_3(MarkLocationResult &mark_loc_result,
                                                                      const float &x_first, const float &y_first, const float &yaw_first)
{
    (void)mark_loc_result;
    bool cross_region_completed = false;

    if (phase_1_completed_ && phase_2_completed_ && !phase_3_completed_)
    {
        {
            std::lock_guard<std::mutex> lck(cross_region_mutex_);
            cross_region_state_ = CrossRegionRunningState::STAGE3_LOC_DETECT_BEACON_WITH_POS; // Phase 3, localization detects QR code, can calculate pose
            UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE3_LOC_DETECT_BEACON_WITH_POS);
        }

        if (x_first > 0.1) // 看到下一草坪的信标。提前结束跨区域
        {
            LOG_INFO("[CrossRegion] [Phase 3] In the case of the next lawn. Adjust to pass_point based on the beacon");

            phase_3_completed_ = true;

            if (phase_1_completed_ && phase_2_completed_ && phase_3_completed_)
            {
                phase_41_completed_ = true;
                phase_42_completed_ = true;
                phase_43_completed_ = false;
                if (phase_1_completed_ && phase_2_completed_ && phase_3_completed_)
                {
                    DealCrossRegionPhase_43(mark_loc_result, x_first, y_first, yaw_first);
                    DealCrossRegionPhase_44(mark_loc_result, cross_region_completed, x_first);

                    return CrossRegionAlgResult(cross_region_completed, CrossRegionStatus::Successed);
                }
            }
        }
        else // 看见当前草坪的信标
        {
            LOG_INFO("[CrossRegion] [Phase 3] In the case of the current lawn. Adjust to pass_point based on the beacon");

            if (edge_mode_direction_ == 1) // Edge following clockwise
            {
                LOG_INFO("[CrossRegion] [Phase 3] Not considering clockwise edge following cross-region scenario");
                // LOG_INFO("[CrossRegion] [Phase 3] In the case of clockwise edge following. Adjust to pass_point based on the beacon");
                // ClockwiseAlignPassPoints(x_first, y_first, yaw_first);
            }
            else // Edge following counterclockwise
            {
                LOG_INFO("[CrossRegion] [Phase 3] In the case of counterclockwise edge following. Adjust to pass_point based on the beacon");
                // CounterClockwiseAlignPassPoints(x_first, y_first, yaw_first);
                // ControlProcessToPassPoints(x_first, y_first, yaw_first);
                ControlProcessToPassPointsWithIMU(x_first, y_first, yaw_first);
            }

            LOG_INFO("[CrossRegion] [Phase 3] Phase 3 completed. Start walking along the channel");
            phase_3_completed_ = true; // Allow passage through the channel
        }
    }

    return CrossRegionAlgResult(cross_region_completed, CrossRegionStatus::InProgress);
}

CrossRegionAlgResult NavigationCrossRegionAlg::DealCrossRegionPhase_4(MarkLocationResult &mark_loc_result,
                                                                      PerceptionFusionResult &fusion_result,
                                                                      ImuData &imu_data)
{
    bool cross_region_completed = false;

    if (phase_1_completed_ && phase_2_completed_ && phase_3_completed_ && is_walking_before_crossing_passage_)
    {
        LOG_INFO_THROTTLE(1000, "[CrossRegion] [Phase 4] Has moved straight for a certain distance, start detecting beacons");

        //  1. Get precise beacon QR code pose
        float x_first = 0.0;
        float y_first = 0.0;
        float yaw_first = 0.0;
        DealCrossRegionPhase_41(mark_loc_result, fusion_result, cross_region_completed, imu_data);
        DealCrossRegionPhase_42(mark_loc_result, x_first, y_first, yaw_first);
        DealCrossRegionPhase_43(mark_loc_result, x_first, y_first, yaw_first);
        DealCrossRegionPhase_44(mark_loc_result, cross_region_completed, x_first);

        return CrossRegionAlgResult(cross_region_completed, CrossRegionStatus::Successed);
    }

    return CrossRegionAlgResult(cross_region_completed, CrossRegionStatus::InProgress);
}

void NavigationCrossRegionAlg::DealStage4EnteringPassage(const float &x_first, const float &y_first, const float &yaw_first)
{
    (void)x_first;
    (void)y_first;
    (void)yaw_first;

    if (phase_1_completed_ && phase_2_completed_ && phase_3_completed_ && !is_walking_before_crossing_passage_)
    {
        LOG_INFO("[CrossRegion] [EnteringPassage] x_after_adjustment_ = {}", x_after_adjustment_);
        if (x_after_adjustment_ < 0.0)
        {
            ControlLinearMotion(0.0, x_after_adjustment_, cross_region_linear_, 1);
            is_walking_before_crossing_passage_ = true;
        }
        else
        {
            is_walking_before_crossing_passage_ = true;
        }

        // Phase 4, just entered the channel and has moved straight for a certain distance
        {
            std::lock_guard<std::mutex> lck(cross_region_mutex_);
            cross_region_state_ = CrossRegionRunningState::STAGE4_ENTERING_CHANNEL; // Phase 4, just entered the channel and has moved straight for a certain distance
            UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_ENTERING_CHANNEL);
        }
    }
}

void NavigationCrossRegionAlg::DealCrossRegionPhase_41(MarkLocationResult &mark_loc_result, PerceptionFusionResult &fusion_result,
                                                       bool &cross_region_completed,
                                                       ImuData &imu_data)
{
    MotorSpeedData motor_speed_data;
    {
        std::lock_guard<std::mutex> lock(motor_speed_mtx_);
        motor_speed_data = motor_speed_data_;
    }

    float act_linear = 0.0f;
    float act_angular = 0.0f;
    GetVelocityFromMotorRPM(motor_speed_data.motor_speed_left,
                            motor_speed_data.motor_speed_right,
                            wheel_radius_, wheel_base_, act_linear, act_angular);

    if (!phase_41_completed_ && !phase_42_completed_ && !phase_43_completed_)
    {
        // FindBeaconsPhase_41(mark_loc_result, fusion_result, cross_region_completed, imu_data, act_linear, act_angular);
        FindBeaconsPhase_41_New(mark_loc_result, fusion_result, cross_region_completed, imu_data, act_linear, act_angular);
    }

    UpdateStraightMotionTimingNew(act_linear);
    // UpdateStraightMotionTiming(act_linear);
}

void NavigationCrossRegionAlg::UpdateStraightMotionTimingNew(float act_linear)
{
    auto current_time = std::chrono::steady_clock::now();

    if (!is_straight_timing_started_)
    {
        straight_start_time_ = std::chrono::steady_clock::now();
        is_straight_timing_started_ = true;
        LOG_WARN("[CrossRegion] [Phase 4] Straight movement timing started");
    }

    float dt;
    if (last_distance_update_time_.time_since_epoch().count() == 0)
    {
        dt = 0.02f;
        last_distance_update_time_ = std::chrono::steady_clock::now();
    }
    else
    {
        dt = std::chrono::duration<float>(current_time - last_distance_update_time_).count();
    }

    last_distance_update_time_ = current_time; // Update timestamp

    // Calculate displacement increment at current speed (not absolute value)
    float delta_distance = act_linear * dt;
    accumulated_distance_ += delta_distance;

    // Calculate elapsed time
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(current_time - straight_start_time_);
    LOG_WARN("[CrossRegion] [Phase 4] Straight movement duration: {} seconds", duration.count());

    // Double condition check
    bool timeout = duration.count() >= CROSS_AREA_WALKING_TIMEOUT;
    bool over_distance = accumulated_distance_ >= cross_region_distance_threshold_;

    if (timeout || over_distance)
    {
        LOG_ERROR("[CrossRegion] Straight movement exceeded limit | Time: {}s Distance: {:.1f}m",
                  duration.count(), accumulated_distance_);
        PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_CROSS_REGION_GRASS_TIMEOUT_EXCEPTION);

        // // 添加补救措施：减速或停止
        // PublishVelocity(cross_region_linear_ * 0.5f, 0.0f, 1000);

        // // 尝试旋转一定角度寻找信标
        // if (duration.count() >= CROSS_AREA_WALKING_TIMEOUT + 5)
        // {
        //     PublishVelocity(0.0f, 0.3f, 2000); // 旋转一定角度寻找信标
        // }
    }
}

void NavigationCrossRegionAlg::UpdateStraightMotionTiming(float act_linear)
{
    auto current_time = std::chrono::steady_clock::now();
    // Only start timing during straight movement in non-grass or grass detection phase
    if (non_grass_area_reached_)
    {
        if (!is_straight_timing_started_)
        {
            straight_start_time_ = std::chrono::steady_clock::now();
            is_straight_timing_started_ = true;
            LOG_WARN("[CrossRegion] [Phase 4] Straight movement timing started");
        }

        float dt;
        if (last_distance_update_time_.time_since_epoch().count() == 0)
        {
            dt = 0.02f;
            last_distance_update_time_ = std::chrono::steady_clock::now();
        }
        else
        {
            dt = std::chrono::duration<float>(current_time - last_distance_update_time_).count();
        }
        last_distance_update_time_ = current_time; // Update timestamp

        // Calculate displacement increment at current speed (not absolute value)
        float delta_distance = act_linear * dt;
        accumulated_distance_ += delta_distance;

        // Calculate elapsed time
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(current_time - straight_start_time_);
        LOG_WARN("[CrossRegion] [Phase 4] Straight movement duration: {} seconds", duration.count());

        // Double condition check
        bool timeout = duration.count() >= CROSS_AREA_WALKING_TIMEOUT;
        bool over_distance = accumulated_distance_ >= cross_region_distance_threshold_;

        if (timeout || over_distance)
        {
            LOG_ERROR("[CrossRegion] Straight movement exceeded limit | Time: {}s Distance: {:.1f}m",
                      duration.count(), accumulated_distance_);
            PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_CROSS_REGION_GRASS_TIMEOUT_EXCEPTION);

            // // 添加补救措施：减速或停止
            // PublishVelocity(cross_region_linear_ * 0.5f, 0.0f, 1000);

            // // 尝试旋转一定角度寻找信标
            // if (duration.count() >= CROSS_AREA_WALKING_TIMEOUT + 5)
            // {
            //     PublishVelocity(0.0f, 0.3f, 2000); // 旋转一定角度寻找信标
            // }
        }
    }
    else
    {
        // Accumulate distance in grass area
        float dt;
        if (last_grass_distance_update_time_.time_since_epoch().count() == 0)
        {
            dt = 0.02f; // Assume initial time step is 20ms
            last_grass_distance_update_time_ = std::chrono::steady_clock::now();
        }
        else
        {
            dt = std::chrono::duration<float>(current_time - last_grass_distance_update_time_).count();
        }
        last_grass_distance_update_time_ = current_time;

        float delta_distance = act_linear * dt;
        grass_area_accumulated_distance_ += delta_distance;

        // // Check if distance exceeds 2 meters
        // if (grass_area_accumulated_distance_ > 2.0f)
        // {
        //     LOG_ERROR("[CrossRegion] Walking distance in grass area exceeds 2m, non-grass area not found, accumulated distance: {:.2f}m", grass_area_accumulated_distance_);
        //     PublishVelocity(0.0f, 0.0f, 1000);
        //     PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_CROSS_ZONE_NON_LAWN_NOT_FOUND_EXCEPTION);
        // }

        // Reset timing flag when not in straight movement
        LOG_WARN("[CrossRegion] [Phase 4] Timing restarted");
        is_straight_timing_started_ = false;
    }
}

void NavigationCrossRegionAlg::DealCrossRegionPhase_42(MarkLocationResult &mark_loc_result,
                                                       float &x_first, float &y_first, float &yaw_first)
{
    if (phase_41_completed_ && !phase_42_completed_ && !phase_43_completed_)
    {
        if (mark_loc_result.detect_status != 2 || mark_loc_result.mark_perception_direction != 0) // The beacon cannot calculate the pose or the mower is not facing the beacon
        {
            LOG_INFO("[CrossRegion] [Phase 4] Beacon cannot calculate pose, start Phase 1, straight cross-region");
            phase_41_completed_ = false;
            PublishZeroVelocity();

            // No beacon detected upon entering the channel
            if (mark_loc_result.detect_status == 0)
            {
                std::lock_guard<std::mutex> lck(cross_region_mutex_);
                cross_region_state_ = CrossRegionRunningState::STAGE4_LOC_NO_DETECT_BEACON; // Phase 4, localization does not detect QR code
                UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_LOC_NO_DETECT_BEACON);
            }
            else if (mark_loc_result.detect_status == 1)
            {
                std::lock_guard<std::mutex> lck(cross_region_mutex_);
                cross_region_state_ = CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_NO_POS; // Phase 4, localization detects QR code, cannot calculate pose
                UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_NO_POS);
            }
            else if (mark_loc_result.mark_perception_status != 0)
            {
                LOG_INFO("[CrossRegion] The mower is not facing the beacon, mark_perception_status != 0");
            }
        }
        else
        {
            LOG_INFO("[CrossRegion] [Phase 4] Localization can calculate pose");
            {
                std::lock_guard<std::mutex> lck(cross_region_mutex_);
                cross_region_state_ = CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_WITH_POS;
                UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_WITH_POS);
            }

            LOG_INFO("[CrossRegion] [Phase 4] Get precise beacon QR code pose");

            //  1. Get precise beacon QR code pose
            x_first = 0.0;
            y_first = 0.0;
            yaw_first = 0.0;
            GetPrecisePositionOfBeacon(mark_loc_result, x_first, y_first, yaw_first, phase_42_completed_);

            // // Within the confidence region
            // if (mark_loc_result.roi_confidence == 1)
            // {
            //     LOG_INFO("[CrossRegion] [Phase 4] Beacon appears in the confidence region");

            //     //  1. Get precise beacon QR code pose
            //     x_first = 0.0;
            //     y_first = 0.0;
            //     yaw_first = 0.0;
            //     GetPrecisePositionOfBeacon(mark_loc_result, x_first, y_first, yaw_first, phase_42_completed_);
            // }
            // else // Outside the confidence region
            // {
            //     LOG_INFO("[CrossRegion] [Phase 4] Beacon appears outside the confidence region");
            //     CorrectionOutsideConfidence(mark_loc_result); // Correct the car's orientation if the beacon is outside the confidence range
            // }
        }
    }
}

void NavigationCrossRegionAlg::DealCrossRegionPhase_43(MarkLocationResult &mark_loc_result,
                                                       const float &x_first, const float &y_first, const float &yaw_first)
{
    (void)mark_loc_result;
    if (phase_41_completed_ && phase_42_completed_ && !phase_43_completed_)
    {
        LOG_INFO("[CrossRegion] [Phase 4] Completed getting precise pose x_first = {}, y_first = {}, yaw_first = {}",
                 x_first, y_first, Radians2Degrees(yaw_first));

        // Adjust the car's pose to the cutoff point based on the obtained pose, and set the stopping distance
        if (edge_mode_direction_ == 1) // Edge following clockwise
        {
            LOG_INFO("[CrossRegion] [Phase 4] Not considering clockwise edge following cross-region scenario");

            // LOG_INFO("[CrossRegion] [Phase 4] In the case of clockwise edge following. Adjust to end_point based on the beacon");
            // ClockwiseAlignEndPoints(x_first, y_first, yaw_first);
        }
        else // Edge following counterclockwise
        {
            LOG_INFO("[CrossRegion] [Phase 4] In the case of counterclockwise edge following. Adjust to end_point based on the beacon");
            // CounterClockwiseAlignEndPoints(x_first, y_first, yaw_first);
            // ControlProcessToEndPoints(x_first, y_first, yaw_first);
            ControlProcessToEndPointsWithIMU(x_first, y_first, yaw_first);
        }

        LOG_INFO("[CrossRegion] [Phase 4] Phase 3 completed. Start adjusting to near the end point, then drive the car towards the target stop point");
        phase_43_completed_ = true;
    }
}

void NavigationCrossRegionAlg::DealCrossRegionPhase_44(MarkLocationResult &mark_loc_result, bool &cross_region_completed, const float &x_first)
{
    (void)mark_loc_result;
    if (phase_41_completed_ && phase_42_completed_ && phase_43_completed_)
    {
        // After adjusting to near the end point, drive the car towards the target stop point channel_stop_pose_x_= -0.5
        LOG_INFO("[CrossRegion] [Phase 4] After adjusting to near the end point, drive the car towards the target stop point");
        ControlLinearMotion(channel_stop_pose_x_, x_first, cross_region_linear_, 1);

        // Send a stop signal after reaching the specified position
        PublishVelocity(0.0, 0.0, 1000); // Keep publishing for 1s
        cross_region_completed = true;
        LOG_INFO("[CrossRegion] [Phase 4] Cross-region algorithm execution finished");

        {
            std::lock_guard<std::mutex> lck(cross_region_mutex_);
            cross_region_state_ = CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION; // Phase 4, beacon detection exits cross-region
            UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION);
        }
    }
}

void NavigationCrossRegionAlg::ResetCrossRegionFlags()
{
    is_walking_before_crossing_passage_ = false;

    phase_1_completed_ = false;
    phase_2_completed_ = false;
    phase_3_completed_ = false;

    phase_41_completed_ = false;
    phase_42_completed_ = false;
    phase_43_completed_ = false;

    is_cooldown_active_ = false;
    last_cooldown_time_ = std::chrono::steady_clock::now();
    perception_drive_duration_ = std::chrono::seconds(0);

    frames_.clear();
    current_state_ = RegionState::IN_GRASS;
    next_paired_beacon_id_ = -1;     // Next pair of beacon IDs
    non_grass_area_reached_ = false; // Phase 4, new state flag
    emergency_stop_ = false;         // Emergency stop flag
    front_beacon_detected_ = false;
    is_first_non_grass_area_reached_ = true;
    // UpdateCrossRegionRunningState(CrossRegionRunningState::UNDEFINED);

    // Reset straight timer
    is_straight_timing_started_ = false;
    straight_start_time_ = std::chrono::steady_clock::now();

    // IMU相关成员变量
    last_imu_timestamp_ = 0;

    is_first_linear_in_progress_ = false;
    linear_motion_completed_ = false;
    linear_start_time_ = std::chrono::steady_clock::now();

    // Reset timed movement variables
    timed_movement_in_progress_ = false;
    timed_movement_start_time_ = std::chrono::steady_clock::now();

    accumulated_angle_ = 0.0;                          // 干扰引起的航向角累积变化
    is_correcting_ = false;                            // 纠正状态标志
    yaw_target_ = 0.0;                                 // 直行目标航向角
    yaw_current_ = 0.0;                                // 当前航向角（IMU积分）
    last_imu_time_ = std::chrono::steady_clock::now(); // 上次IMU更新时间
    is_first_imu_ = true;

    is_bias_calibrated_ = false; // 重置校准状态
    bias_z_ = 0.0f;
    bias_samples_.clear();

    accumulated_distance_ = 0.0;
    last_distance_update_time_ = std::chrono::steady_clock::time_point{};
    last_grass_distance_update_time_ = std::chrono::steady_clock::time_point{};

    // Reset beacon pairing error state
    beacon_pairing_error_active_ = false;
    beacon_pairing_error_start_time_ = std::chrono::steady_clock::now();

    // 重置草地区域距离
    grass_area_accumulated_distance_ = 0.0f;

    // Reset channel distance tracking variables
    channel_distance_tracking_started_ = false;
    channel_accumulated_distance_ = 0.0f;
    channel_distance_start_time_ = std::chrono::steady_clock::time_point{};
    channel_last_distance_update_time_ = std::chrono::steady_clock::time_point{};

    // 重置IMU处理器状态
    if (imu_processor_)
    {
        imu_processor_->ResetState();
    }

    x_after_adjustment_ = 0.0;
}

void NavigationCrossRegionAlg::SetFeatureSelectCallback(std::function<void(const std::vector<FeatureSelectData> &)> callback)
{
    feature_select_callback_ = callback;
}

void NavigationCrossRegionAlg::SetCrossRegionRunningStateCallback(std::function<void(CrossRegionRunningState)> callback)
{
    cross_region_running_state_callback_ = callback;
}

void NavigationCrossRegionAlg::DealFeatureSelect(ThreadControl control, bool state)
{
    std::vector<FeatureSelectData> feature_data;
    feature_data.clear();
    FeatureSelectData feature{control, static_cast<int>(NavAlgCtrlState::IGNORE)};

    feature.alg_status = state ? static_cast<int>(NavAlgCtrlState::ENABLE) : static_cast<int>(NavAlgCtrlState::DISABLE);

    feature_data.push_back(feature);

    if (feature_select_callback_ && !feature_data.empty())
    {
        feature_select_callback_(feature_data);
    }
}

void NavigationCrossRegionAlg::EdgeFollowDisable()
{
    DealFeatureSelect(ThreadControl::PERCEPTION_EDGE_THREAD, false);
}

void NavigationCrossRegionAlg::EdgeFollowEnable()
{
    DealFeatureSelect(ThreadControl::PERCEPTION_EDGE_THREAD, true);
}

void NavigationCrossRegionAlg::UpdateCrossRegionRunningState(CrossRegionRunningState state)
{
    if (cross_region_running_state_callback_)
    {
        cross_region_running_state_callback_(state);
    }
}

/**
 * @brief Phase 1. Find beacons along the edge
 *
 * @param mark_loc_result
 */
void NavigationCrossRegionAlg::FindBeaconsPhase_1(const MarkLocationResult &mark_loc_result)
{
    if (mark_loc_result.mark_perception_status == 0) // The perception does not detect the beacon
    {
        {
            std::lock_guard<std::mutex> lck(cross_region_mutex_);
            cross_region_state_ = CrossRegionRunningState::EDGE_FINDING_BEACON; // Add the state of finding the beacon along the edge
            UpdateCrossRegionRunningState(CrossRegionRunningState::EDGE_FINDING_BEACON);
        }

        LOG_INFO_THROTTLE(1000, "[CrossRegion] [Phase 1] 1. The perception does not detect the beacon, enable edge following");
        EdgeFollowEnable(); // Enable edge following
    }
    else // The perception detects the beacon
    {
        {
            std::lock_guard<std::mutex> lck(cross_region_mutex_);
            cross_region_state_ = CrossRegionRunningState::PER_FOUND_BEACON; // The perception localization has found the beacon
            UpdateCrossRegionRunningState(CrossRegionRunningState::PER_FOUND_BEACON);
        }

        LOG_INFO("[CrossRegion] [Phase 1] 2. The perception detects the beacon");
        if (mark_loc_result.mark_id_distance.size() <= 0) // The mark_id_distance of the localization has no value
        {
            LOG_INFO("[CrossRegion] [Phase 1] 2.1 The mark_id_distance of the localization has no value");
            HandleEdgePerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);
        }
        else // The mark_id_distance of the localization has a value
        {
            LOG_INFO("[CrossRegion] [Phase 1] 2.2 The mark_id_distance of the localization has a value");

            // Determine whether the beacon is valid. (mark_id_distance less than 50cm is considered that the current beacon is valid)
            int shortest_dis_inx = -1;
            std::vector<MarkIdDistance> mark_id_distance_vec = mark_loc_result.mark_id_distance;
            FingVaidBeaconIdx(mark_id_distance_vec, shortest_dis_inx);

            if (shortest_dis_inx == -1) // If the beacon is invalid. Do not operate, continue the previous action
            {
                LOG_INFO("[CrossRegion] [Phase 1]  2.2.1 The cross-region beacon is invalid");
                HandleEdgePerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);
            }
            else // If the beacon is valid. Detect whether the stack container is empty
            {
                LOG_INFO("[CrossRegion] [Phase 1]  2.2.2 The cross-region beacon is valid");
                LOG_INFO("[CrossRegion] [Phase 1]  2.2.2 The valid beacon is mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

                // Send the beacon id to the localization for cross-region
                SetMarkLocationMarkId(mark_id_distance_vec[shortest_dis_inx].mark_id);
                next_paired_beacon_id_ = PairNumber(mark_id_distance_vec[shortest_dis_inx].mark_id);
                LOG_INFO("[CrossRegion] [Phase 1]  2.2.2 The next pair of beacon id is {}", next_paired_beacon_id_);

                if (mark_loc_result.detect_status == 2 && mark_loc_result.mark_perception_direction == 0) // 小车正对信标
                {
                    LOG_INFO("[CrossRegion] [Phase 1]  2.2.2.1 The beacon can calculate the pose");

                    // Execute the cross-channel thread and close the edge following thread
                    LOG_INFO("[CrossRegion] [Phase 1]  2.2.2.1 Phase 1 is completed, start cross-region, close the edge following thread");
                    EdgeFollowDisable();
                    phase_1_completed_ = true;

                    // Reset the cooldown timestamp and activate the cooldown mechanism
                    last_cooldown_time_ = std::chrono::steady_clock::now();
                    is_cooldown_active_ = true;
                    // ResetAndActivateCooldown();
                }
                else
                {
                    LOG_INFO("[CrossRegion] [Phase 1]  2.2.2.2 Phase 1 is completed, start cross-region, perception and posture adjustment");
                    HandleEdgePerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);
                }
            }
        }
    }
}

void NavigationCrossRegionAlg::FindBeaconsPhase_41_New(MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result,
                                                       bool &cross_region_completed,
                                                       ImuData &imu_data, float &act_linear, float &act_angular)
{
    (void)act_angular;

    // Initialize channel passing distance tracking if not started
    if (!channel_distance_tracking_started_)
    {
        channel_distance_tracking_started_ = true;
        channel_accumulated_distance_ = 0.0f;
        channel_distance_start_time_ = std::chrono::steady_clock::now();
        LOG_INFO("[CrossRegion] [Phase 4] Started channel distance tracking, fixed distance: {:.2f}m", channel_fixed_distance_);
    }

    // Update accumulated distance during movement
    if (act_linear > 0.01f) // Only accumulate when actually moving forward
    {
        auto current_time = std::chrono::steady_clock::now();
        if (channel_last_distance_update_time_.time_since_epoch().count() > 0)
        {
            float dt = std::chrono::duration<float>(current_time - channel_last_distance_update_time_).count();
            float delta_distance = act_linear * dt;
            channel_accumulated_distance_ += delta_distance;

            LOG_INFO("[CrossRegion] [Phase 4] Channel distance: {:.2f}m / {:.2f}m",
                     channel_accumulated_distance_, channel_fixed_distance_);
        }
        channel_last_distance_update_time_ = current_time;
    }

    bool beacon_detect_completed = false;
    BeaconDetectedPhase_4(mark_loc_result, imu_data, beacon_detect_completed);

    if (beacon_detect_completed)
    {
        return;
    }

    // Check if exceeded fixed distance without beacon detection
    if (channel_accumulated_distance_ >= channel_fixed_distance_)
    {
        LOG_INFO("[CrossRegion] [Phase 4] Exceeded fixed channel distance ({:.2f}m) without beacon detection", channel_fixed_distance_);

        // Check if the current area is grass
        if (Nongrass2Grass(fusion_result))
        {
            LOG_INFO("[CrossRegion] [Phase 4] Current area is grass, starting timed movement with beacon detection");

            bool beacon_found = false;
            bool movement_completed = false;
            TimedMovementWithBeaconDetection(3.0f, mark_loc_result, imu_data, beacon_found, movement_completed);

            if (beacon_found)
            {
                LOG_INFO("[CrossRegion] [Phase 4] Beacon detected during timed movement, ending via beacon");
                return; // Exit via beacon detection
            }
            else if (movement_completed)
            {
                LOG_INFO("[CrossRegion] [Phase 4] Timed movement completed, ending cross-region");
                PublishVelocity(0.0, 0.0, 1000); // Stop
                cross_region_completed = true;

                {
                    std::lock_guard<std::mutex> lck(cross_region_mutex_);
                    cross_region_state_ = CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION; // Phase 4, non-grass to grass exit cross-region
                    UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION);
                }
                return; // Exit function
            }
        }
        else
        {
            LOG_INFO("[CrossRegion] [Phase 4] Current area is non-grass, continue straight movement");
            PublishVelocity(cross_region_linear_, 0); // Go straight
        }
    }
    else
    {
        // Within fixed distance and no beacon detected, continue straight
        LOG_INFO_THROTTLE(1000, "[CrossRegion] [Phase 4] No beacon detected, continue straight through channel");
        PublishVelocity(cross_region_linear_, 0); // Go straight
    }
}

void NavigationCrossRegionAlg::TimedMovementWithBeaconDetection(float duration_seconds, const MarkLocationResult &mark_loc_result,
                                                                ImuData &imu_data, bool &beacon_found, bool &movement_completed)
{
    beacon_found = false;
    movement_completed = false;

    // Initialize timing if not already started
    if (!timed_movement_in_progress_)
    {
        timed_movement_start_time_ = std::chrono::steady_clock::now();
        timed_movement_in_progress_ = true;
        LOG_INFO("[CrossRegion] [TimedMovement] Starting {:.1f}s timed movement with beacon detection", duration_seconds);
    }

    // Calculate elapsed time
    auto current_time = std::chrono::steady_clock::now();
    auto elapsed_duration = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - timed_movement_start_time_);
    float elapsed_seconds = elapsed_duration.count() / 1000.0f;

    // Check for beacon detection during movement
    bool beacon_detect_completed = false;
    BeaconDetectedPhase_4(mark_loc_result, imu_data, beacon_detect_completed);

    if (beacon_detect_completed)
    {
        LOG_INFO("[CrossRegion] [TimedMovement] Beacon detected at {:.2f}s, ending via beacon", elapsed_seconds);
        beacon_found = true;
        timed_movement_in_progress_ = false; // Reset timing state
        PublishVelocity(0.0, 0.0, 100);      // Stop immediately
        return;
    }

    // Check if time duration is completed
    if (elapsed_seconds >= duration_seconds)
    {
        LOG_INFO("[CrossRegion] [TimedMovement] {:.1f}s duration completed, continuing straight", duration_seconds);
        movement_completed = true;
        timed_movement_in_progress_ = false;      // Reset timing state
        PublishVelocity(cross_region_linear_, 0); // Continue straight movement
        return;
    }

    // Continue moving straight during the timed period
    LOG_INFO_THROTTLE(100, "[CrossRegion] [TimedMovement] Moving straight: {:.2f}s / {:.1f}s", elapsed_seconds, duration_seconds);
    PublishVelocity(cross_region_linear_, 0); // Move straight at cross_region_linear_ speed
}

void NavigationCrossRegionAlg::BeaconDetectedPhase_4(const MarkLocationResult &mark_loc_result, ImuData &imu_data, bool &beacon_detect_completed)
{
    beacon_detect_completed = false;

    // Check if beacon is detected within the fixed distance
    bool beacon_detected = (mark_loc_result.mark_perception_status == 1);

    if (beacon_detected)
    {
        LOG_INFO("[CrossRegion] [Phase 4] Beacon detected within channel distance");

        if (mark_loc_result.mark_id_distance.size() <= 0) // The mark_id_distance of the localization has no value
        {
            LOG_INFO("[CrossRegion] [Phase 4] 2.1 The mark_id_distance of the localization has no value");
            HandleCrossRegionPerceptionBeaconDetection(mark_loc_result, is_cooldown_active_, imu_data);

            beacon_detect_completed = true;
            return;
        }
        else // The mark_id_distance of the localization has a value
        {
            LOG_INFO("[CrossRegion] [Phase 4] 2.2 The mark_id_distance of the localization has a value");

            // Determine whether the beacon is valid. (mark_id_distance less than 50cm is considered that the current beacon is valid)
            int shortest_dis_inx = -1;
            std::vector<MarkIdDistance> mark_id_distance_vec = mark_loc_result.mark_id_distance;
            FingVaidBeaconIdx(mark_id_distance_vec, shortest_dis_inx);

            if (shortest_dis_inx == -1) // If the beacon is invalid. Do not operate, continue the previous action
            {
                LOG_INFO("[CrossRegion] [Phase 4] 2.2.1 The cross-region beacon is invalid");
                HandleCrossRegionPerceptionBeaconDetection(mark_loc_result, is_cooldown_active_, imu_data);

                beacon_detect_completed = true;
                return;
            }
            else // If the beacon is valid
            {
                LOG_INFO("[CrossRegion] 2.2.2 The cross-region beacon is valid");
                LOG_INFO("[CrossRegion] 2.2.2 The valid beacon is mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

                if (next_paired_beacon_id_ == mark_id_distance_vec[shortest_dis_inx].mark_id)
                {
                    LOG_INFO("[CrossRegion] [Phase 4] 2.2.2.1 Found the next beacon mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

                    // Reset the beacon pairing error state
                    beacon_pairing_error_active_ = false;
                    LOG_INFO("[CrossRegion] [Phase 4] The beacon pairing is correct, reset the error state");

                    // Send the beacon id to the localization for cross-region
                    SetMarkLocationMarkId(mark_id_distance_vec[shortest_dis_inx].mark_id);

                    if (mark_loc_result.detect_status == 2 && mark_loc_result.mark_perception_direction == 0) // The mower is facing the beacon
                    {
                        LOG_INFO("[CrossRegion] [Phase 4]  2.2.2.1.1 The beacon can calculate the pose");
                        LOG_INFO("[CrossRegion] [Phase 4] Phase 1 is completed");
                        phase_41_completed_ = true;

                        // Call the function to reset the cooldown mechanism
                        // Reset the cooldown timestamp and activate the cooldown mechanism
                        last_cooldown_time_ = std::chrono::steady_clock::now();
                        is_cooldown_active_ = true;
                        // ResetAndActivateCooldown();

                        beacon_detect_completed = true;
                        return; // Exit function after successful beacon detection
                    }
                    else
                    {
                        LOG_INFO("[CrossRegion] [Phase 4]  2.2.2.1.2 The beacon cannot calculate the pose");
                        HandleCrossRegionPerceptionBeaconDetection(mark_loc_result, is_cooldown_active_, imu_data);

                        beacon_detect_completed = true;
                        return; // Exit function
                    }
                }
                else
                {
                    LOG_ERROR("[CrossRegion] [Phase 4] 2.2.2.2 The next pair of beacons is incorrect");
                    PublishVelocity(0, 0, 100);

                    // If it is the first time a pairing error occurs, record the start time
                    if (!beacon_pairing_error_active_)
                    {
                        beacon_pairing_error_start_time_ = std::chrono::steady_clock::now();
                        beacon_pairing_error_active_ = true;
                        LOG_INFO("[CrossRegion] [Phase 4] Detected a beacon pairing error, start timing");
                    }
                    else
                    {
                        // Calculate the error duration
                        auto current_time = std::chrono::steady_clock::now();
                        auto duration = std::chrono::duration_cast<std::chrono::seconds>(
                            current_time - beacon_pairing_error_start_time_);

                        // If the error lasts more than 20s, publish an exception
                        if (duration.count() >= 20) // 20+0.1*20=22s
                        {
                            LOG_ERROR("[CrossRegion] [Phase 4] The beacon pairing error has lasted for more than 20s, publish an exception");
                            PublishException(SocExceptionLevel::ERROR,
                                             SocExceptionValue::ALG_PNC_CROSS_ZONE_BEACON_PAIRING_EXCEPTION);
                            // Reset the error state after publishing the exception
                            beacon_pairing_error_active_ = false;
                        }
                    }

                    beacon_detect_completed = true;
                    return; // Exit function
                }
            }
        }
    }
}

/**
 * @brief Phase 4. Find beacons in the cross-region
 *
 * @param mark_loc_result
 */
void NavigationCrossRegionAlg::FindBeaconsPhase_41(MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result,
                                                   bool &cross_region_completed,
                                                   ImuData &imu_data, float &act_linear, float &act_angular)
{
    (void)act_linear;
    (void)act_angular;
    // New: Continuously detect state changes regardless of whether the non-grass area is reached
    if (CrossRegionFinished(fusion_result))
    {
        LOG_INFO_THROTTLE(1000, "[CrossRegion] [Phase 4] Detected grass->non-grass->grass state transition");
        // ControlLinearMotion(cross_region_adjust_displace_, 0.0, cross_region_linear_, 1);

        // Continue to move straight for a certain distance, use the safe movement mode
        SafeLinearMotion(cross_region_adjust_displace_, 0.0,
                         cross_region_linear_, 1, mark_loc_result);

        if (!emergency_stop_)
        {
            LOG_INFO("[CrossRegion] Grass detected, cross-region algorithm execution finished");
            PublishVelocity(0.0, 0.0, 1000);
            cross_region_completed = true;

            {
                std::lock_guard<std::mutex> lck(cross_region_mutex_);
                cross_region_state_ = CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION; // Phase 4, non-grass to grass exit cross-region
                UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION);
            }

            return; // Return directly without executing subsequent logic
        }
    }

    // ====== New prerequisite: Must reach the non-grass area first ======
    if (!non_grass_area_reached_) /** Non-grass area has not been reached */
    {
        if (fusion_result.grass_detecte_status == GrassDetectStatus::NO_GRASS) /** Currently in the non-grass area */
        {
            non_grass_area_reached_ = true;          /** Non-grass area reached */
            grass_area_accumulated_distance_ = 0.0f; // Reset grass area distance when entering non-grass area
            LOG_INFO("[CrossRegion] [Phase 4] Non-grass area reached, start channel passing logic");

            {
                std::lock_guard<std::mutex> lck(cross_region_mutex_);
                cross_region_state_ = CrossRegionRunningState::STAGE4_NON_GRASS_REACHED; // Phase 4, non-grass area reached
                UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_NON_GRASS_REACHED);
            }
        }
        else /** Currently in the grass area */
        {
            // PublishVelocityAndInterferenceCorrection(imu_data);
            PublishVelocity(cross_region_linear_, 0);
            LOG_INFO("[CrossRegion] [Phase 4] Non-grass state not reached yet, grass_detecte_status: {}", static_cast<int>(fusion_result.grass_detecte_status));
        }
    }
    else /** Non-grass area reached */
    {
        if (is_first_non_grass_area_reached_ && !linear_motion_completed_)
        {
            // 开始首次直行
            if (!is_first_linear_in_progress_)
            {
                LOG_INFO("[第四阶段] 开始非阻塞直行 0.5m");
                linear_start_time_ = std::chrono::steady_clock::now();
                is_first_linear_in_progress_ = true;
            }

            // 计算已行驶时间
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - linear_start_time_);

            // 计算需要直行的时间：0.5m / 速度
            float required_time = 0.5 / cross_region_linear_ * 1000; // 转为毫秒

            if (duration.count() >= required_time)
            {
                LOG_INFO("[第四阶段] 完成非阻塞直行0.5m");
                // PublishZeroVelocity();
                linear_motion_completed_ = true;
                is_first_non_grass_area_reached_ = false; // 重置标志位
            }
            else
            {
                // 发布持续直行速度（非定时）
                LOG_INFO("[第四阶段] 发布速度: linear: %f, angular: %f", cross_region_linear_, 0.0);
                // PublishVelocityAndInterferenceCorrection(imu_data);
                PublishVelocity(cross_region_linear_, 0.0);
            }

            return; // 直行期间直接返回，不执行后续信标检测
        }

        // ====== Non-grass state has been reached, enter subsequent judgment ======
        if (mark_loc_result.mark_perception_status == 0) // The perception does not detect the beacon
        {
            LOG_INFO_THROTTLE(1000, "[CrossRegion] [Phase 4] 1. The perception does not detect the beacon");
            LOG_INFO_THROTTLE(1000, "[CrossRegion] [Phase 4] 1. Continue to move straight through the channel");

            // PublishVelocityAndInterferenceCorrection(imu_data);
            PublishVelocity(cross_region_linear_, 0); // Go straight
        }
        else // The perception detects the beacon
        {
            LOG_INFO("[CrossRegion] [Phase 4] 2. The perception detects the beacon");
            if (mark_loc_result.mark_id_distance.size() <= 0) // The mark_id_distance of the localization has no value
            {
                LOG_INFO("[CrossRegion] [Phase 4] 2.1 The mark_id_distance of the localization has no value");
                HandleCrossRegionPerceptionBeaconDetection(mark_loc_result, is_cooldown_active_, imu_data);
            }
            else // The mark_id_distance of the localization has a value
            {
                LOG_INFO("[CrossRegion] [Phase 4] 2.2 The mark_id_distance of the localization has a value");

                // Determine whether the beacon is valid. (mark_id_distance less than 50cm is considered that the current beacon is valid)
                int shortest_dis_inx = -1;
                std::vector<MarkIdDistance> mark_id_distance_vec = mark_loc_result.mark_id_distance;
                FingVaidBeaconIdx(mark_id_distance_vec, shortest_dis_inx);

                if (shortest_dis_inx == -1) // If the beacon is invalid. Do not operate, continue the previous action
                {
                    LOG_INFO("[CrossRegion] [Phase 4] 2.2.1 The cross-region beacon is invalid");
                    HandleCrossRegionPerceptionBeaconDetection(mark_loc_result, is_cooldown_active_, imu_data);
                }
                else // If the beacon is valid
                {
                    LOG_INFO("[CrossRegion] 2.2.2 The cross-region beacon is valid");
                    LOG_INFO("[CrossRegion] 2.2.2 The valid beacon is mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

                    if (next_paired_beacon_id_ == mark_id_distance_vec[shortest_dis_inx].mark_id)
                    {
                        LOG_INFO("[CrossRegion] [Phase 4] 2.2.2.1 Found the next beacon mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

                        // Reset the beacon pairing error state
                        beacon_pairing_error_active_ = false;
                        LOG_INFO("[CrossRegion] [Phase 4] The beacon pairing is correct, reset the error state");

                        // Send the beacon id to the localization for cross-region
                        SetMarkLocationMarkId(mark_id_distance_vec[shortest_dis_inx].mark_id);

                        if (mark_loc_result.detect_status == 2 && mark_loc_result.mark_perception_direction == 0) // The mower is facing the beacon
                        {
                            LOG_INFO("[CrossRegion] [Phase 4]  2.2.2.1.1 The beacon can calculate the pose");
                            LOG_INFO("[CrossRegion] [Phase 4] Phase 1 is completed");
                            phase_41_completed_ = true;

                            // Call the function to reset the cooldown mechanism
                            // Reset the cooldown timestamp and activate the cooldown mechanism
                            last_cooldown_time_ = std::chrono::steady_clock::now();
                            is_cooldown_active_ = true;
                            // ResetAndActivateCooldown();
                        }
                        else
                        {
                            LOG_INFO("[CrossRegion] [Phase 4]  2.2.2.1.2 The beacon cannot calculate the pose");
                            HandleCrossRegionPerceptionBeaconDetection(mark_loc_result, is_cooldown_active_, imu_data);
                        }
                    }
                    else
                    {
                        LOG_ERROR("[CrossRegion] [Phase 4] 2.2.2.2 The next pair of beacons is incorrect");
                        PublishVelocity(0, 0, 100);

                        // If it is the first time a pairing error occurs, record the start time
                        if (!beacon_pairing_error_active_)
                        {
                            beacon_pairing_error_start_time_ = std::chrono::steady_clock::now();
                            beacon_pairing_error_active_ = true;
                            LOG_INFO("[CrossRegion] [Phase 4] Detected a beacon pairing error, start timing");
                        }
                        else
                        {
                            // Calculate the error duration
                            auto current_time = std::chrono::steady_clock::now();
                            auto duration = std::chrono::duration_cast<std::chrono::seconds>(
                                current_time - beacon_pairing_error_start_time_);

                            // If the error lasts more than 20s, publish an exception
                            if (duration.count() >= 20) // 20+0.1*20=22s
                            {
                                LOG_ERROR("[CrossRegion] [Phase 4] The beacon pairing error has lasted for more than 20s, publish an exception");
                                PublishException(SocExceptionLevel::ERROR,
                                                 SocExceptionValue::ALG_PNC_CROSS_ZONE_BEACON_PAIRING_EXCEPTION);
                                // Reset the error state after publishing the exception
                                beacon_pairing_error_active_ = false;
                            }
                        }
                    }
                }
            }
        }
    }
}

void NavigationCrossRegionAlg::ResetAndActivateCooldown()
{
    // Reset the cooldown timestamp and activate the cooldown mechanism
    // last_cooldown_time_ = std::chrono::steady_clock::now();
    // is_cooldown_active_ = true;
}

bool NavigationCrossRegionAlg::SetMarkLocationMarkId(int mark_id)
{
    if (set_mark_id_callback_)
    {
        return set_mark_id_callback_(mark_id);
    }
    return false;
}

void NavigationCrossRegionAlg::SetMarkLocationMarkIdCallback(std::function<bool(int)> callback)
{
    set_mark_id_callback_ = callback;
}

void NavigationCrossRegionAlg::FingVaidBeaconIdx(const std::vector<MarkIdDistance> &mark_id_distance_vec, int &shortest_dis_inx)
{
    for (size_t i = 0; i < mark_id_distance_vec.size(); ++i)
    {
        if (mark_id_distance_vec[i].distance < mark_distance_threshold_) // 50cm
        {
            shortest_dis_inx = i;
        }
    }
}

void NavigationCrossRegionAlg::HandleEdgePerceptionBeaconDetection(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active)
{
    if (is_cooldown_active) // Cooldown mechanism is activated
    {
        LOG_INFO("[CrossRegion] Cooldown mechanism is activated");

        // Perception-driven cooldown time
        auto current_time = std::chrono::steady_clock::now();
        perception_drive_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_cooldown_time_);

        // Print the time difference
        LOG_INFO("[CrossRegion] The perception-driven cooldown timer along the edge (seconds): ({})", perception_drive_duration_.count());

        HandleEdgeCooldownMechanism(mark_loc_result, is_cooldown_active, perception_drive_duration_, perception_drive_cooldown_time_threshold_);
    }
    else // Cooldown mechanism is not activated
    {
        LOG_INFO("[CrossRegion] Cooldown mechanism is not activated");

        LOG_INFO("[CrossRegion] Enable perception-driven, and close edge following at the same time!");

        EdgeFollowDisable();
        PerceptionBasedAdjustment(mark_loc_result);
    }
}

void NavigationCrossRegionAlg::HandleEdgeCooldownMechanism(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active,
                                                           std::chrono::seconds &perception_drive_duration, int &perception_drive_cooldown_time_threshold) // Perception detection result
{
    if (perception_drive_duration.count() >= perception_drive_cooldown_time_threshold)
    {
        is_cooldown_active = false;
        LOG_INFO("[CrossRegion] The timing time exceeds ({}) seconds, the cooldown ends", perception_drive_cooldown_time_threshold);

        LOG_INFO("[CrossRegion] Enable perception-driven, and close edge following");
        EdgeFollowDisable();
        PerceptionBasedAdjustment(mark_loc_result);
    }
    else
    {
        // Cooldown is not over, skip execution
        LOG_INFO("[CrossRegion] The cooldown time is not over, it does not exceed ({}) seconds", perception_drive_cooldown_time_threshold);

        EdgeFollowEnable(); // Enable edge following
        LOG_INFO("[CrossRegion] Enable edge following");
    }
}

void NavigationCrossRegionAlg::HandleCrossRegionPerceptionBeaconDetection(const MarkLocationResult &mark_loc_result,
                                                                          bool &is_cooldown_active,
                                                                          ImuData &imu_data)
{
    if (is_cooldown_active) // Cooldown mechanism is activated
    {
        LOG_INFO("[CrossRegion] Cooldown mechanism is activated");

        // Perception-driven cooldown time
        auto current_time = std::chrono::steady_clock::now();
        perception_drive_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_cooldown_time_);

        // Print the time difference
        LOG_INFO("[CrossRegion] The perception-driven cooldown timer along the edge (seconds): ({})", perception_drive_duration_.count());
        HandleCrossRegionCooldownMechanism(mark_loc_result, is_cooldown_active,
                                           perception_drive_duration_, perception_drive_cooldown_time_threshold_, imu_data);
    }
    else // Cooldown mechanism is not activated
    {
        LOG_INFO("[CrossRegion] Cooldown mechanism is not activated");

        LOG_INFO("[CrossRegion] Enable perception-driven");

        PerceptionBasedAdjustment(mark_loc_result);
    }
}

void NavigationCrossRegionAlg::HandleCrossRegionCooldownMechanism(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active,
                                                                  std::chrono::seconds &perception_drive_duration,
                                                                  int &perception_drive_cooldown_time_threshold,
                                                                  ImuData &imu_data) // Perception detection result
{
    (void)imu_data;
    if (perception_drive_duration.count() >= perception_drive_cooldown_time_threshold)
    {
        is_cooldown_active = false;
        LOG_INFO("[CrossRegion] The timing time exceeds ({}) seconds, the cooldown ends!", perception_drive_cooldown_time_threshold);

        LOG_INFO("[CrossRegion] Enable perception-driven");
        PerceptionBasedAdjustment(mark_loc_result);
    }
    else
    {
        // Cooldown is not over, skip execution
        LOG_INFO("[CrossRegion] The cooldown time is not over, it does not exceed ({}) seconds", perception_drive_cooldown_time_threshold);

        // Drive the car to go straight through the channel
        LOG_INFO("[CrossRegion] Drive the car to go straight through the channel");
        PublishVelocity(cross_region_linear_, 0); // Go straight
        // PublishVelocityAndInterferenceCorrection(imu_data);
    }
}

/**
 * @brief
 *
 * @param detect_status
 * @return true Rotation in place is completed or QR code is detected
 * @return false Rotation in place is not completed
 */
bool NavigationCrossRegionAlg::ProcessPassageRotate(int mark_perception_status)
{
    bool ret = false;
    EdgeFollowDisable();

    // Calculate the rotation time in place
    if (passage_rotate_time_ == 0 && passage_rotate_begin_time_ == 0)
    {
        passage_rotate_time_ = (2 * M_PI / cross_region_angular_) * 1000; // Turning duration ms
        passage_rotate_begin_time_ = GetTimestampMs();
    }

    if (mark_perception_status == 1) // 0 means the beacon is not perceived (detected); 1 means the beacon is perceived (detected)
    {
        passage_rotate_time_ = 0;
        passage_rotate_begin_time_ = 0;
        LOG_INFO_THROTTLE(1000, "Passage rotate stage, find passage or QR code, stop rotate stage!");
        PublishVelocity(0, 0);
        return true;
    }

    // Execute the rotation process
    uint64_t run_time = GetTimestampMs() - passage_rotate_begin_time_;
    LOG_INFO_THROTTLE(1000, "Passage rotate stage, run time {} rotate_time {}!", run_time, passage_rotate_time_);
    if (run_time < passage_rotate_time_)
    {
        PublishVelocity(0, cross_region_angular_); // Rotate left
        ret = false;
    }
    else
    {
        LOG_INFO_THROTTLE(1000, "Recharge rotate stage finished!");
        passage_rotate_time_ = 0;
        passage_rotate_begin_time_ = 0;
        ret = true;
    }

    return ret;
}

/**
 * @brief Get the Precise Position Of Beacon object
 *
 * @param mark_loc_result
 * @param x_first
 * @param y_first
 * @param yaw_first
 * @param is_get_precise_position Whether the precise pose has been obtained
 * @note Drive close to the beacon until the precise QR code solution pose is obtained within the threshold range (close distance improves the accuracy of the solution)
 */
void NavigationCrossRegionAlg::GetPrecisePositionOfBeacon(MarkLocationResult &mark_loc_result,
                                                          float &x_first, float &y_first, float &yaw_first,
                                                          bool &is_get_precise_position)
{
    // If the solution is obtained, according to the solved pose, drive the car to reach the specified Euclidean distance range (close distance improves the accuracy of the solution)
    float x = mark_loc_result.xyzrpw.x;
    float y = mark_loc_result.xyzrpw.y;
    if (hypotf(x, y) > max_distance_threshold_)
    {
        // Drive the car to reach the maximum Euclidean distance range
        LOG_INFO("[CrossRegion] Drive the car to reach the maximum Euclidean distance range");
        PublishVelocity(cross_region_linear_, 0); // Move forward
    }
    else if (hypotf(x, y) < min_distance_threshold_)
    {
        // Drive the car to reach outside the minimum Euclidean distance range
        LOG_INFO("[CrossRegion] Drive the car to reach outside the minimum Euclidean distance range");
        PublishVelocity(-cross_region_linear_, 0); // Reverse
    }
    else
    {
        PublishVelocity(0.0, 0.0, 100); // Stop the car for 100ms

        // Assign initial values to x_first, y_first, yaw_first
        x_first = mark_loc_result.xyzrpw.x;
        y_first = mark_loc_result.xyzrpw.y;
        yaw_first = mark_loc_result.xyzrpw.w;

        // Add a loop to continue to obtain the latest QR code pose during sleep
        for (int i = 0; i < 30; ++i) // Get a new pose every 50ms, a total of 2 seconds
        {
            bool is_pose_resolved = false; // Whether the re-solution pose is successful

            {
                std::lock_guard<std::mutex> lck(mark_loc_mutex_);
                const float epsilon = 1e-6; // Tolerance range
                if (std::fabs(mark_loc_result_law_.xyzrpw.x + 1.0) < epsilon &&
                    std::fabs(mark_loc_result_law_.xyzrpw.y + 1.0) < epsilon) // The original beacon values x, y are both -1, indicating that detect_status is not 2, and it is not detected
                {
                    LOG_INFO("[CrossRegion] The original beacon values x, y are both -1, indicating that detect_status is not 2, and it is not detected");
                    is_pose_resolved = false;
                }
                else
                {
                    LOG_INFO("[CrossRegion] It is judged that detect_status is 2, and it is detected");
                    mark_loc_result = mark_loc_result_;
                    is_pose_resolved = true; // Re-solution pose is successful, execute coordinate transformation
                }
            }

            if (is_pose_resolved) // Execute coordinate transformation
            {
                x_first = mark_loc_result.xyzrpw.x;
                y_first = mark_loc_result.xyzrpw.y;
                yaw_first = mark_loc_result.xyzrpw.w;
            }

            LOG_INFO("[CrossRegion] Get the new solution pose x_first = ({}) , y_first = ({}) , yaw_first = ({})",
                     x_first, y_first, Radians2Degrees(yaw_first));

            // Sleep
            // std::this_thread::sleep_for(std::chrono::seconds(1));
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }

        is_get_precise_position = true; // Phase 2 is completed
        LOG_INFO("[CrossRegion] Complete getting the new solution pose x_first = ({}) , y_first = ({}) , yaw_first = ({})",
                 x_first, y_first, Radians2Degrees(yaw_first));
    }
}
/**
 * @brief Correct the car's steering when the beacon is outside the confidence range
 *
 * @param mark_loc_result
 */
void NavigationCrossRegionAlg::CorrectionOutsideConfidence(const MarkLocationResult &mark_loc_result)
{
    // Rotate according to the beacon direction given by the localization to find the high confidence area
    if (mark_loc_result.target_direction == static_cast<int>(Direction::LEFT_TURN)) // Turn left
    {
        PublishVelocity(0, cross_region_angular_);
        LOG_INFO("[CrossRegion] The beacon appears outside the confidence area! Turn left");
    }
    else if (mark_loc_result.target_direction == static_cast<int>(Direction::RIGHT_TURN)) // Turn right
    {
        PublishVelocity(0, -cross_region_angular_);
        LOG_INFO("[CrossRegion] The beacon appears outside the confidence area! Turn right");
    }
    else
    {
        LOG_INFO("[CrossRegion] Beacon confidence area detection failed");
    }
}

/**
 * @brief In the case of clockwise edge following. Adjust to pass_point based on the beacon
 *
 * @param x_first
 * @param y_first
 * @param yaw_first
 */
void NavigationCrossRegionAlg::ClockwiseAlignPassPoints(const float &x_first, const float &y_first, const float &yaw_first)
{
    float pass_point = channel_width_ / 2;
    // Determine whether the car is on the left or right side of the beacon. Clockwise edge following
    if (y_first >= 0) // On the left side of the beacon
    {
        // Determine whether the car is on the left or right side of the channel center
        if (y_first <= pass_point) // On the right side of the pass point
        {
            LOG_INFO("[CrossRegion] Phase 3! Beacon left, pass point right, turn left (or right) go straight turn right y_first = {}, yaw_first = {}",
                     y_first, Radians2Degrees(yaw_first));
            if (fabsf(pass_point - y_first) < dis_tolerance_)
            {
                // Turn left
                ControlRotaryMotion(0.0 + cross_region_angle_compensation_, yaw_first, cross_region_angular_);
            }
            else
            {
                // Pass point right.
                ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);
                // Move to the pass point (0, pass_point)
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, -1);
                // Turn right
                ControlRotaryMotion(0.0, -M_PI / 2, cross_region_angular_);
            }
        }
        else // On the left side of the pass point
        {
            LOG_INFO("[CrossRegion] Phase 3! Beacon left, pass point left, turn left (or right) go straight turn left y_first = {}, yaw_first = {}",
                     y_first, Radians2Degrees(yaw_first));
            if (fabsf(pass_point - y_first) < dis_tolerance_)
            {
                // Turn left
                ControlRotaryMotion(0.0 + cross_region_angle_compensation_, yaw_first, cross_region_angular_);
            }
            else
            {
                // Pass point right.
                ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);
                // Move to the pass point (0, channel_width_ / 2)
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);
                // Turn left
                ControlRotaryMotion(0.0, -M_PI / 2, cross_region_angular_);
            }
        }
    }
    else // On the right side of the beacon
    {
        // 1. First, according to the beacon coordinates, drive the car to the left side of its beacon
        if (x_first > adjust_mode_x_direction_threshold_) // Default -0.5
        {
            LOG_INFO("[CrossRegion] Phase 3! Beacon right, pass point right, turn right (left turn) go straight turn right straight line x_first = {}, y_first = {}, yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));

            if (fabsf(adjust_mode_x_direction_threshold_ - x_first) < dis_tolerance_)
            {
                // On the right side of the beacon and the pass point, turn right or left
                ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);
                // Move the car
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);
                // Turn right
                ControlRotaryMotion(0.0, M_PI / 2, cross_region_angular_);
            }
            else
            {
                // On the right side of the beacon and the pass point, turn right or left
                ControlRotaryMotion(M_PI, yaw_first, cross_region_angular_);
                // Move the car to (x_first - adjust_mode_x_direction_threshold_, y_first)
                ControlLinearMotion(adjust_mode_x_direction_threshold_, x_first, cross_region_linear_, 1);
                // Turn right
                ControlRotaryMotion(M_PI / 2, M_PI, cross_region_angular_);
                // Move the car
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);
                // Turn right
                ControlRotaryMotion(0.0, M_PI / 2, cross_region_angular_);
            }
        }
        else // Outside the -adjust_mode_x_direction_threshold_ range
        {
            LOG_INFO("[CrossRegion] Phase 3! Beacon right, pass point right, turn right (left turn) go straight x_first = {}, y_first = {}, yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));
            // On the right side of the beacon and the pass point, turn right or left
            ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);

            // Move the car to (x_first, pass_point - y_first)
            ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);

            // Turn right
            ControlRotaryMotion(0.0, M_PI / 2, cross_region_angular_);
        }
    }
}

/**
 * @brief Rotary motion control
 *
 * @param yaw_des
 * @param yaw_first
 */
void NavigationCrossRegionAlg::ControlRotaryMotion(const float &yaw_des, const float &yaw_first, const float &vel_angular)
{
    float sign = UnifyAngle(yaw_des - yaw_first) >= 0.0 ? 1.0 : -1.0; // Default rotation direction 1.0 turn left -1.0 turn right
    if (sign > 0)
    {
        LOG_INFO("[CrossRegion] Rotation direction: turn left");
    }
    else
    {
        LOG_INFO("[CrossRegion] Rotation direction: turn right");
    }

    float ang_err = fabsf(UnifyAngle(yaw_des - yaw_first));
    uint64_t t = (ang_err / vel_angular) * 1000; // Turning duration ms
    LOG_INFO("[CrossRegion] Rotation angle = {}", Radians2Degrees(ang_err));
    LOG_INFO("[CrossRegion] Angular velocity = {}, time = {}", sign * vel_angular, ang_err / vel_angular);
    PublishVelocity(0, sign * vel_angular, t);
}

/**
 * @brief Linear motion control
 *
 * @param pass_point
 * @param location
 */
void NavigationCrossRegionAlg::ControlLinearMotion(const float &pass_point, const float &location,
                                                   const float &vel_linear, const int &reverse)
{
    float dis = fabsf(pass_point - location);
    uint64_t t = (dis / vel_linear) * 1000;
    LOG_INFO("[CrossRegion] Straight line distance dis = {}", dis);
    LOG_INFO("[CrossRegion] Straight line speed = {}, time = {}", reverse * vel_linear, dis / vel_linear);
    PublishVelocity(reverse * vel_linear, 0, t);
}

void NavigationCrossRegionAlg::ControlProcessToPassPoints(const float &x_first, const float &y_first, const float &yaw_first)
{
    float pass_point = 0.55; // 0.375 Pass point: offset from the beacon in the y-axis direction
    // float yaw_des = M_PI / 4;     // 45 Expected adjustment angle
    // float yaw_compare = M_PI / 6; // 30 degrees (used for comparison, although not currently used)

    // Use a local variable to save the current angle for easy updating
    float current_yaw = yaw_first;
    bool need_adjust = false;
    float target_yaw = current_yaw;

    // Determine whether the direction needs to be adjusted
    // If the current angle is in the range of [0, 30°], the target angle is 45° ($$\pi/4$$)
    if (current_yaw >= 0 && current_yaw <= M_PI / 6)
    {
        target_yaw = M_PI / 4;
        need_adjust = true;
    }
    // If the current angle is in the range of [150°, 180°], the target angle is 135° ($$3\pi/4$$)
    else if (current_yaw >= 5 * M_PI / 6 && current_yaw <= M_PI)
    {
        target_yaw = 3 * M_PI / 4;
        need_adjust = true;
    }
    // If the current angle is in the range of [0, -30°], the target angle is -45° ($$-\pi/4$$)
    else if (current_yaw >= -M_PI / 6 && current_yaw < 0)
    {
        target_yaw = -M_PI / 4;
        need_adjust = true;
    }
    // If the current angle is in the range of [-180°, -150°], the target angle is -135° ($$-3\pi/4$$)
    else if (current_yaw <= -5 * M_PI / 6 && current_yaw >= -M_PI)
    {
        target_yaw = -3 * M_PI / 4;
        need_adjust = true;
    }

    // If the direction needs to be adjusted, perform the rotation first
    if (need_adjust)
    {
        LOG_INFO("[CrossRegion] Adjust direction: from {} to {}",
                 Radians2Degrees(current_yaw), Radians2Degrees(target_yaw));
        ControlRotaryMotion(target_yaw, current_yaw, cross_region_angular_);
        // Assume that after the rotation is completed, the direction of the car is updated
        current_yaw = target_yaw;
    }

    // Determine the position of the car relative to the beacon based on y_first, and determine which pass point to move to
    if (y_first <= 0) // The car is on the right side of the beacon
    {
        // Determine whether the car is on the left or right side of the pass point
        if (y_first <= -pass_point) // The car is on the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] Beacon right, and on the right side of the pass point: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            // Calculate the coordinates of the target point B, the y coordinate of the target pass point is -pass_point
            ComputeB(x_first, y_first, current_yaw, -pass_point, B_x, distance);
            // Move forward to the pass point
            ControlLinearMotion(distance, 0.0, cross_region_linear_, 1);
        }
        else // The car is on the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] Beacon right, and on the left side of the pass point: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, -pass_point, B_x, distance);
            // Move backward to the pass point
            ControlLinearMotion(distance, 0.0, cross_region_linear_, -1);
        }
    }
    else // The car is on the left side of the beacon
    {
        if (y_first <= pass_point) // The car is on the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] Beacon left, and on the right side of the pass point: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            // Calculate the coordinates of the target point B, the y coordinate of the target pass point is pass_point
            ComputeB(x_first, y_first, current_yaw, pass_point, B_x, distance);
            // Move backward to the pass point
            ControlLinearMotion(distance, 0.0, cross_region_linear_, -1);
        }
        else // The car is on the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] Beacon left, and on the left side of the pass point: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, pass_point, B_x, distance);
            // Move forward to the pass point
            ControlLinearMotion(distance, 0.0, cross_region_linear_, 1);
        }
    }

    // After moving to the pass point, adjust the direction of the car to 0 degrees
    LOG_INFO("[CrossRegion] Adjust direction to 0 degrees");
    ControlRotaryMotion(0.0, current_yaw, cross_region_angular_);
}

/**
 * @brief Calculate the x coordinate of point B and the y coordinates of points A and B
 *
 * @param A_x
 * @param A_y
 * @param yaw
 * @param B_y
 * @param B_x
 * @param distance
 */
void NavigationCrossRegionAlg::ComputeB(float A_x, float A_y, float yaw, float B_y, float &B_x, float &distance)
{
    // Define a very small constant to determine whether floating point numbers are close to 0
    const float EPS = 1e-6;

    // Determine whether sin(yaw) is large enough (not close to 0)
    if (std::fabs(std::sin(yaw)) > EPS)
    {
        // Calculate parameter t
        float t = (B_y - A_y) / std::sin(yaw);
        // Calculate the x coordinate of B according to t
        B_x = A_x + t * std::cos(yaw);
        // The Euclidean distance is equal to |t| (unit direction vector)
        distance = std::fabs(t);
    }
    else
    {
        // When sin(yaw) is approximately 0, it means that the line is a horizontal line
        // At this time, the y coordinates of A and B must be equal, otherwise B is not on the line
        if (std::fabs(B_y - A_y) > EPS)
        {
            LOG_ERROR("[CrossRegion] Error: For a horizontal line, the y coordinate of B must be equal to the y coordinate of A!");

            // Handle error situation: here set B_x to A_x and set the distance to 0
            B_x = A_x;
            distance = 0;
        }
        else
        {
            // When the y coordinates of A and B are the same, the x coordinate of point B on the horizontal line cannot be uniquely determined
            // Here, the default is B_x = A_x, and the distance is 0
            B_x = A_x;
            distance = 0;
        }
    }
}

/**
 * @brief In the case of counterclockwise edge following. Adjust to pass_point based on the beacon
 *
 * @param x_first
 * @param y_first
 * @param yaw_first
 * @note Edge following is counterclockwise
 */
void NavigationCrossRegionAlg::CounterClockwiseAlignPassPoints(const float &x_first, const float &y_first, const float &yaw_first)
{
    float pass_point = -channel_width_ / 2;

    // Determine whether the car is on the left or right side of the beacon. Counterclockwise edge following
    if (y_first <= 0) // On the right side of the beacon
    {
        // Determine whether the car is on the left or right side of the channel center
        if (y_first <= pass_point) // On the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] On the right side of the beacon, on the right side of the pass point, turn left (or right) go straight turn right y_first = {}, yaw_first = {}", y_first, Radians2Degrees(yaw_first));

            if (fabsf(pass_point - y_first) < dis_tolerance_)
            {
                // On the right side of the pass point. Turn right
                ControlRotaryMotion(0.0 + cross_region_angle_compensation_, yaw_first, cross_region_angular_);
            }
            else
            {
                // On the right side of the pass point. Turn left
                ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);

                // Move to the pass point (0, pass_point)
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);

                // Turn right
                LOG_INFO("[CrossRegion] [Phase 3] Turn right Rotation angle = {}", Radians2Degrees(M_PI / 2));
                ControlRotaryMotion(0.0, M_PI / 2, cross_region_angular_);
            }
        }
        else // On the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] On the right side of the beacon, on the left side of the pass point, turn left (turn right) go straight turn left y_first = {}, yaw_first = {}", y_first, Radians2Degrees(yaw_first));

            if (fabsf(pass_point - y_first) < dis_tolerance_)
            {
                // On the left side of the pass point. Turn right
                ControlRotaryMotion(0.0 + cross_region_angle_compensation_, yaw_first, cross_region_angular_);
            }
            else
            {
                // On the left side of the pass point. Turn right
                ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);

                // Move to the pass point (0, pass_point)
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, -1); // Reverse

                // Turn left
                LOG_INFO("[CrossRegion] [Phase 3] Turn left Rotation angle = {}", Radians2Degrees(M_PI / 2));
                ControlRotaryMotion(0.0, M_PI / 2, cross_region_angular_);
            }
        }
    }
    else // On the left side of the beacon
    {
        // 1. First, according to the beacon coordinates, drive the car to the right side of its beacon
        if (x_first > adjust_mode_x_direction_threshold_) // Default -adjust_mode_x_direction_threshold_
        {
            LOG_INFO("[CrossRegion] [Phase 3] On the left side of the beacon, on the left side of the pass point, turn right (turn left) go straight turn left straight line x_first = {}, y_first = {}, yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));

            if (fabsf(adjust_mode_x_direction_threshold_ - x_first) < dis_tolerance_)
            {
                // On the left side of the pass point. Turn right
                ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);

                // Move the car
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);

                // Turn left
                ControlRotaryMotion(0.0, -M_PI / 2, cross_region_angular_);
            }
            else
            {
                // On the left side of the beacon and the pass point, turn right or left
                ControlRotaryMotion(-M_PI, yaw_first, cross_region_angular_);

                // Move the car
                ControlLinearMotion(adjust_mode_x_direction_threshold_, x_first, cross_region_linear_, 1);

                // Turn left
                ControlRotaryMotion(-M_PI / 2, -M_PI, cross_region_angular_);

                // Move the car
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);

                // Turn left
                ControlRotaryMotion(0.0, -M_PI / 2, cross_region_angular_);
            }
        }
        else // Outside the -adjust_mode_x_direction_threshold_ range
        {
            LOG_INFO("[CrossRegion] [Phase 3] Beacon left, pass point left, turn right (turn left) go straight x_first = {}, y_first = {}, yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));

            // On the left side of the beacon and the pass point, turn right or left
            ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);

            // Move the car
            ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);

            // Turn left
            ControlRotaryMotion(0.0, -M_PI / 2, cross_region_angular_);
        }
    }
}
/**
 * @brief Determine whether to transition from non-grass to grass
 *
 * @param fusion_result
 * @return true
 * @return false
 */
bool NavigationCrossRegionAlg::Nongrass2Grass(const PerceptionFusionResult &fusion_result)
{
    LOG_INFO_THROTTLE(1000, "[CrossRegion] [Nongrass2Grass1] Processing transition from non-grass to grass");

    // Add the new state to the queue
    frames_.push_back(fusion_result.grass_detecte_status);

    // If the queue is full (exceeds 10 frames), remove the earliest frame
    if (frames_.size() >= 11)
    {
        frames_.pop_front();
    }

    /**
     * NO_GRASS = 0,                // No grass (all obstacles)
     * HAVE_GRASS_NO_OBSTACLE = 1,  // Grass without obstacles (all grass)
     * HAVE_GRASS_HAVE_OBSTACLE = 2 // Grass with obstacles (partial grass and partial obstacles)
     */

    if (frames_.size() >= 10)
    {
        size_t grass_count = 0; // Grass count
        for (const auto &status : frames_)
        {
            if (status == GrassDetectStatus::HAVE_GRASS_NO_OBSTACLE || status == GrassDetectStatus::HAVE_GRASS_HAVE_OBSTACLE) // Grass without obstacles (all grass)
            {
                grass_count++; // Grass
            }
        }
        LOG_INFO("[CrossRegion] [Nongrass2Grass1] Grass count grass_count({})", grass_count);
        if (int(grass_count) > grass_count_threshold_) // Determine if it is grass
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    // Less than 10 frames, cannot determine
    return false;
}

/**
 * @brief Determine whether to transition from grass to non-grass
 *
 * @param fusion_result
 * @return true
 * @return false
 */
bool NavigationCrossRegionAlg::Grass2Nongrass(const PerceptionFusionResult &fusion_result)
{
    LOG_INFO_THROTTLE(1000, "[CrossRegion] [Grass2Nongrass] Processing transition from grass to non-grass");

    // Add the new state to the queue
    frames_.push_back(fusion_result.grass_detecte_status);

    // If the queue is full (exceeds 10 frames), remove the earliest frame
    if (frames_.size() >= 11)
    {
        frames_.pop_front();
    }

    /**
     * NO_GRASS = 0,                // No grass (all obstacles)
     * HAVE_GRASS_NO_OBSTACLE = 1,  // Grass without obstacles (all grass)
     * HAVE_GRASS_HAVE_OBSTACLE = 2 // Grass with obstacles (partial grass and partial obstacles)
     */

    if (frames_.size() >= 10)
    {
        size_t nongrass_count = 0; // Non-grass count
        for (const auto &status : frames_)
        {
            if (status == GrassDetectStatus::NO_GRASS) // No grass (all obstacles)
            {
                nongrass_count++; // Non-grass
            }
        }
        LOG_INFO("[CrossRegion] [Grass2Nongrass] Non-grass count nongrass_count({})", nongrass_count);
        if (int(nongrass_count) > grass_count_threshold_) // Determine if it is non-grass
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    // Less than 10 frames, cannot determine
    return false;
}

/**
 * @brief Determine whether the lawn mower's cross-region operation is finished, i.e., whether it has experienced a grass → non-grass → grass transition
 *
 * Explanation:
 * 1. Use a deque frames_ to store the most recent grass detection states. When the queue length exceeds 20 frames, automatically discard the oldest data.
 * 2. Use a voting method with the most recent $$window\_size$$ (e.g., 5) frames to determine whether the current area is grass:
 *    - If $$grass\_count \geq nongrass\_count$$, consider the current area as grass; otherwise, consider it as non-grass.
 * 3. State machine logic:
 *    - If the current state is IN_GRASS, when it is detected that the current area is non-grass, switch to the CROSSING state.
 *    - If the current state is CROSSING, when it is detected that the current area is grass again, it indicates that a cross-region operation has been experienced (grass → non-grass → grass), return true, and reset the state to IN_GRASS.
 *
 * @param fusion_result Perception fusion result, where fusion_result.grass_detecte_status represents the current grass detection state
 * @return true  Indicates that the cross-region operation is finished (i.e., the grass → non-grass → grass transition is completed)
 * @return false Indicates that the cross-region operation is not completed
 */
bool NavigationCrossRegionAlg::CrossRegionFinished(const PerceptionFusionResult &fusion_result)
{
    LOG_INFO_THROTTLE(1000, "[CrossRegion] [CrossRegionFinished] Processing grass → non-grass → grass cross-region determination");

    // Add the current detection state to the queue
    frames_.push_back(fusion_result.grass_detecte_status);

    // When the queue length exceeds 20 frames, remove the oldest frame
    if (frames_.size() > 20)
    {
        frames_.pop_front();
    }

    // Set the window size for determining the current area, e.g., take the most recent 10 frames of data
    const size_t window_size = 10;
    size_t effective_size = std::min(frames_.size(), window_size);
    size_t grass_count = 0;    // $$grass\_count$$: Grass count
    size_t nongrass_count = 0; // Non-grass count

    // Iterate through the most recent effective_size frames of data for voting
    for (size_t i = frames_.size() - effective_size; i < frames_.size(); ++i)
    {
        // If the detection state is grass (including with or without obstacles, both are considered grass)
        if (frames_[i] == GrassDetectStatus::HAVE_GRASS_NO_OBSTACLE || frames_[i] == GrassDetectStatus::HAVE_GRASS_HAVE_OBSTACLE)
        {
            grass_count++;
        }
        // If the detection state is no grass
        else if (frames_[i] == GrassDetectStatus::NO_GRASS)
        {
            nongrass_count++;
        }
    }

    // Determine the current area based on majority voting: if the grass count is higher, consider the current area as grass
    bool current_is_grass = (grass_count >= nongrass_count);
    LOG_INFO("[CrossRegion] Recent ({}) frames: grass_count = ({}), nongrass_count = ({}), current_is_grass = ({})",
             effective_size, grass_count, nongrass_count, current_is_grass ? "true" : "false");

    // State machine logic implementation:
    // 1. The initial state is IN_GRASS. When it is detected that the current area is non-grass, it indicates that the lawn mower has left the grass area and switches to the CROSSING state.
    // 2. When in the CROSSING state, if it is detected that the current area is grass again, it is considered that the cross-region operation is completed.
    if (current_state_ == RegionState::IN_GRASS)
    {
        if (!current_is_grass)
        {
            current_state_ = RegionState::CROSSING;
            LOG_INFO("[CrossRegion] State transitioned from IN_GRASS to CROSSING");
        }
    }
    else if (current_state_ == RegionState::CROSSING)
    {
        if (current_is_grass)
        {
            // Cross-region completed: experienced grass → non-grass → grass
            LOG_INFO("[CrossRegion] Detected the end of the cross-region operation (non-grass returned to grass)");
            // Reset the state to IN_GRASS for the next detection
            current_state_ = RegionState::IN_GRASS;
            // Clear the queue to prevent historical data from interfering with subsequent judgments
            frames_.clear();
            return true;
        }
    }

    // If the state does not meet the conditions for completing the cross-region operation, return false
    return false;
}

/**
 * @brief From the perspective of PassPoints
 *
 * @param x_first
 * @param y_first
 * @param yaw_first
 */
void NavigationCrossRegionAlg::ControlProcessToEndPoints(const float &x_first, const float &y_first, const float &yaw_first)
{
    float end_point = 0.55; // 0.375 Pass point: offset from the beacon in the y-axis direction
    // float yaw_des = M_PI / 4;     // 45 Expected adjustment angle
    // float yaw_compare = M_PI / 6; // 30 degrees (used for comparison, although currently unused)

    // Use a local variable to save the current angle for easy updating
    float current_yaw = yaw_first;
    bool need_adjust = false;
    float target_yaw = current_yaw;

    // Determine whether the direction needs to be adjusted
    // If the current angle is in the range of [0, 30°], the target angle is 45° ($$\pi/4$$)
    if (current_yaw >= 0 && current_yaw <= M_PI / 6)
    {
        target_yaw = M_PI / 4;
        need_adjust = true;
    }
    // If the current angle is in the range of [150°, 180°], the target angle is 135° ($$3\pi/4$$)
    else if (current_yaw >= 5 * M_PI / 6 && current_yaw <= M_PI)
    {
        target_yaw = 3 * M_PI / 4;
        need_adjust = true;
    }
    // If the current angle is in the range of [0, -30°], the target angle is -45° ($$-\pi/4$$)
    else if (current_yaw >= -M_PI / 6 && current_yaw < 0)
    {
        target_yaw = -M_PI / 4;
        need_adjust = true;
    }
    // If the current angle is in the range of [-180°, -150°], the target angle is -135° ($$-3\pi/4$$)
    else if (current_yaw <= -5 * M_PI / 6 && current_yaw >= -M_PI)
    {
        target_yaw = -3 * M_PI / 4;
        need_adjust = true;
    }

    // If the direction needs to be adjusted, perform the rotation first
    if (need_adjust)
    {
        LOG_INFO("[CrossRegion] Adjust direction: from {} to {}",
                 Radians2Degrees(current_yaw), Radians2Degrees(target_yaw));
        ControlRotaryMotion(target_yaw, current_yaw, cross_region_angular_);
        // Assume that after the rotation is completed, the direction of the car is updated
        current_yaw = target_yaw;
    }

    // Determine the position of the car relative to the beacon based on y_first
    if (y_first <= 0) // On the left side of the beacon
    {
        // Determine whether the car is on the left or right side of the pass point
        if (y_first <= -end_point) // On the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] On the left side of the beacon, on the left side of the pass point y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, -end_point, B_x, distance); // The position of the car is point A, and the pass point is point B

            // Move to the pass point
            ControlLinearMotion(distance, 0.0, cross_region_linear_, 1);
        }
        else // On the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] On the left side of the beacon, on the right side of the pass point y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, -end_point, B_x, distance); // The position of the car is point A, and the pass point is point B

            // Move to the pass point
            ControlLinearMotion(distance, 0.0, cross_region_linear_, -1); // Reverse
        }
    }
    else // On the right side of the beacon
    {
        // Determine whether the car is on the left or right side of the pass point
        if (y_first <= end_point) // On the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] On the right side of the beacon, on the left side of the pass point y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, end_point, B_x, distance); // The position of the car is point A, and the pass point is point B

            // Move to the pass point
            ControlLinearMotion(distance, 0.0, cross_region_linear_, -1); // Reverse
        }
        else // On the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] On the right side of the beacon, on the right side of the pass point y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, end_point, B_x, distance); // The position of the car is point A, and the pass point is point B

            // Move to the pass point
            ControlLinearMotion(distance, 0.0, cross_region_linear_, 1);
        }
    }

    // After moving to the pass point, adjust the direction of the car to 0 degrees
    LOG_INFO("[CrossRegion] Adjust direction to M_PI degrees");
    ControlRotaryMotion(M_PI, current_yaw, cross_region_angular_);
}

/**
 * @brief In the case of counterclockwise edge following. Adjust to end_point + channel_stop_pose_x_ based on the beacon
 *
 * @param x_first
 * @param y_first
 * @param yaw_first
 * @note 1. The edge following is counterclockwise 2. The base coordinate system is the beacon
 */
void NavigationCrossRegionAlg::CounterClockwiseAlignEndPoints(const float &x_first, const float &y_first, const float &yaw_first)
{
    float end_point = -channel_width_ / 2;

    // Determine whether the car is on the left or right side of the beacon. Counterclockwise edge following
    if (y_first <= 0) // On the right side of the beacon
    {
        // Determine whether the car is on the left or right side of the channel center
        if (y_first <= end_point) // On the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 4] On the right side of the beacon, on the right side of the pass point, turn left (or right) go straight turn left y_first = {} , yaw_first = {}", y_first, Radians2Degrees(yaw_first));

            if (fabsf(end_point - y_first) < dis_tolerance_)
            {
                // On the right side of the pass point. Turn right
                ControlRotaryMotion(M_PI, yaw_first, cross_region_angular_);
            }
            else
            {
                // On the right side of the pass point. Turn left
                ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);

                // Move to the pass point (0, end_point)
                ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);

                // Turn left
                LOG_INFO("[CrossRegion] [Phase 4] Turn left Rotation angle = {}", Radians2Degrees(M_PI / 2));
                ControlRotaryMotion(M_PI, M_PI / 2, cross_region_angular_);
            }
        }
        else // On the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 4] On the right side of the beacon, on the left side of the pass point, turn left (turn right) go straight turn right y_first = {} , yaw_first = {}", y_first, Radians2Degrees(yaw_first));

            if (fabsf(end_point - y_first) < dis_tolerance_)
            {
                // On the left side of the pass point. Turn right
                ControlRotaryMotion(M_PI, yaw_first, cross_region_angular_);
            }
            else
            {
                // On the left side of the pass point. Turn right
                ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);

                // Move to the pass point (0, end_point)
                ControlLinearMotion(end_point, y_first, cross_region_linear_, -1); // Reverse

                // Turn right
                LOG_INFO("[CrossRegion] [Phase 4] Turn right Rotation angle = {}", Radians2Degrees(M_PI / 2));
                ControlRotaryMotion(M_PI, M_PI / 2, cross_region_angular_);
            }
        }
    }
    else // On the left side of the beacon
    {
        // 1. First, according to the beacon coordinates, drive the car to the right side of its beacon
        if (x_first < -adjust_mode_x_direction_threshold_) // Default adjust_mode_x_direction_threshold_
        {
            LOG_INFO("[CrossRegion] [Phase 4] On the left side of the beacon, on the left side of the pass point, turn right (turn left) go straight turn right straight line x_first = {} , y_first = {} , yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));

            if (fabsf(-adjust_mode_x_direction_threshold_ - x_first) < dis_tolerance_)
            {
                // Turn left
                ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);

                // Move the car to (x_first, y_first - pass_point)
                ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);

                // Turn right
                ControlRotaryMotion(-M_PI, -M_PI / 2, cross_region_angular_);
            }
            else
            {
                // On the left side of the beacon and the pass point, turn right or left
                ControlRotaryMotion(-M_PI, yaw_first, cross_region_angular_);

                // Move the car to (x_first - (-adjust_mode_x_direction_threshold_), y_first)
                ControlLinearMotion(-adjust_mode_x_direction_threshold_, x_first, cross_region_linear_, -1);

                // Turn right
                ControlRotaryMotion(-M_PI / 2, -M_PI, cross_region_angular_);

                // Move the car to (x_first - (-adjust_mode_x_direction_threshold_), y_first - pass_point)
                ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);

                // Turn right
                ControlRotaryMotion(-M_PI, -M_PI / 2, cross_region_angular_);
            }
        }
        else // Outside the 0.5 range
        {
            LOG_INFO("[CrossRegion] [Phase 4] Beacon left, pass point left, turn right (turn left) go straight x_first = {} , y_first = {} , yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));

            // On the left side of the beacon and the pass point, turn right or left
            ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);

            // Move the car to (x_first, y_first - pass_point)
            ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);

            // Turn right
            ControlRotaryMotion(-M_PI, -M_PI / 2, cross_region_angular_);
        }
    }
}

/**
 * @brief In the case of clockwise edge following. Adjust to end_point + channel_stop_pose_x_ based on the beacon
 *
 * @param x_first
 * @param y_first
 * @param yaw_first
 */
void NavigationCrossRegionAlg::ClockwiseAlignEndPoints(const float &x_first, const float &y_first, const float &yaw_first)
{
    float end_point = channel_width_ / 2;
    // Determine whether the car is on the left or right side of the beacon. Clockwise edge following
    if (y_first >= 0) // On the left side of the beacon
    {
        // Determine whether the car is on the left or right side of the channel center
        if (y_first <= end_point) // On the right side of the pass point
        {
            LOG_INFO("[CrossRegion] Phase 4! Beacon left, pass point right, turn left (or right) go straight turn left y_first = {} , yaw_first = {}", y_first, Radians2Degrees(yaw_first));

            if (fabsf(end_point - y_first) < dis_tolerance_)
            {
                // On the right side of the pass point. Turn right
                ControlRotaryMotion(-M_PI, yaw_first, cross_region_angular_);
            }
            else
            {
                // On the right side of the pass point.
                ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);
                // Move to the pass point (0, end_point)
                ControlLinearMotion(end_point, y_first, cross_region_linear_, -1);
                // Turn left
                ControlRotaryMotion(-M_PI, -M_PI / 2, cross_region_angular_);
            }
        }
        else // On the left side of the pass point
        {
            LOG_INFO("[CrossRegion] Phase 3! Beacon left, pass point left, turn left (or right) go straight turn right y_first = {} , yaw_first = {}", y_first, Radians2Degrees(yaw_first));
            if (fabsf(end_point - y_first) < dis_tolerance_)
            {
                // On the right side of the pass point. Turn right
                ControlRotaryMotion(-M_PI, yaw_first, cross_region_angular_);
            }
            else
            {
                // On the right side of the pass point.
                ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);
                // Move to the pass point (0, channel_width_ / 2)
                ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);
                // Turn right
                ControlRotaryMotion(-M_PI, -M_PI / 2, cross_region_angular_);
            }
        }
    }
    else // On the right side of the beacon
    {
        // 1. First, according to the beacon coordinates, drive the car to the left side of its beacon
        if (x_first < -adjust_mode_x_direction_threshold_) // Default adjust_mode_x_direction_threshold_
        {
            LOG_INFO("[CrossRegion] Phase 3! Beacon right, pass point right, turn right (turn left) go straight turn left straight line x_first = {} , y_first = {} , yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));
            if (fabsf(adjust_mode_x_direction_threshold_ - x_first) < dis_tolerance_)
            {
                // Turn left
                ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);

                // Move the car to (x_first - adjust_mode_x_direction_threshold_, pass_point - y_first)
                ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);

                // Turn left
                ControlRotaryMotion(M_PI, M_PI / 2, cross_region_angular_);
            }
            else
            {
                // On the right side of the beacon and the pass point, turn right or left
                ControlRotaryMotion(M_PI, yaw_first, cross_region_angular_);

                // Move the car to (x_first - adjust_mode_x_direction_threshold_, y_first)
                ControlLinearMotion(-adjust_mode_x_direction_threshold_, x_first, cross_region_linear_, -1);

                // Turn left
                ControlRotaryMotion(M_PI / 2, M_PI, cross_region_angular_);

                // Move the car to (x_first - adjust_mode_x_direction_threshold_, pass_point - y_first)
                ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);

                // Turn left
                ControlRotaryMotion(M_PI, M_PI / 2, cross_region_angular_);
            }
        }
        else // Outside the -0.5 range
        {
            LOG_INFO("[CrossRegion] Phase 3! Beacon right, pass point right, turn right (turn left) go straight x_first = {} , y_first = {} , yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));
            // On the right side of the beacon and the pass point, turn right or left
            ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);
            // Move the car to (x_first, pass_point - y_first)
            ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);
            // Turn left
            ControlRotaryMotion(M_PI, M_PI / 2, cross_region_angular_);
        }
    }
}

/**
 * @brief Adjust direction based on perception
 *
 * @param mark_loc_result
 */
void NavigationCrossRegionAlg::PerceptionBasedAdjustment(const MarkLocationResult &mark_loc_result)
{
    switch (mark_loc_result.mark_perception_direction)
    {
    case -1: // Leftward deviation, turn left
        LOG_INFO("Adjust direction leftward based on perception!");
        PublishVelocity(0, cross_region_special_angular_);
        break;

    case 0: // Centered, go straight
        LOG_INFO("Adjust direction straight based on perception!");
        PublishVelocity(cross_region_special_linear_, 0);
        break;

    case 1: // Rightward deviation, turn right
        LOG_INFO("Adjust direction rightward based on perception!");
        PublishVelocity(0, -cross_region_special_angular_);
        break;

    default:
        LOG_INFO("Error in the mark_perception_direction flag based on perception!");
        PublishVelocity(cross_region_special_linear_, 0);
        break;
    }
}

/**
 * @brief Pair positive integers
 *
 * Rule description:
 * If the input positive integer n is odd, return n+1;
 * If n is even, return n-1.
 * The formula is expressed as follows:
 * $$\text{If } n \% 2 == 1, \text{ then output } n+1$$
 * $$\text{If } n \% 2 == 0, \text{ then output } n-1$$
 *
 * @param n Positive integer
 * @return int Paired value
 */
int NavigationCrossRegionAlg::PairNumber(int n)
{
    // Determine if n is odd
    if (n % 2 == 1)
    {
        // If n is odd, return n+1
        return n + 1;
    }
    else
    {
        // If n is even, return n-1
        return n - 1;
    }
}
// Modified safe movement control function
void NavigationCrossRegionAlg::SafeLinearMotion(float target_dis, float current_dis,
                                                float vel_linear, int reverse,
                                                MarkLocationResult &mark_loc_result)
{
    const float step = 0.005f; // Move 0.5cm each time before checking
    float remaining = fabs(target_dis - current_dis);
    front_beacon_detected_ = false;

    while (remaining > 0 && !emergency_stop_)
    {
        LOG_INFO("[CrossRegion] Safe movement control executing");

        // Check the latest beacon status before each movement
        mark_loc_result = mark_loc_result_;
        CheckFrontBeaconCollision(mark_loc_result);

        if (front_beacon_detected_)
        {
            LOG_WARN("[CrossRegion] Detected a valid front beacon, interrupting straight movement process");
            emergency_stop_ = true;
            non_grass_area_reached_ = true; // Force entry into the non-grass processing flow

            PublishVelocity(0.0, 0.0, 100);
            break;
        }

        // Execute a small segment of movement
        float move_dis = std::min(remaining, step);
        uint64_t t = (move_dis / vel_linear) * 1000;
        PublishVelocity(reverse * vel_linear, 0, t);
        remaining -= move_dis;

        LOG_INFO("[CrossRegion] Movement completed | New remaining distance: {:.2f}m", remaining);
    }
}

// Safe linear motion with non-grass detection
void NavigationCrossRegionAlg::SafeLinearMotionWithNonGrassDetection(float target_dis, float current_dis,
                                                                     float vel_linear, int reverse,
                                                                     MarkLocationResult &mark_loc_result,
                                                                     PerceptionFusionResult &fusion_result,
                                                                     bool &motion_completed)
{
    const float step = 0.005f; // Move 0.5cm each time before checking
    float remaining = fabs(target_dis - current_dis);
    front_beacon_detected_ = false;
    motion_completed = false;

    while (remaining > 0 && !emergency_stop_)
    {
        LOG_INFO("[CrossRegion] Safe movement with non-grass detection executing");

        // Get latest fusion result if callback is available
        PerceptionFusionResult current_fusion_result = fusion_result;

        // Check for non-grass terrain first
        if (Grass2Nongrass(fusion_result))
        {
            LOG_WARN("[CrossRegion] Non-grass terrain detected, stopping immediately");
            PublishVelocity(0.0, 0.0, 100);
            motion_completed = true;
            break;
        }

        // Check the latest beacon status before each movement
        mark_loc_result = mark_loc_result_;
        CheckFrontBeaconCollision(mark_loc_result);

        if (front_beacon_detected_)
        {
            LOG_WARN("[CrossRegion] Detected a valid front beacon, interrupting straight movement process");
            emergency_stop_ = true;
            non_grass_area_reached_ = true; // Force entry into the non-grass processing flow

            PublishVelocity(0.0, 0.0, 100);
            motion_completed = true;
            break;
        }

        // Execute a small segment of movement
        float move_dis = std::min(remaining, step);
        uint64_t t = (move_dis / vel_linear) * 1000;
        PublishVelocity(reverse * vel_linear, 0, t);
        remaining -= move_dis;

        LOG_INFO("[CrossRegion] Movement completed | New remaining distance: {:.2f}m", remaining);
    }

    // If we completed the full distance without detecting non-grass
    if (remaining <= 0)
    {
        motion_completed = true;
        LOG_INFO("[CrossRegion] Completed full distance movement without non-grass detection");
    }
}

// New collision detection logic
void NavigationCrossRegionAlg::CheckFrontBeaconCollision(const MarkLocationResult &mark_loc_result)
{
    // Validity check
    if (mark_loc_result.mark_id_distance.empty())
    {
        LOG_INFO("[CrossRegion] mark_id_distance is empty");
        front_beacon_detected_ = false;
        return;
    }

    // Determine whether the beacon is valid. (mark_id_distance less than 50cm is considered that the current beacon is valid)
    int shortest_dis_inx = -1;
    std::vector<MarkIdDistance> mark_id_distance_vec = mark_loc_result.mark_id_distance;
    FingVaidBeaconIdx(mark_id_distance_vec, shortest_dis_inx);

    if (shortest_dis_inx == -1) // If the beacon is invalid. Do not operate, continue the previous action
    {
        LOG_INFO("[CrossRegion] mark_id_distance is invalid");
        front_beacon_detected_ = false;
        return;
    }
    else // If the beacon is valid
    {
        LOG_INFO("[CrossRegion] mark_id_distance is valid");
        LOG_INFO("[CrossRegion] The valid beacon is mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

        if (next_paired_beacon_id_ == mark_id_distance_vec[shortest_dis_inx].mark_id)
        {
            LOG_INFO("[CrossRegion] Found the next beacon mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

            // Send the beacon id to the localization for cross-region
            SetMarkLocationMarkId(mark_id_distance_vec[shortest_dis_inx].mark_id);

            if (mark_loc_result.detect_status == 2 && mark_loc_result.mark_perception_direction == 0) // The mower is facing the beacon
            {
                LOG_INFO("[CrossRegion] The beacon can calculate the pose");

                front_beacon_detected_ = true;
                return;
            }
        }
        else
        {
            LOG_ERROR("[CrossRegion] The next pair of beacons is incorrect");
        }
    }

    front_beacon_detected_ = false;
}

void NavigationCrossRegionAlg::PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value)
{
    if (pub_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.node_name = "navigation_cross_region_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_exception_->publishCopyOf(exception)
            .or_else([](auto &error) {
                LOG_ERROR("Navigation cross_region publish soc exception Unable to publishCopyOf, error: {}", error);
            });
    }
}

void NavigationCrossRegionAlg::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_exception_ = std::make_unique<iox_exception_publisher>(
        iox::capro::ServiceDescription{kSocExceptionIox[0],
                                       kSocExceptionIox[1],
                                       kSocExceptionIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
}

void NavigationCrossRegionAlg::PublishVelocityAndInterferenceCorrection(ImuData &imu_data)
{
    // 如果IMU处理器可用且已校准，使用处理器的数据
    if (imu_processor_ && imu_processor_->IsBiasCalibrated())
    {
        // 使用IMU处理器处理的数据进行干扰检测和纠正
        float current_yaw = imu_processor_->getCurrentYaw();

        // 简化的干扰检测逻辑，基于处理器的滤波数据
        // 这里可以根据需要实现更复杂的干扰检测和纠正逻辑

        LOG_DEBUG("[CrossRegion] Using IMU processor data: current_yaw={:.2f}°",
                  current_yaw * 180.0f / M_PI);

        // 发布直行速度（干扰纠正逻辑可以在这里实现）
        PublishVelocity(cross_region_linear_, 0.0);
        return;
    }

    // 回退到原始的IMU处理逻辑（保持向后兼容性）
    // 计算时间步长
    auto current_time = std::chrono::steady_clock::now();
    float dt = 0.0f;
    if (!is_first_imu_)
    {
        dt = std::chrono::duration<float>(current_time - last_imu_time_).count();
    }
    else
    {
        is_first_imu_ = false; // 标记已处理第一次数据
        LOG_INFO("[CrossRegion] 忽略第一次 IMU 数据，dt = 0");
        last_imu_time_ = current_time;
        last_imu_timestamp_ = imu_data.system_timestamp;
        PublishVelocity(cross_region_linear_, 0.0); // 继续直行
        return;                                     // 跳过第一次处理
    }
    last_imu_time_ = current_time;
    last_imu_timestamp_ = imu_data.system_timestamp;

    // 校准零偏（启动时）
    if (!is_bias_calibrated_)
    {
        CalibrateImuBias(imu_data);
        PublishVelocity(cross_region_linear_, 0.0); // 校准期间继续直行
        return;
    }

    // 减去零偏并应用阈值滤波
    float angular_velocity_z = imu_data.angular_velocity_z - bias_z_;
    if (std::abs(angular_velocity_z) < bias_threshold_)
    {
        angular_velocity_z = 0.0f; // 滤除微小角速度
    }

    LOG_WARN("[CrossRegion] 当前角速度：{:.2f} 度/秒 [{:.2f} rad/s]", angular_velocity_z * 180.0 / M_PI, angular_velocity_z);

    yaw_current_ += angular_velocity_z * dt;
    LOG_INFO("[CrossRegion] 当前航向角：{:.2f} 度 [{:.2f} rad]", yaw_current_ * 180.0 / M_PI, yaw_current_);

    // 第一步：检测干扰
    if (!is_correcting_)
    {
        if (std::abs(angular_velocity_z) > threshold_angular_velocity_)
        {
            // 第二步：累积角度
            accumulated_angle_ += angular_velocity_z * dt;
            LOG_ERROR("[CrossRegion] 检测到干扰，累积角度：{:.2f} 度",
                      accumulated_angle_ * 180.0 / M_PI);

            if (std::abs(accumulated_angle_) > threshold_accumulated_angle_)
            {
                is_correcting_ = true;
                LOG_ERROR("[CrossRegion] 开始纠正，累积角度：{:.2f} 度",
                          accumulated_angle_ * 180.0 / M_PI);
            }
            else
            {
                // 继续直行
                PublishVelocity(cross_region_linear_, 0.0);
                return;
            }
        }
        else
        {
            accumulated_angle_ = 0.0; // 无干扰时重置累积角度
            PublishVelocity(cross_region_linear_, 0.0);
            return;
        }
    }

    // 第三步：纠正阶段
    if (is_correcting_)
    {
        float angle_to_correct = accumulated_angle_;
        float v_linear = cross_region_linear_;
        float t_correction = 1.0; // 纠正时间1秒
        float v_angular = angle_to_correct / t_correction;

        // 3a：纠正中仍有干扰
        if (std::abs(angular_velocity_z) > threshold_angular_velocity_)
        {
            accumulated_angle_ += angular_velocity_z * dt; // 动态更新
            angle_to_correct = accumulated_angle_;
            v_angular = angle_to_correct / t_correction;
            LOG_ERROR("[CrossRegion] 纠正中仍有干扰，需纠正角度：{:.2f} 度",
                      angle_to_correct * 180.0 / M_PI);
        }
        else
        {
            // 3b：纠正中无干扰
            LOG_ERROR("[CrossRegion] 纠正中无干扰，需纠正角度：{:.2f} 度",
                      angle_to_correct * 180.0 / M_PI);
        }

        // 第五步：同时发布直线和旋转速度
        PublishVelocity(v_linear, v_angular);

        // 第四步：检查纠正是否完成
        if (std::abs(yaw_current_ - yaw_target_) < threshold_correction_)
        {
            is_correcting_ = false;
            accumulated_angle_ = 0.0;
            LOG_ERROR("[CrossRegion] 纠正完成，航向误差：{:.2f} 度",
                      (yaw_current_ - yaw_target_) * 180.0 / M_PI);
            PublishVelocity(cross_region_linear_, 0.0); // 恢复直行
        }
    }
}

void NavigationCrossRegionAlg::CalibrateImuBias(const ImuData &imu_data)
{
    if (bias_samples_.size() < bias_sample_count_)
    {
        bias_samples_.push_back(imu_data.angular_velocity_z);
        LOG_INFO("[CrossRegion] 收集零偏样本：{}/{}", bias_samples_.size(), bias_sample_count_);
        return;
    }

    // 计算平均零偏
    float sum = 0.0f;
    for (const auto &sample : bias_samples_)
    {
        sum += sample;
    }
    bias_z_ = sum / bias_samples_.size();
    is_bias_calibrated_ = true;
    bias_samples_.clear(); // 清空样本
    LOG_INFO("[CrossRegion] 零偏校准完成，bias_z = {:.6f} rad/s", bias_z_);
}

void NavigationCrossRegionAlg::SetImuData(const ImuData &imu_data)
{
    std::lock_guard<std::mutex> lock(imu_data_mtx_);
    imu_data_ = imu_data;

    // 将IMU数据传递给处理器
    if (imu_processor_)
    {
        imu_processor_->SetImuData(imu_data);
    }
}

void NavigationCrossRegionAlg::InitializeImuProcessor()
{
    // 设置IMU处理器参数
    imu_processor_param_.filter_alpha = 1.0f;
    imu_processor_param_.angular_velocity_threshold = 0.00f;
    imu_processor_param_.bias_calibration_samples = 300;
    imu_processor_param_.rotation_tolerance = 0.17f; // 约10度
    imu_processor_param_.max_rotation_time = 30.0f;
    imu_processor_param_.backup_distance = 0.3f;     // 后退距离0.3米
    imu_processor_param_.backup_speed = 0.2f;        // 后退速度0.2米/秒
    imu_processor_param_.max_backup_attempts = 3;    // 最大后退尝试次数3次
    imu_processor_param_.enable_data_logging = true; // 可根据需要启用
    imu_processor_param_.log_file_path = "/userdata/log/cross_region_imu.log";

    // 创建IMU处理器
    imu_processor_ = std::make_unique<ImuDataProcessor>(imu_processor_param_);

    // 设置速度发布回调函数
    imu_processor_->SetVelocityCallback([this](float linear, float angular, uint64_t duration_ms) {
        this->PublishVelocity(linear, angular, duration_ms);
    });

    // 设置回调函数
    imu_processor_->SetImuDataCallback([this](const ImuData &processed_data) {
        // 这里可以处理经过滤波的IMU数据
        LOG_DEBUG("[CrossRegion] Received processed IMU data: angular_velocity_z = {:.4f}",
                  processed_data.angular_velocity_z);
    });

    // 初始化处理器
    imu_processor_->Initialize();

    LOG_INFO("[CrossRegion] IMU processor initialized successfully");
}

void NavigationCrossRegionAlg::ShutdownImuProcessor()
{
    if (imu_processor_)
    {
        imu_processor_->Shutdown();
        imu_processor_.reset();
        LOG_INFO("[CrossRegion] IMU processor shutdown completed");
    }
}

void NavigationCrossRegionAlg::ControlRotaryMotionWithIMU(const float &yaw_des, const float &yaw_first, const float &vel_angular)
{
    if (!imu_processor_ || !imu_processor_->IsBiasCalibrated())
    {
        LOG_WARN("[CrossRegion] IMU processor not ready, falling back to time-based rotation");
        ControlRotaryMotion(yaw_des, yaw_first, vel_angular);
        return;
    }

    float target_angle = UnifyAngle(yaw_des - yaw_first);
    float sign = target_angle >= 0.0f ? 1.0f : -1.0f;
    // target_angle = std::abs(target_angle);

    LOG_INFO("[CrossRegion] Starting IMU-based rotation: target={:.3f}°, direction={}",
             target_angle * 180.0f / M_PI, sign > 0 ? "left" : "right");

    // 启动IMU闭环控制（现在通过回调函数自动控制速度）
    imu_processor_->StartRotationControl(target_angle, vel_angular);

    // 等待旋转完成
    while (imu_processor_->IsRotationControlActive())
    {
        // 旋转控制现在通过IMU处理器的回调函数自动处理
        // 包括正常旋转、超时后退、后退完成后继续旋转等逻辑
        std::this_thread::sleep_for(std::chrono::milliseconds(50));

        // 检查是否需要停止（例如收到停止信号）
        if (mower_running_state_ == MowerRunningState::PAUSE)
        {
            imu_processor_->StopRotationControl();
            break;
        }
    }

    // 确保停止所有运动（IMU处理器已经在线程退出时停止了，这里是额外保险）
    PublishVelocity(0.0f, 0.0f, 100);

    // 获取最终结果
    // 这里可以根据需要处理结果
    LOG_INFO("[CrossRegion] IMU-based rotation completed");
}

void NavigationCrossRegionAlg::ControlProcessToPassPointsWithIMU(const float &x_first, const float &y_first, const float &yaw_first)
{
    float pass_point = 0.55; // Pass point: offset from the beacon in the y-axis direction

    // Use a local variable to save the current angle for easy updating
    float current_yaw = yaw_first;
    bool need_adjust = false;
    float target_yaw = current_yaw;

    x_after_adjustment_ = x_first;

    // Position-based movement logic (same as original)
    if (y_first <= 0) // The car is on the right side of the beacon
    {
        // Determine whether the direction needs to be adjusted
        if (current_yaw >= 0 && current_yaw <= M_PI / 6)
        {
            target_yaw = M_PI / 4;
            need_adjust = true;
        }
        else if (current_yaw >= 2 * M_PI / 6 && current_yaw <= M_PI)
        {
            target_yaw = M_PI / 4;
            need_adjust = true;
        }
        else if (current_yaw < 0)
        {
            target_yaw = M_PI / 4;
            need_adjust = true;
        }

        // If the direction needs to be adjusted, perform the rotation first using IMU
        if (need_adjust)
        {
            LOG_INFO("[CrossRegion] [first] Adjust direction with IMU: from {:.1f}° to {:.1f}°",
                     Radians2Degrees(current_yaw), Radians2Degrees(target_yaw));
            ControlRotaryMotionWithIMU(target_yaw, current_yaw, cross_region_angular_);
            current_yaw = target_yaw;
        }

        if (y_first <= -pass_point) // The car is on the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] Beacon right, and on the right side of the pass point: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, -pass_point, B_x, distance);
            ControlLinearMotion(distance, 0.0, cross_region_linear_, 1);

            x_after_adjustment_ = B_x;
        }
        else // The car is on the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] Beacon right, and on the left side of the pass point: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, -pass_point, B_x, distance);
            ControlLinearMotion(distance, 0.0, cross_region_linear_, -1);

            x_after_adjustment_ = B_x;
        }
    }
    else // The car is on the left side of the beacon
    {
        // Determine whether the direction needs to be adjusted
        if (current_yaw >= 0)
        {
            target_yaw = -M_PI / 4;
            need_adjust = true;
        }
        else if (current_yaw >= -M_PI / 6 && current_yaw < 0)
        {
            target_yaw = -M_PI / 4;
            need_adjust = true;
        }
        else if (current_yaw <= -2 * M_PI / 6 && current_yaw >= -M_PI)
        {
            target_yaw = -M_PI / 4;
            need_adjust = true;
        }

        // If the direction needs to be adjusted, perform the rotation first using IMU
        if (need_adjust)
        {
            LOG_INFO("[CrossRegion] [first] Adjust direction with IMU: from {:.1f}° to {:.1f}°",
                     Radians2Degrees(current_yaw), Radians2Degrees(target_yaw));
            ControlRotaryMotionWithIMU(target_yaw, current_yaw, cross_region_angular_);
            current_yaw = target_yaw;
        }

        if (y_first <= pass_point) // The car is on the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] Beacon left, and on the right side of the pass point: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, pass_point, B_x, distance);
            ControlLinearMotion(distance, 0.0, cross_region_linear_, -1);

            x_after_adjustment_ = B_x;
        }
        else // The car is on the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] Beacon left, and on the left side of the pass point: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, pass_point, B_x, distance);
            ControlLinearMotion(distance, 0.0, cross_region_linear_, 1);

            x_after_adjustment_ = B_x;
        }
    }

    // After moving to the pass point, adjust the direction to 0 degrees using IMU
    LOG_INFO("[CrossRegion][second] Adjust direction to 0 degrees with IMU");
    ControlRotaryMotionWithIMU(0.0, current_yaw, cross_region_angular_);
}

void NavigationCrossRegionAlg::ControlProcessToEndPointsWithIMU(const float &x_first, const float &y_first, const float &yaw_first)
{
    float end_point = 0.55; // End point: offset from the beacon in the y-axis direction

    // Use a local variable to save the current angle for easy updating
    float current_yaw = yaw_first;
    bool need_adjust = false;
    float target_yaw = current_yaw;

    // Position-based movement logic (same as original)
    if (y_first <= 0) // On the left side of the beacon
    {
        // Determine whether the direction needs to be adjusted
        if (current_yaw >= 0 && current_yaw <= 4 * M_PI / 6)
        {
            target_yaw = 3 * M_PI / 4;
            need_adjust = true;
        }
        else if (current_yaw >= 5 * M_PI / 6 && current_yaw <= M_PI)
        {
            target_yaw = 3 * M_PI / 4;
            need_adjust = true;
        }
        else if (current_yaw < 0)
        {
            target_yaw = 3 * M_PI / 4;
            need_adjust = true;
        }

        // If the direction needs to be adjusted, perform the rotation first using IMU
        if (need_adjust)
        {
            LOG_INFO("[CrossRegion][first] Adjust direction with IMU: from {:.1f}° to {:.1f}°",
                     Radians2Degrees(current_yaw), Radians2Degrees(target_yaw));
            ControlRotaryMotionWithIMU(target_yaw, current_yaw, cross_region_angular_);
            current_yaw = target_yaw;
        }

        if (y_first <= -end_point) // On the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 4] On the left side of the beacon, on the left side of the pass point y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, -end_point, B_x, distance);
            ControlLinearMotion(distance, 0.0, cross_region_linear_, 1);
        }
        else // On the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 4] On the left side of the beacon, on the right side of the pass point y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, -end_point, B_x, distance);
            ControlLinearMotion(distance, 0.0, cross_region_linear_, -1);
        }
    }
    else // On the right side of the beacon
    {
        // Determine whether the direction needs to be adjusted
        if (current_yaw >= 0)
        {
            target_yaw = -3 * M_PI / 4;
            need_adjust = true;
        }
        else if (current_yaw >= -4 * M_PI / 6 && current_yaw < 0)
        {
            target_yaw = -3 * M_PI / 4;
            need_adjust = true;
        }
        else if (current_yaw <= -5 * M_PI / 6 && current_yaw >= -M_PI)
        {
            target_yaw = -3 * M_PI / 4;
            need_adjust = true;
        }

        // If the direction needs to be adjusted, perform the rotation first using IMU
        if (need_adjust)
        {
            LOG_INFO("[CrossRegion][first] Adjust direction with IMU: from {:.1f}° to {:.1f}°",
                     Radians2Degrees(current_yaw), Radians2Degrees(target_yaw));
            ControlRotaryMotionWithIMU(target_yaw, current_yaw, cross_region_angular_);
            current_yaw = target_yaw;
        }

        if (y_first <= end_point) // On the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 4] On the right side of the beacon, on the left side of the pass point y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, end_point, B_x, distance);
            ControlLinearMotion(distance, 0.0, cross_region_linear_, -1);
        }
        else // On the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 4] On the right side of the beacon, on the right side of the pass point y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, end_point, B_x, distance);
            ControlLinearMotion(distance, 0.0, cross_region_linear_, 1);
        }
    }

    // After moving to the end point, adjust the direction to 0 degrees using IMU
    LOG_INFO("[CrossRegion][second] Adjust direction to M_PI degrees with IMU");
    ControlRotaryMotionWithIMU(M_PI, current_yaw, cross_region_angular_);
}

} // namespace fescue_iox
