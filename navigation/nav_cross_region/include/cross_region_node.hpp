#ifndef NAVIGATION_CROSS_REGION_NODE_HPP
#define NAVIGATION_CROSS_REGION_NODE_HPP

#include "cross_region.hpp"
#include "cross_region_config.hpp"
#include "data_type.hpp"
#include "geometry_msgs/twist__struct.h"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/client.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/server.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "mower_msgs/msg/mcu_imu.hpp"
#include "mower_msgs/msg/mcu_motor_speed.hpp"
#include "mower_msgs/msg/soc_imu.hpp"
#include "mower_msgs/srv/cross_region_width.hpp"
#include "ob_mower_msgs/mark_location_result_struct.h"
#include "ob_mower_msgs/nav_alg_ctrl__struct.h"
#include "ob_mower_msgs/nav_cross_region_final_result__struct.h"
#include "ob_mower_msgs/nav_cross_region_state__struct.h"
#include "ob_mower_msgs/nav_fusion_pose.h"
#include "ob_mower_msgs/nav_running_state__struct.h"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "ob_mower_srvs/mark_location_detect_mark_id_service__struct.h"
#include "ob_mower_srvs/nav_cross_region_alg_param_service__struct.h"
#include "ob_mower_srvs/nav_cross_region_node_param_service__struct.h"
#include "opencv2/opencv.hpp"
#include "utils/heartbeat_publisher.hpp"
#include "utils/iceoryx_client_mower.hpp"
#include "utils/iceoryx_publisher_mower.hpp"
#include "utils/iceoryx_server_mower.hpp"
#include "utils/iceoryx_subscriber_mower.hpp"

#include <fstream>
#include <memory>
#include <mutex>
#include <thread>

#define PRINTF_IMU_DATA 1

namespace fescue_iox
{

struct FilterState
{
    float accel_x = 0.0f, accel_y = 0.0f, accel_z = 0.0f;
    float gyro_x = 0.0f, gyro_y = 0.0f, gyro_z = 0.0f;
    float motor_speed_left = 0.0f, motor_speed_right = 0.0f;
};

class NavigationCrossRegionNode
{
    using iox_cross_region_final_result_publisher = iox::popo::Publisher<fescue_msgs__msg__NavCrossRegionFinalResult>;
    using iox_nav_alg_ctrl_publisher = iox::popo::Publisher<fescue_msgs__msg__NavigationAlgoCtrlData>;
    using iox_cross_region_state_publisher = iox::popo::Publisher<fescue_msgs__msg__CrossRegionStateData>;

    using get_node_param_request = fescue_msgs__srv__GetNavigationCrossRegionNodeParam_Request;
    using get_node_param_response = fescue_msgs__srv__GetNavigationCrossRegionNodeParam_Response;
    using set_node_param_request = fescue_msgs__srv__SetNavigationCrossRegionNodeParam_Request;
    using set_node_param_response = fescue_msgs__srv__SetNavigationCrossRegionNodeParam_Response;

    using get_alg_param_request = fescue_msgs__srv__GetNavigationCrossRegionAlgParam_Request;
    using get_alg_param_response = fescue_msgs__srv__GetNavigationCrossRegionAlgParam_Response;
    using set_alg_param_request = fescue_msgs__srv__SetNavigationCrossRegionAlgParam_Request;
    using set_alg_param_response = fescue_msgs__srv__SetNavigationCrossRegionAlgParam_Response;

    using set_cross_region_width_request = mower_msgs::srv::CrossRegionWidthRequest;
    using set_cross_region_width_response = mower_msgs::srv::CrossRegionWidthResponse;

public:
    NavigationCrossRegionNode(const std::string &node_name);
    ~NavigationCrossRegionNode();

private:
    void InitWorkingDirectory();
    void InitParam();
    void InitAlgorithmParam();
    void InitAlgorithm();
    void DeinitAlgorithm();
    void InitLogger();
    void InitSubscriber();
    void InitPublisher();
    void InitService();
    void CrossRegionThread();
    void InitHeartbeat();

private:
    void DealPerceptionFusionResult(const fescue_msgs__msg__PerceptionFusionResult &msg);
    void DealMarkLocationResult(const fescue_msgs__msg__MarkLocationResult &msg);
    void DealNavAlgCtrlResult(const fescue_msgs__msg__NavigationAlgoCtrlData &msg);
    void DealCrossRegionState(const fescue_msgs__msg__CrossRegionStateData &msg);
    void DealNavRunningState(const fescue_msgs__msg__NavigationRunningStateData &data);
    void DealMcuImu(const mower_msgs::msg::McuImu &msg);
    void DealSocImu(const mower_msgs::msg::SocImu &msg);
    void DealMcuMotorSpeed(const mower_msgs::msg::McuMotorSpeed &msg);

    bool GetCrossRegionNodeParam(fescue_msgs__msg__NavigationCrossRegionNodeParam &data);
    bool SetCrossRegionNodeParam(const fescue_msgs__msg__NavigationCrossRegionNodeParam &data);
    bool GetCrossRegionAlgParam(fescue_msgs__msg__NavigationCrossRegionAlgParam &data);
    bool SetCrossRegionAlgParam(const fescue_msgs__msg__NavigationCrossRegionAlgParam &data);
    bool SetCrossRegionChannelWidth(float cross_region_channel_width);
    void ConfigParamToAlgParam(const NavigationCrossRegionAlgConfig &config, CrossRegionAlgParam &param);
    void AlgParamToConfigParam(NavigationCrossRegionAlgConfig &config, const CrossRegionAlgParam &param);
    void PublishCrossRegionFinalResult(const CrossRegionAlgResult &result);
    bool DealMarkLocationMarkIdCallback(int mark_id);
    void DealFeatureSelectCallback(const std::vector<FeatureSelectData> &data);
    void DealCrossRegionRunningStateCallback(CrossRegionRunningState state);
    float LowPassFilter(float new_value, float prev_value, float alpha);
    void SetMotorSpeedData(const MotorSpeedData &motor_speed_data);
    void SetImuData(const ImuData &imu_data);

    void SetCrossRegionVelPublisherProhibit(bool prohibit)
    {
        if (cross_region_alg_)
        {
            cross_region_alg_->SetVelPublisherProhibit(prohibit);
        }
    }

private:
    // Subscribers
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__PerceptionFusionResult>> sub_fusion_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__MarkLocationResult>> sub_mark_loc_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>> sub_nav_alg_ctrl_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__CrossRegionStateData>> sub_cross_region_state_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationRunningStateData>> sub_nav_running_state_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuImu>> sub_mcu_imu_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::SocImu>> sub_soc_imu_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuMotorSpeed>> sub_mcu_motor_speed_{nullptr};

    // Publishers
    std::unique_ptr<iox_nav_alg_ctrl_publisher> pub_nav_alg_ctrl_{nullptr};
    std::unique_ptr<iox_cross_region_state_publisher> pub_cross_region_state_{nullptr};
    std::unique_ptr<iox_cross_region_final_result_publisher> pub_cross_region_final_result_{nullptr}; // Publish cross-region final execution result

    // Services
    std::unique_ptr<IceoryxServerMower<get_node_param_request, get_node_param_response>> service_get_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_node_param_request, set_node_param_response>> service_set_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<get_alg_param_request, get_alg_param_response>> service_get_alg_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_alg_param_request, set_alg_param_response>> service_set_alg_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_cross_region_width_request, set_cross_region_width_response>> service_set_cross_region_width_{nullptr};

    std::atomic_bool cross_region_enable_{false};

private:
    FilterState filter_state_;
    float alpha_imu_ = 0.1f;   // IMU filter coefficient
    float alpha_speed_ = 0.2f; // Rotation speed filter coefficient
    bool enable_imu_filter_ = false;
    bool enable_motor_speed_filter_ = false;

private:
    std::mutex mark_loc_mutex_;
    std::mutex fusion_mutex_;
    MarkLocationResult mark_loc_result_;
    PerceptionFusionResult fusion_result_;
    std::mutex cross_region_mtx_;
    CrossRegionRunningState cross_region_state_{CrossRegionRunningState::UNDEFINED};

    std::thread cross_region_thread_;
    std::atomic_bool thread_running_{true};

    CrossRegionAlgParam cross_region_param_;
    std::unique_ptr<NavigationCrossRegionAlg> cross_region_alg_{nullptr};

    std::mutex imu_mtx_;
    ImuData imu_data_;
    std::mutex motor_speed_mtx_;
    MotorSpeedData motor_speed_data_;

    // Parameters
    std::string node_name_{"navigation_cross_region_node"};
    std::string log_dir_{"/userdata/log"};
    std::string console_log_level_{"info"};
    std::string file_log_level_{"warn"};
    std::string cross_region_alg_conf_file_{"conf/navigation_cross_region_node/cross_region.yaml"};
    std::unique_ptr<NodeHeartbeatPublisher> pub_heartbeat_{nullptr};

#if PRINTF_IMU_DATA
    void OpenImuDataFile();
    std::ofstream imu_data_file_;
#endif
};

} // namespace fescue_iox

#endif
