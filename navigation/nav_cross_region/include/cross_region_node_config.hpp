#pragma once

#include "utils/common_config.hpp"
#include "utils/config.hpp"

#include <string>

namespace fescue_iox
{

/**
 * @brief Configuration parameters for the NavigationCrossRegionNode
 * @param common_conf Common configuration parameters
 * @param run_frequency Run frequency in Hz
 */
struct NavigationCrossRegionNodeConfig
{
    CommonConfig common_conf;
    std::string cross_region_alg_conf_file{"conf/navigation_cross_region_node/cross_region.yaml"};

    NavigationCrossRegionNodeConfig() = default;
    ~NavigationCrossRegionNodeConfig() = default;
    NavigationCrossRegionNodeConfig(const NavigationCrossRegionNodeConfig &config) = default;
    NavigationCrossRegionNodeConfig &operator=(const NavigationCrossRegionNodeConfig &config);
    std::string toString() const;
};

bool operator==(const NavigationCrossRegionNodeConfig &lhs, const NavigationCrossRegionNodeConfig &rhs);
bool operator!=(const NavigationCrossRegionNodeConfig &lhs, const NavigationCrossRegionNodeConfig &rhs);

} // namespace fescue_iox
