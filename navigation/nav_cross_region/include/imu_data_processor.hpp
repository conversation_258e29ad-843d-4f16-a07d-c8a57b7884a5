#ifndef IMU_DATA_PROCESSOR_HPP
#define IMU_DATA_PROCESSOR_HPP

#include "data_type.hpp"
#include "utils/logger.hpp"

#include <atomic>
#include <chrono>
#include <functional>
#include <memory>
#include <mutex>
#include <thread>
#include <vector>

namespace fescue_iox
{

/**
 * @brief IMU数据处理器参数结构
 */
struct ImuProcessorParam
{
    // 滤波参数
    float filter_alpha{1.0f};                // 低通滤波器系数 (0-1, 越小滤波越强)
    float angular_velocity_threshold{0.00f}; // 0.02 角速度阈值滤波 (rad/s)

    // 校准参数
    size_t bias_calibration_samples{300}; // 零偏校准样本数
    float bias_threshold{0.05f};          // 零偏阈值 (rad/s)

    // 闭环控制参数
    float rotation_tolerance{0.17f}; // 旋转完成容差 (rad, 约10度)
    float max_rotation_time{30.0f};  // 最大旋转时间 (秒)

    // 超时后退参数
    float backup_distance{0.3f}; // 后退距离 (米)
    float backup_speed{0.2f};    // 后退速度 (米/秒)
    int max_backup_attempts{3};  // 最大后退尝试次数

    // 数据日志参数
    bool enable_data_logging{false};                         // 是否启用数据日志
    std::string log_file_path{"/userdata/log/imu_data.log"}; // 日志文件路径
};

/**
 * @brief 旋转控制结果
 */
struct RotationControlResult
{
    bool completed{false};       // 旋转是否完成
    bool timeout{false};         // 是否超时
    float actual_rotation{0.0f}; // 实际旋转角度 (rad)
    float target_rotation{0.0f}; // 目标旋转角度 (rad)
    float rotation_error{0.0f};  // 旋转误差 (rad)
    float elapsed_time{0.0f};    // 已用时间 (秒)
};

/**
 * @brief IMU数据处理器类
 *
 * 功能：
 * 1. IMU数据订阅和预处理
 * 2. 零偏校准和滤波
 * 3. 基于IMU的闭环旋转控制
 * 4. 数据日志记录
 */
class ImuDataProcessor
{
public:
    explicit ImuDataProcessor(const ImuProcessorParam &param = ImuProcessorParam{});
    ~ImuDataProcessor();

    // 初始化和关闭
    void Initialize();
    void Shutdown();

    // IMU数据输入接口
    void SetImuData(const ImuData &imu_data);

    // 设置回调函数，向主算法类传递处理后的IMU数据
    void SetImuDataCallback(std::function<void(const ImuData &)> callback);

    // 设置速度发布回调函数，用于旋转控制中的后退操作
    void SetVelocityCallback(std::function<void(float, float, uint64_t)> callback);

    // 闭环旋转控制接口
    RotationControlResult StartRotationControl(float target_angle, float angular_velocity);
    void StopRotationControl();
    bool IsRotationControlActive() const;

    // 获取当前状态
    bool IsBiasCalibrated() const;
    float getCurrentYaw() const;
    float getBiasZ() const;

    // 参数设置
    void SetParam(const ImuProcessorParam &param);
    ImuProcessorParam GetParam() const;

    // 重置状态
    void ResetState();

private:
    // 核心处理函数
    void ProcessImuData(const ImuData &imu_data);
    void CalibrateImuBias(const ImuData &imu_data);
    float ApplyLowPassFilter(float new_value, float &filtered_value, float alpha);
    void InitializeFilters(float initial_angular_velocity);

    // 闭环旋转控制
    void RotationControlThread();
    void UpdateRotationControl(float angular_velocity, float dt);

    // 数据日志
    void InitializeDataLogging();
    void LogFilteringData(uint64_t timestamp, float raw_angular_velocity, float filtered_angular_velocity);
    void CloseDataLogging();

private:
    // 参数
    ImuProcessorParam param_;

    // 线程控制
    std::atomic_bool processing_active_{false};
    std::atomic_bool rotation_control_active_{false};
    std::thread rotation_control_thread_;

    // 数据互斥锁
    std::mutex imu_data_mutex_;
    std::mutex rotation_control_mutex_;

    // IMU数据
    ImuData latest_imu_data_;
    bool imu_data_valid_{false};

    // 回调函数
    std::function<void(const ImuData &)> imu_data_callback_;
    std::function<void(float, float, uint64_t)> velocity_callback_; // 速度发布回调 (linear, angular, duration_ms)

    // 滤波和校准状态
    bool is_bias_calibrated_{false};
    bool filter_initialized_{false};
    float bias_z_{0.0f};
    std::vector<float> bias_samples_;
    float filtered_angular_velocity_{0.0f};

    // 时间管理
    std::chrono::steady_clock::time_point last_imu_time_;
    bool is_first_imu_{true};
    uint64_t last_imu_timestamp_{0};

    // 闭环旋转控制状态
    RotationControlResult rotation_result_;
    float target_rotation_angle_{0.0f};
    float accumulated_rotation_{0.0f};
    float target_angular_velocity_{0.0f};
    std::chrono::steady_clock::time_point rotation_start_time_;

    // 超时后退状态
    int backup_attempt_count_{0};                                       // 当前后退尝试次数
    bool is_backing_up_{false};                                         // 是否正在后退
    std::chrono::steady_clock::time_point backup_start_time_;           // 后退开始时间
    std::chrono::steady_clock::time_point current_rotation_start_time_; // 当前旋转开始时间

    // 当前状态
    float current_yaw_{0.0f};

    // 数据日志
    std::ofstream log_file_;
    bool logging_initialized_{false};
};

} // namespace fescue_iox

#endif // IMU_DATA_PROCESSOR_HPP
