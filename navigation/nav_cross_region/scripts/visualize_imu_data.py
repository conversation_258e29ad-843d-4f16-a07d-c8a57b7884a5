#!/usr/bin/env python3
"""
IMU数据可视化脚本
用于比较滤波前后的IMU数据，分析滤波效果
"""

import argparse
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import sys

def load_imu_data(file_path):
    """
    加载IMU数据文件
    
    Args:
        file_path (str): CSV文件路径
        
    Returns:
        pandas.DataFrame: 包含时间戳、原始角速度、滤波后角速度的数据
    """
    try:
        # 读取CSV文件
        data = pd.read_csv(file_path)
        
        # 检查必要的列是否存在
        required_columns = ['timestamp', 'raw_angular_velocity', 'filtered_angular_velocity']
        for col in required_columns:
            if col not in data.columns:
                raise ValueError(f"Missing required column: {col}")
        
        # 转换时间戳为相对时间（秒）
        data['time_seconds'] = (data['timestamp'] - data['timestamp'].iloc[0]) / 1000000.0  # 假设时间戳是微秒
        
        return data
        
    except Exception as e:
        print(f"Error loading data from {file_path}: {e}")
        return None

def plot_angular_velocity_comparison(data, output_path=None):
    """
    绘制角速度对比图
    
    Args:
        data (pandas.DataFrame): IMU数据
        output_path (str, optional): 输出图片路径
    """
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 10))
    
    # 转换为度/秒
    raw_deg = np.degrees(data['raw_angular_velocity'])
    filtered_deg = np.degrees(data['filtered_angular_velocity'])
    
    # 子图1: 原始数据和滤波后数据对比
    ax1.plot(data['time_seconds'], raw_deg, 'b-', alpha=0.7, linewidth=0.8, label='原始角速度')
    ax1.plot(data['time_seconds'], filtered_deg, 'r-', linewidth=1.2, label='滤波后角速度')
    ax1.set_ylabel('角速度 (度/秒)')
    ax1.set_title('IMU角速度数据对比')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 子图2: 滤波效果（差值）
    diff = raw_deg - filtered_deg
    ax2.plot(data['time_seconds'], diff, 'g-', linewidth=1.0, label='滤波差值')
    ax2.set_ylabel('角速度差值 (度/秒)')
    ax2.set_title('滤波效果（原始 - 滤波后）')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 子图3: 累积角度
    raw_cumulative = np.cumsum(raw_deg * np.diff(np.concatenate([[0], data['time_seconds']])))
    filtered_cumulative = np.cumsum(filtered_deg * np.diff(np.concatenate([[0], data['time_seconds']])))
    
    ax3.plot(data['time_seconds'], raw_cumulative, 'b-', alpha=0.7, linewidth=0.8, label='原始累积角度')
    ax3.plot(data['time_seconds'], filtered_cumulative, 'r-', linewidth=1.2, label='滤波后累积角度')
    ax3.set_xlabel('时间 (秒)')
    ax3.set_ylabel('累积角度 (度)')
    ax3.set_title('累积角度对比')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存到: {output_path}")
    else:
        plt.show()

def calculate_statistics(data):
    """
    计算统计信息
    
    Args:
        data (pandas.DataFrame): IMU数据
        
    Returns:
        dict: 统计信息
    """
    raw_deg = np.degrees(data['raw_angular_velocity'])
    filtered_deg = np.degrees(data['filtered_angular_velocity'])
    
    stats = {
        'raw_mean': np.mean(raw_deg),
        'raw_std': np.std(raw_deg),
        'raw_max': np.max(np.abs(raw_deg)),
        'filtered_mean': np.mean(filtered_deg),
        'filtered_std': np.std(filtered_deg),
        'filtered_max': np.max(np.abs(filtered_deg)),
        'noise_reduction': (np.std(raw_deg) - np.std(filtered_deg)) / np.std(raw_deg) * 100,
        'data_points': len(data),
        'duration': data['time_seconds'].iloc[-1] - data['time_seconds'].iloc[0]
    }
    
    return stats

def print_statistics(stats):
    """
    打印统计信息
    
    Args:
        stats (dict): 统计信息
    """
    print("\n=== IMU数据统计信息 ===")
    print(f"数据点数: {stats['data_points']}")
    print(f"持续时间: {stats['duration']:.2f} 秒")
    print(f"\n原始数据:")
    print(f"  平均值: {stats['raw_mean']:.4f} 度/秒")
    print(f"  标准差: {stats['raw_std']:.4f} 度/秒")
    print(f"  最大值: {stats['raw_max']:.4f} 度/秒")
    print(f"\n滤波后数据:")
    print(f"  平均值: {stats['filtered_mean']:.4f} 度/秒")
    print(f"  标准差: {stats['filtered_std']:.4f} 度/秒")
    print(f"  最大值: {stats['filtered_max']:.4f} 度/秒")
    print(f"\n噪声降低: {stats['noise_reduction']:.2f}%")

def main():
    parser = argparse.ArgumentParser(description='IMU数据可视化工具')
    parser.add_argument('input_file', help='输入的CSV文件路径')
    parser.add_argument('-o', '--output', help='输出图片路径（可选）')
    parser.add_argument('--stats-only', action='store_true', help='仅显示统计信息，不绘制图表')
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    if not os.path.exists(args.input_file):
        print(f"错误: 文件 {args.input_file} 不存在")
        sys.exit(1)
    
    # 加载数据
    print(f"正在加载数据: {args.input_file}")
    data = load_imu_data(args.input_file)
    
    if data is None:
        print("数据加载失败")
        sys.exit(1)
    
    print(f"成功加载 {len(data)} 个数据点")
    
    # 计算统计信息
    stats = calculate_statistics(data)
    print_statistics(stats)
    
    # 绘制图表（除非只要求统计信息）
    if not args.stats_only:
        print("\n正在生成图表...")
        plot_angular_velocity_comparison(data, args.output)

if __name__ == '__main__':
    main()
