#ifndef NAVIGATION_BEHAVIOR_HPP
#define NAVIGATION_BEHAVIOR_HPP

#include "data_type.hpp"
#include "ob_mower_msgs/nav_alg_ctrl__struct.h"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "opencv2/opencv.hpp"
#include "path_track.hpp"
#include "predict_trajectory.hpp"
#include "velocity_publisher.hpp"
#include "velocity_smooth.hpp"

#include <atomic>
#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{

struct BehaviorAlgParam
{
    /****************************************Collision Recovery****************************************************/
    float collision_backup_speed = 0.1;     // Backup speed (m/s)/*param*/
    float collision_backup_duration = 2500; // Backup duration (ms)/*param*/
    float collision_turn_angle = M_PI / 6;  // Turn angle (30 degrees)/*param*/
    float collision_turn_speed = 0.3;       // Turn speed (rad/s)/*param*/

    /****************************************Lift Recovery****************************************************/
    float lift_backup_speed = 0.1;   // Backup speed during recovery phase (m/s)/*param*/
    int lift_backup_duration = 1500; // Backup duration during recovery phase (ms)/*param*/

    /****************************************Unstuck Recovery****************************************************/
    float backup_speed = 0.1;         // Backup speed (m/s)/*param*/
    float turn_angle = M_PI / 4;      // Rotation angle (45 degrees)/*param*/
    float forward_speed = 0.2;        // Forward speed (m/s)/*param*/
    uint64_t backup_duration = 1000;  // Backup duration (ms)/*param*/
    uint64_t turn_duration = 1000;    // Rotation duration (ms)/*param*/
    uint64_t forward_duration = 1000; // Forward duration (ms)/*param*/

    /****************************************Wheel Slip and Stall Detection and Recovery****************************************************/
    float wheel_radius = 0.1f;            // Wheel radius (meters)
    float slip_ratio_threshold = 0.3f;    // Slip ratio threshold (30%)
    float stall_current_threshold = 5.0f; // Stall current threshold (Amperes)
    float stall_speed_threshold = 0.1f;   // Stall speed threshold (m/s)
    float min_valid_speed = 0.05f;        // Minimum valid speed (m/s)
    float slip_reduce_ratio = 0.5f;       // Slip reduction ratio
    int max_stall_retries = 3;            // Maximum stall retry attempts
};

// Unstuck State Machine
enum class UnstuckState : int
{
    NORMAL,     // Normal state
    BACKING_UP, // Backing up phase
    TURNING,    // Turning phase
    FORWARDING  // Forwarding phase
};

// Lift Recovery State Machine
enum class LiftState : int
{
    NORMAL,    // Normal state
    LIFTED,    // Lifted state
    RECOVERING // Recovering state
};

// Collision Recovery State Machine
enum class CollisionRecoveryState : int
{
    NORMAL,     // Normal state
    BACKING_UP, // Backing up phase
    TURNING,    // Turning phase
    RECOVERING  // Recovering phase
};

enum class WheelSlipRecoveryState : int
{
    NORMAL,     // Normal state
    BACKING_UP, // Backing up phase
    TURNING,    // Turning phase
    RECOVERING  // Recovering phase
};

enum class BehaviorStatus : int
{
    InProgress = 0,
    Successed = 1,
    Failed = 2
};

struct BehaviorAlgResult
{
    bool behavior_completed{false};
    BehaviorStatus behavior_status{BehaviorStatus::InProgress};
    BehaviorAlgResult() = default;

    BehaviorAlgResult(bool behavior_completed)
        : behavior_completed(behavior_completed)
    {
    }

    BehaviorAlgResult(bool behavior_completed, BehaviorStatus status)
        : behavior_completed(behavior_completed)
        , behavior_status(status)
    {
    }
};

struct PreintegrationState
{
    float velocity_x = 0.0f;
    float velocity_y = 0.0f;
    uint64_t last_timestamp = 0;
};

class NavigationBehaviorAlg
{
public:
    NavigationBehaviorAlg(const BehaviorAlgParam &param);
    ~NavigationBehaviorAlg();

    void SetBehaviorAlgParam(const BehaviorAlgParam &param);
    void GetBehaviorAlgParam(BehaviorAlgParam &param);
    void SetAlgoRunningState(MowerRunningState state);
    void ProhibitVelPublisher();
    void ShowMowerRunningInfo();
    void ResetBehaviorFlags();
    void SetFeatureSelectCallback(std::function<void(const std::vector<FeatureSelectData> &)> callback);
    void SetBehaviorRunningStateCallback(std::function<void(BehaviorRunningState)> callback);

    // New
    BehaviorAlgResult DoBehavior(PerceptionFusionResult &fusion_result,
                                 McuExceptionStatus &mcu_exception_status,
                                 const ImuData &imu_data,
                                 const MotorSpeedData &motor_speed_data,
                                 const MotionDetectionResult &motion_detection_result);

    void SetCollisionStatus(const McuExceptionStatus &mcu_exception_status);
    void SetLiftedStatus(const McuExceptionStatus &mcu_exception_status);
    bool IsStuck(/*const SensorData &sensor_data*/);

    void SetVelPublisherProhibit(bool prohibit)
    {
        if (vel_publisher_)
        {
            vel_publisher_->SetProhibitFlag(prohibit);
        }
    }
    void SetMotorSpeedData(const MotorSpeedData &motor_speed_data);
    void SetMotionDetectionResult(const MotionDetectionResult &motion_detection_result);

    const char *GetVersion();

private:
    void InitSlipDetection();
    void DeinitSlipDetection();
    void SlipDetectionThread();
    bool IsWheelSlipping(const MotorSpeedData &motor_data, const ImuData &imu_data, float wheel_radius, float wheel_base);
    bool IsWheelSlipping(const MotorSpeedData &motor_data,
                         const MotionDetectionResult &motion_detection_result,
                         float wheel_radius, float wheel_base);
    bool IsMotorStalling(const MotorSpeedData &motor_data);
    void HandleMotorStall();
    void HandleWheelSlip(const PerceptionFusionResult &fusion_result);
    void UpdatePreintegration(const ImuData &imu_data);

private:
    void DealFeatureSelect(ThreadControl control, bool state);
    void UpdateBehaviorRunningState(BehaviorRunningState state);
    void ControlRotaryMotion(const float &yaw_des, const float &yaw_first, const float &vel_angular);
    void ControlLinearMotion(const float &pass_point, const float &location, const float &vel_linear, const int &reverse);

    void ShowBehaviorPrint(BehaviorRunningState &behavior_state);

private:
    void PublishVelocity(float linear, float angular, uint64_t duration_ms);
    void PublishZeroVelocity();
    void PauseVelocity();
    void ResumeVelocity();

private:
    void PerformUnstuck();
    void HandleLift(/*const SensorData &sensor_data*/);
    void PerformCollisionRecovery(const PerceptionFusionResult &fusion_result);
    float GetTurnAngle(const PerceptionFusionResult &fusion_result);

private:
    PreintegrationState preint_state_;

private:
    std::function<void(const std::vector<FeatureSelectData> &)> feature_select_callback_;
    std::function<void(BehaviorRunningState)> behavior_running_state_callback_;

    std::unique_ptr<VelocityPublisher> vel_publisher_{nullptr}; // Velocity publisher

    // New running status variables
    MowerRunningState mower_running_state_{MowerRunningState::STOP}; // 0-Not started, 1-Running, 2-Paused
    BehaviorRunningState behavior_status_{BehaviorRunningState::UNDEFINED};

    /****************************************Collision Recovery****************************************************/
    CollisionRecoveryState collision_state_ = CollisionRecoveryState::NORMAL;
    bool is_collision_detected_ = false; // Collision detection flag
    bool collision_recovery_completed_ = false;
    bool collision_recovery_succeed_ = false;

    float collision_backup_speed_ = 0.1;     // Backup speed (m/s)/*param*/
    float collision_backup_duration_ = 2500; // Backup duration (ms)/*param*/
    float collision_turn_angle_ = M_PI / 6;  // Turn angle (30 degrees)/*param*/
    float collision_turn_speed_ = 0.3;       // Turn speed (rad/s)/*param*/

    /****************************************Collision Recovery****************************************************/

    /****************************************Lift Recovery****************************************************/
    LiftState lift_state_ = LiftState::NORMAL;
    bool is_lifted_ = false; // Lift detection flag
    bool lift_completed_ = false;
    bool lift_succeed_ = false;

    float lift_backup_speed_ = 0.1;   // Backup speed during recovery phase (m/s)/*param*/
    int lift_backup_duration_ = 1500; // Backup duration during recovery phase (ms)/*param*/

    /****************************************Lift Recovery****************************************************/

    /****************************************Unstuck Recovery****************************************************/
    UnstuckState unstuck_state_ = UnstuckState::NORMAL;
    bool is_stucked_ = false; // Stuck flag
    bool unstuck_completed_ = false;
    bool unstuck_succeed_ = false;
    int unstuck_attempts_ = 0; // Current number of attempts

    const int max_unstuck_attempts_ = 3; // Maximum number of attempts /*param*/
    float backup_speed_ = 0.1;           // Backup speed (m/s)/*param*/
    float turn_angle_ = M_PI / 4;        // Rotation angle (45 degrees)/*param*/
    float forward_speed_ = 0.2;          // Forward speed (m/s)/*param*/
    uint64_t backup_duration_ = 1000;    // Backup duration (ms)/*param*/
    uint64_t turn_duration_ = 1000;      // Rotation duration (ms)/*param*/
    uint64_t forward_duration_ = 1000;   // Forward duration (ms)/*param*/

    // float wheel_speed_threshold_ = 0.1;   // Wheel speed threshold (m/s)
    // float actual_speed_threshold_ = 0.01; // Actual speed threshold (m/s)
    /****************************************Unstuck Recovery****************************************************/

    /****************************************Wheel Slip and Stall Detection and Recovery****************************************************/
    WheelSlipRecoveryState wheel_slip_state_ = WheelSlipRecoveryState::NORMAL;

    std::mutex motor_speed_mtx_;
    MotorSpeedData motor_speed_data_;

    std::mutex motion_detection_result_mtx_;
    MotionDetectionResult motion_detection_result_;

    std::thread slip_detection_thread_;
    std::atomic_bool slip_detection_running_{false};
    std::atomic_bool is_slipping_detected_{false};
    // int slip_detection_frequency_{20}; // Detection frequency (Hz)

    bool slipping_recovery_completed_ = false;
    bool slipping_recovery_succeed_ = false;
    float slipping_backup_speed_ = 0.15;    // Backup speed (m/s)/*param*/
    float slipping_backup_duration_ = 2500; // Backup duration (ms)/*param*/
    float slipping_turn_angle_ = M_PI / 6;  // Turn angle (30 degrees)/*param*/
    float slipping_turn_speed_ = 0.3;       // Turn speed (rad/s)/*param*/

    float wheel_radius_ = 0.1f; // Wheel radius (meters)
    float wheel_base_ = 0.335f;
    float slip_ratio_threshold_ = 0.3f;    // Slip ratio threshold (30%)
    float stall_current_threshold_ = 5.0f; // Stall current threshold (Amperes)
    float stall_speed_threshold_ = 0.1f;   // Stall speed threshold (m/s)
    float min_valid_speed_ = 0.05f;        // Minimum valid speed (m/s)

    float min_valid_linear_ = 0.1f; // Minimum valid speed (m/s)
    float min_valid_angular_ = 0.2f;

    float slip_reduce_ratio_ = 0.5f; // Slip reduction ratio
    int max_stall_retries_ = 3;      // Maximum stall retry attempts

    /****************************************Wheel Slip and Stall Detection and Recovery****************************************************/
};

} // namespace fescue_iox

#endif