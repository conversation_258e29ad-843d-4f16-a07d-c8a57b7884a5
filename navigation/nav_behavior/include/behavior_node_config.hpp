#pragma once

#include "utils/common_config.hpp"
#include "utils/config.hpp"

#include <string>

namespace fescue_iox
{

/**
 * @brief Configuration parameters for the NavigationBehaviorNode node
 * @param common_conf Common configuration parameters
 * @param run_frequency Execution frequency, in Hz
 */
struct NavigationBehaviorNodeConfig
{
    CommonConfig common_conf;
    std::string behavior_alg_conf_file{"conf/navigation_behavior_node/behavior.yaml"};

    NavigationBehaviorNodeConfig() = default;
    ~NavigationBehaviorNodeConfig() = default;
    NavigationBehaviorNodeConfig(const NavigationBehaviorNodeConfig &config) = default;
    NavigationBehaviorNodeConfig &operator=(const NavigationBehaviorNodeConfig &config);
    std::string toString() const;
};

bool operator==(const NavigationBehaviorNodeConfig &lhs, const NavigationBehaviorNodeConfig &rhs);
bool operator!=(const NavigationBehaviorNodeConfig &lhs, const NavigationBehaviorNodeConfig &rhs);

} // namespace fescue_iox