#include "connect_trajectory_generator.hpp"

#include "Eigen/Dense"
#include "process_fusion.hpp"
#include "utils/logger.hpp"

namespace fescue_iox
{

ConnectTrajectoryGenerator::ConnectTrajectoryGenerator(TrajectoryMode mode) : mode_(mode)
{
}

std::vector<TrajectoryPose> ConnectTrajectoryGenerator::CubicSplineConnect(
    double x1, double y1, double theta1, double kappa1,
    double x2, double y2, double theta2, double kappa2,
    double L, bool is_forward) const {
    (void)kappa1;
    (void)kappa2;
    double linear_velocity = 0.15;
    int numPoints = 50;
    // 如果后退，调整角度和曲率方向
    if (!is_forward)
    {
        theta1 += M_PI; // 后退时实际方向相反
        theta2 += M_PI;
    }

    // 计算导数 (dx/ds, dy/ds)
    double dx0 = cos(theta1);
    double dy0 = sin(theta1);
    double dxL = cos(theta2);
    double dyL = sin(theta2);

    // 计算二阶导数 (d2x/ds2, d2y/ds2) 以满足曲率
    // double d2x0 = -kappa1 * dy0 * pow(dx0*dx0 + dy0*dy0, 1.5);
    // double d2y0 = kappa1 * dx0 * pow(dx0*dx0 + dy0*dy0, 1.5);
    // double d2xL = -kappa2 * dyL * pow(dxL*dxL + dyL*dyL, 1.5);
    // double d2yL = kappa2 * dxL * pow(dxL*dxL + dyL*dyL, 1.5);

    // 构造线性方程组求解三次样条系数
    Eigen::Matrix4d A;
    Eigen::Vector4d bx, by;

    // X(s)的约束条件
    A << 0, 0, 0, 1,
        L * L * L, L * L, L, 1,
        0, 0, 1, 0,
        3 * L * L, 2 * L, 1, 0;
    bx << x1, x2, dx0, dxL;
    Eigen::Vector4d coeff_x = A.fullPivLu().solve(bx);

    // Y(s)的约束条件
    by << y1, y2, dy0, dyL;
    Eigen::Vector4d coeff_y = A.fullPivLu().solve(by);

    // 生成路径点
    std::vector<TrajectoryPose> trajectory;
    for (int i = 0; i < numPoints; ++i)
    {
        double s = L * i / (numPoints - 1);
        double x = coeff_x(0) * s * s * s + coeff_x(1) * s * s + coeff_x(2) * s + coeff_x(3);
        double y = coeff_y(0) * s * s * s + coeff_y(1) * s * s + coeff_y(2) * s + coeff_y(3);
        double dx_ds = 3 * coeff_x(0) * s * s + 2 * coeff_x(1) * s + coeff_x(2);
        double dy_ds = 3 * coeff_y(0) * s * s + 2 * coeff_y(1) * s + coeff_y(2);
        double theta = atan2(dy_ds, dx_ds); // 运动方向（后退时自动反转）
        TrajectoryPose point;
        point.x = x;
        point.y = y;
        point.theta = theta;
        if (is_forward)
        {
            point.linear_velocity = linear_velocity;
        }
        else
        {
            point.linear_velocity = -linear_velocity;
        }
        trajectory.push_back(point);
    }
    if (!is_forward)
    {
        // 角度需要调整回来
        for (auto &pose : trajectory)
        {
            pose.theta = NormalizeAngle(pose.theta + M_PI);
        }
    }
    return trajectory;
}

double ConnectTrajectoryGenerator::CalculateLength(double theta1, double kappa1, double theta2, double kappa2) const
{
    // 通过角度差和曲率计算（Clothoid启发）
    return 2 * abs(NormalizeAngle(theta2 - theta1)) / (abs(kappa1) + abs(kappa2));
}

std::vector<std::vector<TrajectoryDirection>> ConnectTrajectoryGenerator::GenerateHeadingGoalTrajectoryDirections(
    const TrajectoryPose &start_pose, const TrajectoryPose &end_pose) const {
    std::vector<std::vector<TrajectoryDirection>> trajectory_directions;
    double angle_to_end = NormalizeAngle(end_pose.theta - start_pose.theta);
    if (angle_to_end < 0) {
        trajectory_directions.push_back({TrajectoryDirection::TURNING_RIGHT_BACKWARD, TrajectoryDirection::FORWARD, TrajectoryDirection::TURNING_RIGHT_FORWARD});
    } else {
        trajectory_directions.push_back({TrajectoryDirection::TURNING_LEFT_BACKWARD, TrajectoryDirection::FORWARD, TrajectoryDirection::TURNING_LEFT_FORWARD});
    }
    return trajectory_directions;
}

std::vector<std::vector<TrajectoryDirection>> ConnectTrajectoryGenerator::GenerateTrajectoryDirections(
    const TrajectoryPose &start_pose, const TrajectoryPose &end_pose) const
{
    std::vector<std::vector<TrajectoryDirection>> trajectory_directions;
    double angle_to_end = NormalizeAngle(end_pose.theta - start_pose.theta);
    if (mode_ == TrajectoryMode::TRAJECTORY_MODE_HEADING_GOAL) {
        auto heading_goal_directions = GenerateHeadingGoalTrajectoryDirections(start_pose, end_pose);
        for (const auto& direction : heading_goal_directions) {
            trajectory_directions.push_back(direction);
        }
    }
    if (angle_to_end > 0) {
        trajectory_directions.push_back({TrajectoryDirection::TURNING_LEFT_FORWARD, TrajectoryDirection::BACKWARD, TrajectoryDirection::TURNING_RIGHT_FORWARD});
        trajectory_directions.push_back({TrajectoryDirection::TURNING_LEFT_FORWARD, TrajectoryDirection::BACKWARD, TrajectoryDirection::TURNING_LEFT_FORWARD});
        trajectory_directions.push_back({TrajectoryDirection::TURNING_RIGHT_FORWARD, TrajectoryDirection::BACKWARD, TrajectoryDirection::TURNING_RIGHT_FORWARD});
        trajectory_directions.push_back({TrajectoryDirection::TURNING_RIGHT_FORWARD, TrajectoryDirection::BACKWARD, TrajectoryDirection::TURNING_LEFT_FORWARD});

        trajectory_directions.push_back({TrajectoryDirection::TURNING_LEFT_BACKWARD, TrajectoryDirection::FORWARD, TrajectoryDirection::TURNING_RIGHT_BACKWARD});
        trajectory_directions.push_back({TrajectoryDirection::TURNING_LEFT_BACKWARD, TrajectoryDirection::FORWARD, TrajectoryDirection::TURNING_LEFT_BACKWARD});
        trajectory_directions.push_back({TrajectoryDirection::TURNING_RIGHT_BACKWARD, TrajectoryDirection::FORWARD, TrajectoryDirection::TURNING_RIGHT_BACKWARD});
        trajectory_directions.push_back({TrajectoryDirection::TURNING_RIGHT_BACKWARD, TrajectoryDirection::FORWARD, TrajectoryDirection::TURNING_LEFT_BACKWARD});

        trajectory_directions.push_back({TrajectoryDirection::TURNING_LEFT_FORWARD, TrajectoryDirection::FORWARD, TrajectoryDirection::TURNING_LEFT_BACKWARD});
        trajectory_directions.push_back({TrajectoryDirection::TURNING_LEFT_FORWARD, TrajectoryDirection::FORWARD, TrajectoryDirection::TURNING_RIGHT_BACKWARD});
        trajectory_directions.push_back({TrajectoryDirection::TURNING_RIGHT_FORWARD, TrajectoryDirection::FORWARD, TrajectoryDirection::TURNING_LEFT_BACKWARD});
        trajectory_directions.push_back({TrajectoryDirection::TURNING_RIGHT_FORWARD, TrajectoryDirection::FORWARD, TrajectoryDirection::TURNING_RIGHT_BACKWARD});
    }
    else
    {
        trajectory_directions.push_back({TrajectoryDirection::TURNING_RIGHT_FORWARD, TrajectoryDirection::BACKWARD, TrajectoryDirection::TURNING_LEFT_FORWARD});
        trajectory_directions.push_back({TrajectoryDirection::TURNING_RIGHT_FORWARD, TrajectoryDirection::BACKWARD, TrajectoryDirection::TURNING_RIGHT_FORWARD});
        trajectory_directions.push_back({TrajectoryDirection::TURNING_LEFT_FORWARD, TrajectoryDirection::BACKWARD, TrajectoryDirection::TURNING_LEFT_FORWARD});
        trajectory_directions.push_back({TrajectoryDirection::TURNING_LEFT_FORWARD, TrajectoryDirection::BACKWARD, TrajectoryDirection::TURNING_RIGHT_FORWARD});

        trajectory_directions.push_back({TrajectoryDirection::TURNING_RIGHT_BACKWARD, TrajectoryDirection::FORWARD, TrajectoryDirection::TURNING_LEFT_BACKWARD});
        trajectory_directions.push_back({TrajectoryDirection::TURNING_RIGHT_BACKWARD, TrajectoryDirection::FORWARD, TrajectoryDirection::TURNING_RIGHT_BACKWARD});
        trajectory_directions.push_back({TrajectoryDirection::TURNING_LEFT_BACKWARD, TrajectoryDirection::FORWARD, TrajectoryDirection::TURNING_LEFT_BACKWARD});
        trajectory_directions.push_back({TrajectoryDirection::TURNING_LEFT_BACKWARD, TrajectoryDirection::FORWARD, TrajectoryDirection::TURNING_RIGHT_BACKWARD});

        trajectory_directions.push_back({TrajectoryDirection::TURNING_RIGHT_FORWARD, TrajectoryDirection::FORWARD, TrajectoryDirection::TURNING_RIGHT_BACKWARD});
        trajectory_directions.push_back({TrajectoryDirection::TURNING_RIGHT_FORWARD, TrajectoryDirection::FORWARD, TrajectoryDirection::TURNING_LEFT_BACKWARD});
        trajectory_directions.push_back({TrajectoryDirection::TURNING_LEFT_FORWARD, TrajectoryDirection::FORWARD, TrajectoryDirection::TURNING_RIGHT_BACKWARD});
        trajectory_directions.push_back({TrajectoryDirection::TURNING_LEFT_FORWARD, TrajectoryDirection::FORWARD, TrajectoryDirection::TURNING_LEFT_BACKWARD});
    }
    return trajectory_directions;
}

std::vector<std::vector<TrajectoryPose>> ConnectTrajectoryGenerator::ConnectPose(const TrajectoryPose &start_pose, const TrajectoryPose &end_pose)
{
    OccupancyResult start_pose_result;
    return ConnectPose(start_pose, end_pose, start_pose_result);
}

Pose2f ConnectTrajectoryGenerator::GetRelativePose(const Pose2f &base_pose, const Pose2f &pose) const {
    // 把pose转换到base_pose的坐标系下
    float dx = pose.x - base_pose.x;
    float dy = pose.y - base_pose.y;
    
    float theta_b = base_pose.theta;
    float cos_b = cos(theta_b);
    float sin_b = sin(theta_b);
    
    float relative_x =  dx * cos_b + dy * sin_b;
    float relative_y = -dx * sin_b + dy * cos_b;
    float relative_theta = NormalizeAngle(pose.theta - base_pose.theta);
    
    return Pose2f(relative_x, relative_y, relative_theta);
}

std::vector<std::vector<TrajectoryPose>> ConnectTrajectoryGenerator::ConnectPose(
    const TrajectoryPose &start_pose,
    const TrajectoryPose &end_pose,
    const OccupancyResult &start_pose_result)
{
    Pose2f cur_pose(start_pose.x, start_pose.y, start_pose.theta);
    auto sdf_map = GetSDFMap(start_pose_result, cur_pose);

    auto trajectory_directions = GenerateTrajectoryDirections(start_pose, end_pose);
    for (const auto &directions : trajectory_directions)
    {
        const auto &start_direction = directions[0];
        const auto &middle_direction = directions[1];
        const auto &end_direction = directions[2];
        bool is_middle_forward = false;
        if (middle_direction == TrajectoryDirection::BACKWARD)
        {
            is_middle_forward = false;
        }
        else
        {
            is_middle_forward = true;
        }
        auto start_trajs = GenerateTrajs(start_pose, start_direction, false);
        auto end_trajs = GenerateTrajs(end_pose, end_direction, true);
        std::vector<ConnectTrajs> cur_connect_trajs;
        for (const auto &start_traj : start_trajs)
        {
            if (start_traj.empty())
            {
                continue;
            }
            const auto &connect_start_pose = start_traj.back();
            double connect_start_kappa = CalculateKappa(connect_start_pose.linear_velocity, connect_start_pose.angular_velocity);
            for (const auto &end_traj : end_trajs)
            {
                if (end_traj.empty())
                {
                    continue;
                }
                // 终点的路径需要反向
                const auto &connect_end_pose = end_traj.front();
                double connect_end_kappa = CalculateKappa(connect_end_pose.linear_velocity, connect_end_pose.angular_velocity);
                double connect_length = CalculateConnectLength(connect_start_pose, connect_end_pose);
                // std::cout << "start_pose: " << connect_start_pose.x << ", " << connect_start_pose.y << ", " << connect_start_pose.theta
                //           << " kappa: " << connect_start_kappa
                //           << ", end_pose: " << connect_end_pose.x << ", " << connect_end_pose.y << ", " << connect_end_pose.theta
                //           << " kappa: " << connect_end_kappa
                //           << ", connect_length: " << connect_length
                //           << ", is_middle_forward: " << is_middle_forward
                //           << std::endl;
                auto connect_traj = CubicSplineConnect(connect_start_pose.x, connect_start_pose.y, connect_start_pose.theta, connect_start_kappa,
                                                       connect_end_pose.x, connect_end_pose.y, connect_end_pose.theta, connect_end_kappa,
                                                       connect_length, is_middle_forward);
                cur_connect_trajs.push_back({start_traj, connect_traj, end_traj});
            }
        }
        // std::cout << "start_direction: " << (int)start_direction
        //           << ", middle_direction: " << (int)middle_direction
        //           << ", end_direction: " << (int)end_direction
        //           << " cur_connect_trajs size: " << cur_connect_trajs.size()
        //           << std::endl;
        auto best_traj = SelectBestTrajectory(cur_connect_trajs, sdf_map);
        if (!best_traj.connect_traj.empty())
        {
            // 当前方向如果有最优轨迹，则直接返回，不用尝试后续的方向
            std::vector<std::vector<TrajectoryPose>> connect_trajs;
            connect_trajs.emplace_back(best_traj.start_traj);
            connect_trajs.emplace_back(best_traj.connect_traj);
            connect_trajs.emplace_back(best_traj.end_traj);
            return connect_trajs;
        }
    }
    return SpinConnectPose(start_pose, end_pose);
}

std::vector<TrajectoryPose> ConnectTrajectoryGenerator::LineConnectPose(const TrajectoryPose& start_pose, 
                                                                        const TrajectoryPose& end_pose) const {
    double angle_to_end = atan2(end_pose.y - start_pose.y, end_pose.x - start_pose.x);
    double dist_diff = std::hypot(end_pose.x - start_pose.x, end_pose.y - start_pose.y);
    double interval_dist = 0.01;
    double min_dist = 0.05;
    double linear_velocity = 0.15;
    std::vector<TrajectoryPose> line_traj;
    if (dist_diff < min_dist)
    {
        line_traj.emplace_back(start_pose.x, start_pose.y, angle_to_end, linear_velocity, 0);
        line_traj.emplace_back(end_pose.x, end_pose.y, angle_to_end, linear_velocity, 0);
    }
    else
    {
        double cos_angle = cos(angle_to_end);
        double sin_angle = sin(angle_to_end);
        int num = (int)(dist_diff / interval_dist);
        if (num > 1)
        {
            num -= 1;
        }
        for (int i = 0; i < num; ++i)
        {
            double x = start_pose.x + i * interval_dist * cos_angle;
            double y = start_pose.y + i * interval_dist * sin_angle;
            line_traj.emplace_back(x, y, angle_to_end, linear_velocity, 0);
        }
        line_traj.emplace_back(end_pose.x, end_pose.y, angle_to_end, linear_velocity, 0);
    }
    return line_traj;
}

std::vector<std::vector<TrajectoryPose>> ConnectTrajectoryGenerator::SpinConnectPose(const TrajectoryPose &start_pose,
                                                                                     const TrajectoryPose &end_pose) const
{
    // 直接从起始角度自旋到终点角度，然后前进
    std::vector<std::vector<TrajectoryPose>> connect_trajs;
    double angle_diff = NormalizeAngle(end_pose.theta - start_pose.theta);
    double dist_diff = std::hypot(end_pose.x - start_pose.x, end_pose.y - start_pose.y);
    LOG_INFO("SpinConnectPose start_pose: x={}, y={}, theta={}, end_pose: x={}, y={}, theta={} angle_diff: {}, dist_diff: {}",
             start_pose.x, start_pose.y, start_pose.theta, end_pose.x, end_pose.y, end_pose.theta, angle_diff, dist_diff);
    std::vector<TrajectoryPose> spin_traj;
    double line_min_dist = 0.05;
    if (dist_diff > line_min_dist)
    {
        // 距离较大，先自旋到起始角度，走过去，再自旋到终点角度
        LOG_INFO("add spin, line and spin trajs, dist_diff: {} start theta: {} end theta: {}",
                 dist_diff, start_pose.theta, end_pose.theta);
        spin_traj.emplace_back(start_pose.x, start_pose.y, start_pose.theta);
        connect_trajs.emplace_back(spin_traj);
        auto line_traj = LineConnectPose(start_pose, end_pose);
        connect_trajs.emplace_back(line_traj);
        spin_traj.clear();
        spin_traj.emplace_back(end_pose.x, end_pose.y, end_pose.theta);
        connect_trajs.emplace_back(spin_traj);
    }
    else
    {
        // 距离较小，直接自旋到终点角度
        LOG_INFO("add spin traj, dist_diff: {} start theta: {} end theta: {}",
                 dist_diff, start_pose.theta, end_pose.theta);
        spin_traj.emplace_back(start_pose.x, start_pose.y, end_pose.theta);
        connect_trajs.emplace_back(spin_traj);
    }
    return connect_trajs;
}

double ConnectTrajectoryGenerator::CalculateConnectLength(const TrajectoryPose &pose1, const TrajectoryPose &pose2) const
{
    double theta1 = pose1.theta;
    double theta2 = pose2.theta;
    double kappa1 = CalculateKappa(pose1.linear_velocity, pose1.angular_velocity);
    double kappa2 = CalculateKappa(pose2.linear_velocity, pose2.angular_velocity);
    double kappa_len = CalculateLength(theta1, kappa1, theta2, kappa2);
    double straight_len = std::hypot(pose1.x - pose2.x, pose1.y - pose2.y);
    double kappa_len_ratio = 1.5;
    return std::min(2 * straight_len, kappa_len_ratio * kappa_len);
}

double ConnectTrajectoryGenerator::CalculateKappa(double linear_velocity, double angular_velocity) const
{
    if (abs(linear_velocity) < 1e-6)
    {
        return 0;
    }
    return abs(angular_velocity / linear_velocity);
}

std::vector<TrajectoryPose> ConnectTrajectoryGenerator::GenerateTrajectory(const TrajectoryPose &pose, double linear_velocity, double angular_velocity, double distance) const
{
    std::vector<TrajectoryPose> trajectory;
    double dt = 0.1;
    double total_time = abs(distance / linear_velocity);
    double time = 0;
    TrajectoryPose cur_pose = pose;
    cur_pose.linear_velocity = linear_velocity;
    cur_pose.angular_velocity = angular_velocity;
    trajectory.push_back(cur_pose);
    while (time < total_time + 1e-6)
    {
        cur_pose.x += linear_velocity * cos(cur_pose.theta) * dt;
        cur_pose.y += linear_velocity * sin(cur_pose.theta) * dt;
        cur_pose.theta = NormalizeAngle(cur_pose.theta + angular_velocity * dt);
        cur_pose.linear_velocity = linear_velocity;
        cur_pose.angular_velocity = angular_velocity;
        trajectory.push_back(cur_pose);
        time += dt;
    }
    return trajectory;
}

std::vector<std::vector<TrajectoryPose>> ConnectTrajectoryGenerator::GenerateTrajs(const TrajectoryPose &pose,
                                                                                   const TrajectoryDirection &direction,
                                                                                   bool is_reverse) const
{
    double linear_velocity = 0.15;
    double min_angular_velocity = 0;
    double max_angular_velocity = 1.0;
    double interval_angular_velocity = 0.1;
    double min_distance = 0.1;
    double max_distance = 0.2;
    double interval_distance = 0.01;
    if (is_reverse)
    {
        if (direction == TrajectoryDirection::TURNING_LEFT_FORWARD || direction == TrajectoryDirection::TURNING_RIGHT_FORWARD)
        {
            linear_velocity = -linear_velocity;
        }
    }
    else if (direction == TrajectoryDirection::TURNING_LEFT_BACKWARD || direction == TrajectoryDirection::TURNING_RIGHT_BACKWARD)
    {
        linear_velocity = -linear_velocity;
    }
    std::vector<Trajectory> trajs;
    for (double angular_velocity = min_angular_velocity; angular_velocity < max_angular_velocity + 1e-6; angular_velocity += interval_angular_velocity)
    {
        double cur_angular_velocity = angular_velocity;
        if (is_reverse)
        {
            if (direction == TrajectoryDirection::TURNING_LEFT_FORWARD || direction == TrajectoryDirection::TURNING_LEFT_BACKWARD)
            {
                cur_angular_velocity = -angular_velocity;
            }
        }
        else if (direction == TrajectoryDirection::TURNING_RIGHT_FORWARD || direction == TrajectoryDirection::TURNING_RIGHT_BACKWARD)
        {
            cur_angular_velocity = -angular_velocity;
        }
        for (double distance = min_distance; distance < max_distance + 1e-6; distance += interval_distance)
        {
            Trajectory trajectory = GenerateTrajectory(pose, linear_velocity, cur_angular_velocity, distance);
            if (is_reverse)
            {
                for (auto &traj_pose : trajectory)
                {
                    traj_pose.linear_velocity = -traj_pose.linear_velocity;
                    traj_pose.angular_velocity = -traj_pose.angular_velocity;
                }
                std::reverse(trajectory.begin(), trajectory.end());
            }
            trajs.push_back(trajectory);
        }
    }
    return trajs;
}

double ConnectTrajectoryGenerator::CalculateTrajectoryLength(const Trajectory &traj) const
{
    double length = 0;
    for (size_t i = 1; i < traj.size(); ++i)
    {
        length += std::hypot(traj[i].x - traj[i - 1].x, traj[i].y - traj[i - 1].y);
    }
    return length;
}

double ConnectTrajectoryGenerator::CalculateTrajectoryAngleChange(const Trajectory &traj) const
{
    double angle_change = 0;
    if (traj.size() <= 1)
    {
        return angle_change;
    }
    double length = CalculateTrajectoryLength(traj);
    for (size_t i = 1; i < traj.size(); ++i) {
        angle_change += abs(NormalizeAngle(traj[i].theta - traj[i - 1].theta));
    }
    if (length < 0.05 && angle_change > 1.57) {
        return 1e6;
    } else if (length < 0.01) {
        return 1e6;
    }
    angle_change /= length;
    return angle_change;
}

double ConnectTrajectoryGenerator::CalculateMaxAngleChange(const Trajectory& traj) const {
    double max_angle_change = 0;
    if (traj.size() < 5) {
        return max_angle_change;
    }
    for (size_t i = 2; i < traj.size() - 2; ++i) {
        double angle_change = abs(NormalizeAngle(traj[i].theta - traj[i - 1].theta)) + 
                              abs(NormalizeAngle(traj[i].theta - traj[i + 1].theta)) + 
                              abs(NormalizeAngle(traj[i + 1].theta - traj[i + 2].theta)) + 
                              abs(NormalizeAngle(traj[i - 1].theta - traj[i - 2].theta));
        max_angle_change = std::max(max_angle_change, angle_change);
    }
    return max_angle_change;
}

double ConnectTrajectoryGenerator::CalculateNearPointAngleChange(const Trajectory& traj) const {
    double average_angle_change = 0;
    if (traj.size() <= 1)
    {
        return average_angle_change;
    }
    double ratio_dist = 0.1;
    for (size_t i = 1; i < traj.size(); ++i)
    {
        // 角度变化的比例系数，在靠近起点和终点时，比例系数较大。在远离期待你和终点处，比例系数为0
        double angle_ratio = 0;
        double dist_to_start = std::hypot(traj[i].x - traj[0].x, traj[i].y - traj[0].y);
        double dist_to_end = std::hypot(traj[i].x - traj[traj.size() - 1].x, traj[i].y - traj[traj.size() - 1].y);
        if (dist_to_start < ratio_dist || dist_to_end < ratio_dist)
        {
            angle_ratio = 1;
        }
        else
        {
            angle_ratio = 0;
        }
        average_angle_change += angle_ratio * abs(NormalizeAngle(traj[i].theta - traj[i - 1].theta));
    }
    if (traj.size() > 1)
    {
        average_angle_change /= (traj.size() - 1);
    }
    return average_angle_change;
}

bool ConnectTrajectoryGenerator::IsCollision(const ConnectTrajs &trajs, const std::shared_ptr<SDFMap> &sdf_map) const
{
    if (IsTrajCollision(trajs.start_traj, sdf_map) || IsTrajCollision(trajs.connect_traj, sdf_map) || IsTrajCollision(trajs.end_traj, sdf_map))
    {
        return true;
    }
    return false;
}

bool ConnectTrajectoryGenerator::IsTrajCollision(const Trajectory &traj, const std::shared_ptr<SDFMap> &sdf_map) const
{
    // TODO: 先简单实现，只判断中心点的障碍物距离
    double collision_dist = 0.2;
    for (const auto &pose : traj)
    {
        Point2f point(pose.x, pose.y);
        auto grid = sdf_map->ConvertToGrid(point);
        if (!sdf_map->IsInside(grid))
        {
            break;
        }
        double dist = sdf_map->GetPreciseDist(grid.x, grid.y);
        if (dist < collision_dist)
        {
            return true;
        }
    }
    return false;
}

ConnectTrajs ConnectTrajectoryGenerator::SelectBestTrajectory(const std::vector<ConnectTrajs> &trajs, const std::shared_ptr<SDFMap> &sdf_map) const
{
    double length_ratio = 1.0;
    double near_point_angle_change_ratio = 1.0;
    double angle_change_ratio = 6.0;
    double max_angle_change_ratio = 6.0;
    double max_connect_traj_length = 0.25;
    // 分数越低，越优
    double best_score = 1e6;
    ConnectTrajs best_traj;
    bool is_back = false;
    for (const auto &traj : trajs)
    {
        if (traj.start_traj.back().linear_velocity < 0)
        {
            is_back = true;
            break;
        }
    }
    if (is_back)
    {
        length_ratio = 10.0;
    }
    for (const auto &traj : trajs)
    {
        if (IsCollision(traj, sdf_map))
        {
            continue;
        }
        double length = CalculateTrajectoryLength(traj.connect_traj);
        if (length > max_connect_traj_length + 1e-6)
        {
            continue;
        }
        double near_point_angle_change = CalculateNearPointAngleChange(traj.connect_traj);
        double angle_change = CalculateTrajectoryAngleChange(traj.connect_traj);
        double max_angle_change = CalculateMaxAngleChange(traj.connect_traj);
        double score = length_ratio * length + near_point_angle_change_ratio * near_point_angle_change + angle_change_ratio * angle_change + max_angle_change_ratio * max_angle_change;
        // std::cout << "length: " << length 
        //           << " near_point_angle_change: " << near_point_angle_change 
        //           << " angle_change: " << angle_change 
        //           << " max_angle_change: " << max_angle_change 
        //           << " score: " << score
        //           << std::endl;
        if (score < best_score) {
            best_score = score;
            best_traj = traj;
        }
    }
    // std::cout << "best traj length: " << CalculateTrajectoryLength(best_traj.connect_traj) 
    //           << " near_point_angle_change: " << CalculateNearPointAngleChange(best_traj.connect_traj) 
    //           << " angle_change: " << CalculateTrajectoryAngleChange(best_traj.connect_traj) 
    //           << " max_angle_change: " << CalculateMaxAngleChange(best_traj.connect_traj) 
    //           << " score: " << best_score
    //           << std::endl;
    return best_traj;
}

} // namespace fescue_iox
