#include "process_fusion.hpp"

#include "opencv2/opencv.hpp"
#include "utils/logger.hpp"

namespace fescue_iox
{

void GetFusionGrassDetectStatus(const fescue_msgs__msg__PerceptionFusionResult &data, GrassDetectStatus &status)
{
    switch (data.boundary_state)
    {
    case 0:
        status = GrassDetectStatus::NO_GRASS; // 无草地（全是障碍物）
        LOG_DEBUG_THROTTLE(1000, "[GetFusionGrassDetectStatus]无草地");
        break;
    case 1:
        status = GrassDetectStatus::HAVE_GRASS_NO_OBSTACLE; // 有草地无障碍物 (全是草地)
        LOG_DEBUG_THROTTLE(1000, "[GetFusionGrassDetectStatus]全是草地");
        break;
    case 2:
        status = GrassDetectStatus::HAVE_GRASS_HAVE_OBSTACLE; // 有草地有障碍物 （部分草地部分障碍物）
        LOG_DEBUG_THROTTLE(1000, "[GetFusionGrassDetectStatus]部分草地部分障碍物");
        break;
    default:
        LOG_DEBUG_THROTTLE(1000, "[GetFusionGrassDetectStatus] Invalid perception grass detect status: {}", data.boundary_state);
        status = GrassDetectStatus::NO_GRASS;
        break;
    }
}

void GetFusionObstacleResult(const fescue_msgs__msg__PerceptionFusionResult &data, ObstacleResult &result)
{
    float left_min_distance = data.left_min_distance;
    float ahead_min_distance = data.ahead_min_distance;
    float right_min_distance = data.right_min_distance;
    result.left_obstacle_status = data.left_boundary == 0 ? ObstacleDetectStatus::NO_OBSTACLE : ObstacleDetectStatus::HAVE_OBSTACLE;
    result.ahead_obstacle_status = data.ahead_boundary == 0 ? ObstacleDetectStatus::NO_OBSTACLE : ObstacleDetectStatus::HAVE_OBSTACLE;
    result.right_obstacle_status = data.right_boundary == 0 ? ObstacleDetectStatus::NO_OBSTACLE : ObstacleDetectStatus::HAVE_OBSTACLE;
    result.obstacle_distance = (left_min_distance < ahead_min_distance)
                                   ? ((left_min_distance < right_min_distance) ? left_min_distance : right_min_distance)
                                   : ((ahead_min_distance < right_min_distance) ? ahead_min_distance : right_min_distance);
}

void GetFusionBoundaryResult(const fescue_msgs__msg__PerceptionFusionResult &data, BoundaryResult &result)
{
    result.inverse_perspect_mask.width = data.inverse_perspect_mask.width;
    result.inverse_perspect_mask.height = data.inverse_perspect_mask.height;
    result.inverse_perspect_mask.size = data.inverse_perspect_mask.step * data.inverse_perspect_mask.height;
    result.inverse_perspect_mask.pixels_to_meters = data.pixels_to_meters;
    result.inverse_perspect_mask.image = cv::Mat(data.inverse_perspect_mask.height, data.inverse_perspect_mask.width, CV_8UC1,
                                                 (void *)data.inverse_perspect_mask.data.data());
    // cv::imwrite("/userdata/image/inverse_perspect_mask.png", result.inverse_perspect_mask.image);
}

void GetFusionOccupancyResult(const fescue_msgs__msg__PerceptionFusionResult &data, OccupancyResult &result)
{
    result.width = data.bev_grass_region.width;
    result.height = data.bev_grass_region.height;
    result.resolution = data.bev_grass_region.resolution;
    result.grid.clear();

#if 1
    for (int j = 0; j < result.height; j++)
    {
        std::vector<uint8_t> cells_array;
        cells_array.reserve(result.width);
        for (int i = 0; i < result.width; i++)
        {
            // LOG_INFO("******* {}", data.bev_grass_region.cells_array[j * result.width + i]);
            uint8_t grass_val = (data.bev_grass_region.cells_array[j * result.width + i] == 0) ? 0 : 1;
            cells_array.push_back(grass_val);
        }
        result.grid.push_back(cells_array);
    }
#endif

#if 0
    // result.cells_array = data.bev_grass_region.cells_array;
    int idx = -1;
    // std::vector<uint8_t> std_vec(data.bev_grass_region.cells_array.begin(), data.bev_grass_region.cells_array.begin() + data.bev_grass_region.cells_array.size());
    for (int i = 0; i < result.height; i++)
    {
        // std::vector<uint8_t> vector_row;
        for (int j = 0; j < result.width; j++)
        {
            idx = j + i * result.width;
            //std::cout << idx;
            // vector_row.push_back(result.cells_array[idx]);
            result.cells_array.push_back(data.bev_grass_region.cells_array[idx]);
            // std::cout << std_vec[idx];
        }
        std::cout << std::endl;
        // result.grid.push_back(vector_row);
    }
#endif

#if 0
    LOG_ERROR("data width: {}", data.bev_grass_region.width);
    LOG_ERROR("Occupancy width: {}", result.width);
    LOG_ERROR("data height: {}", data.bev_grass_region.height);
    LOG_ERROR("Occupancy heigth: {}", result.height);
    LOG_ERROR("data resolution: {}", data.bev_grass_region.resolution);
    LOG_ERROR("Occupancy resolution: {}", result.resolution);
    LOG_ERROR("result.grid size: {}", result.grid.size());
    LOG_ERROR("result.grid size size: {}", result.grid[0].size());
    for (int i = 0; i < result.height; i++)
    {
        for (int j = 0; j < result.width; j++)
        {
            // std::cout << result.grid[i][j];
            if (result.grid[i][j] == 0)
            {
                std::cout << 0;
            }
            else
            {
                std::cout << 1;
            }
        }
        std::cout << std::endl;
    }
#endif
}

void GetOptFusionOccupancyResult(const fescue_msgs__msg__PerceptionFusionResult &data, OccupancyResult &result)
{
    result.width = data.bev_grass_region.width;
    result.height = data.bev_grass_region.height;
    result.resolution = data.bev_grass_region.resolution;
    result.grid.clear();

#if 1
    if (data.opt_status)
    {
        LOG_INFO("Pitch is Big And We Use Opt Fusion");
        for (int j = 0; j < result.height; j++)
        {
            std::vector<uint8_t> cells_array;
            cells_array.reserve(result.width);
            for (int i = 0; i < result.width; i++)
            {
                // LOG_INFO("******* {}", data.bev_grass_region.cells_array[j * result.width + i]);
                uint8_t grass_val = (data.bev_grass_region_opt.cells_array[j * result.width + i] == 0) ? 0 : 1;
                cells_array.push_back(grass_val);
            }
            result.grid.push_back(cells_array);
        }
    }
    else
    {
        for (int j = 0; j < result.height; j++)
        {
            std::vector<uint8_t> cells_array;
            cells_array.reserve(result.width);
            for (int i = 0; i < result.width; i++)
            {
                uint8_t grass_val = (data.bev_grass_region.cells_array[j * result.width + i] == 0) ? 0 : 1;
                cells_array.push_back(grass_val);
            }
            result.grid.push_back(cells_array);
        }
    }

#endif

#if 0
    // result.cells_array = data.bev_grass_region.cells_array;
    int idx = -1;
    // std::vector<uint8_t> std_vec(data.bev_grass_region.cells_array.begin(), data.bev_grass_region.cells_array.begin() + data.bev_grass_region.cells_array.size());
    for (int i = 0; i < result.height; i++)
    {
        // std::vector<uint8_t> vector_row;
        for (int j = 0; j < result.width; j++)
        {
            idx = j + i * result.width;
            //std::cout << idx;
            // vector_row.push_back(result.cells_array[idx]);
            result.cells_array.push_back(data.bev_grass_region.cells_array[idx]);
            // std::cout << std_vec[idx];
        }
        std::cout << std::endl;
        // result.grid.push_back(vector_row);
    }
#endif

#if 0
    LOG_ERROR("data width: {}", data.bev_grass_region.width);
    LOG_ERROR("Occupancy width: {}", result.width);
    LOG_ERROR("data height: {}", data.bev_grass_region.height);
    LOG_ERROR("Occupancy heigth: {}", result.height);
    LOG_ERROR("data resolution: {}", data.bev_grass_region.resolution);
    LOG_ERROR("Occupancy resolution: {}", result.resolution);
    LOG_ERROR("result.grid size: {}", result.grid.size());
    LOG_ERROR("result.grid size size: {}", result.grid[0].size());
    for (int i = 0; i < result.height; i++)
    {
        for(int j = 0; j < result.width ; j++)
        {
            // std::cout << result.grid[i][j];
            if(result.grid[i][j] == 0){
                std::cout << 0;
            }
            else
            {
                std::cout << 1;
            }
        }
        std::cout<<std::endl;
    }
#endif
}

std::shared_ptr<SDFMap> GetSDFMap(const OccupancyResult& occupancy_result, const Pose2f& cur_pose) 
{
    double resolution = 0.01;
    int width = 300;
    int height = width;
    double width_length = width * resolution;
    double height_length = height * resolution;
    Point2f map_origin(cur_pose.x, cur_pose.y);
    map_origin.x = map_origin.x - width_length / 2;
    map_origin.y = map_origin.y - height_length / 2;
    auto sdf_map = std::make_shared<SDFMap>(resolution, width, height, map_origin);
    // LOG_INFO("x min: {}, y min: {}, x max: {}, y max: {} occupancy width: {}, height: {}", 
    //          map_origin.x, map_origin.y, map_origin.x + width_length, map_origin.y + height_length, occupancy_result.width, occupancy_result.height);
    if (occupancy_result.width <= 0 || occupancy_result.resolution < 0) 
    {
        LOG_WARN("occupancy result: width={} resolution={}", occupancy_result.width, occupancy_result.resolution);
        sdf_map->Commit();
        return sdf_map;
    }
    // bev坐标偏置
    double bev_x_offset = 1.05;
    double bev_y_offset = 0.5;
    double cos_theta = cos(cur_pose.theta);
    double sin_theta = sin(cur_pose.theta);
    for (int i = 0; i < occupancy_result.height; i++)
    {
        for(int j = 0; j < occupancy_result.width ; j++)
        {
            if (occupancy_result.grid[i][j] == 0) 
            {
                continue;   
            }
            double bev_map_x = j * occupancy_result.resolution;
            double bev_map_y = i * occupancy_result.resolution;
            double robot_map_x = bev_map_y + bev_x_offset;
            double robot_map_y = -bev_map_x + bev_y_offset;
            Point2f robot_point(robot_map_x, robot_map_y);
            double map_x = cur_pose.x + robot_point.x * cos_theta - robot_point.y * sin_theta;
            double map_y = cur_pose.y + robot_point.x * sin_theta + robot_point.y * cos_theta;
            Point2f map_point(map_x, map_y);
            const auto& grid = sdf_map->ConvertToGrid(map_point);
            if (!sdf_map->IsInside(grid))
            {
                continue;
            }
            sdf_map->AddPoint(grid);
        }
    }
    sdf_map->Commit();
    return sdf_map;
}

} // namespace fescue_iox
