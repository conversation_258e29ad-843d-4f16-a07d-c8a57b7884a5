#include "slope_control_config.hpp"

#include <sstream>

namespace fescue_iox
{

NavigationSlopeControlAlgConfig &NavigationSlopeControlAlgConfig::operator=(const NavigationSlopeControlAlgConfig &config)
{
    if (this != &config)
    {
        k_linear = config.k_linear;
        k_angular = config.k_angular;
    }
    return *this;
}

std::string NavigationSlopeControlAlgConfig::toString() const
{
    std::stringstream ss;
    ss << "------------------------- " << typeid(*this).name() << " -------------------------\r\n";
    ss << "  k_linear: " << k_linear << "\n";
    ss << "  k_angular: " << k_angular << "\n";
    ss << "------------------------- " << typeid(*this).name() << " -------------------------";
    return ss.str();
}

bool operator==(const NavigationSlopeControlAlgConfig &lhs, const NavigationSlopeControlAlgConfig &rhs)
{
    return lhs.k_linear == rhs.k_linear &&
           lhs.k_angular == rhs.k_angular;
}

bool operator!=(const NavigationSlopeControlAlgConfig &lhs, const NavigationSlopeControlAlgConfig &rhs)
{
    return !(lhs == rhs);
}

/**
 * @brief 从配置文件中加载 NavigationSlopeControlAlgConfig 参数
 * TypedBadConversion是模板类，读取什么类型的参数就传入什么类型
 * @tparam
 * @param conf NavigationSlopeControlAlgConfig 参数结构体
 * @param conf_file 配置文件路径
 * @return true
 * @return false
 */
template <>
bool Config<NavigationSlopeControlAlgConfig>::LoadConfig(NavigationSlopeControlAlgConfig &conf, const std::string &conf_file)
{
    YAML::Node node;
    try
    {
        node = YAML::LoadFile(conf_file);
    }
    catch (const YAML::BadFile &e)
    {
        std::cerr << "NavigationSlopeControlAlgConfig load config fail (BadFile): " << e.what() << '\n';
        return false;
    }
    catch (const YAML::ParserException &e)
    {
        std::cerr << "NavigationSlopeControlAlgConfig load config fail (ParserException): " << e.what() << '\n';
        return false;
    }
    catch (const std::exception &e)
    {
        std::cerr << "NavigationSlopeControlAlgConfig load config fail (Unknown): " << e.what() << '\n';
        return false;
    }

    conf.k_linear = GetYamlValue<double>(node, "k_linear", conf.k_linear);
    conf.k_angular = GetYamlValue<double>(node, "k_angular", conf.k_angular);

    return true;
}

/**
 * @brief 创建配置文件
 *
 * @tparam
 * @param conf NavigationSlopeControlAlgConfig 参数结构体
 * @param conf_file 配置文件路径
 * @return true
 * @return false
 */
template <>
bool Config<NavigationSlopeControlAlgConfig>::CreateConfig(const NavigationSlopeControlAlgConfig &conf, const std::string &conf_file)
{
    YAML::Node node;
    node["k_linear"] = conf.k_linear;
    node["k_angular"] = conf.k_angular;
    return WriteYamlFile(conf_file, node);
}

} // namespace fescue_iox
