#ifndef NAVIGATION_PATH_TRACK_ALG_HPP
#define NAVIGATION_PATH_TRACK_ALG_HPP

#include "geometry_msgs/point__struct.h"
#include "geometry_msgs/twist__struct.h"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
// #include "iceoryx_hoofs/cxx/forward_list.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/client.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/server.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "moving_average_filter.hpp"
#include "ob_mower_msgs/nav_alg_ctrl__struct.h"
#include "ob_mower_msgs/nav_edge_follow_data__struct.h"
#include "utils/math_type.hpp"
#include "velocity_smooth.hpp"

#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{

struct PathTrackAlgParam
{
    float path_point_reached_threshold{0.195};
    float max_linear_v{0.2};
    float max_angular_w{0.3};

    double lookahead_dist{1.5};
    double desired_linear_vel{0.2};
    double regulated_linear_scaling_min_radius{1.0};
    double regulated_linear_scaling_min_speed{0.05};
    bool use_velocity_scaled_lookahead_dist{false};
    double lookahead_time{1.5};
    double min_lookahead_dist{0.3};
    double max_lookahead_dist{0.6};

    int filter_time{2};
    float safety_distance_along_edge{0.4}; // Safety distance along the edge

    // Robot position in the image
    float robot_in_map_x{198.0};
    float robot_in_map_y{-196.0};
    float robot_in_map_yaw{1.57};
};

struct PathTrackAlgResult
{
    float linear{0};
    float angular{0};
};

class NavigationPathTrackAlg
{
public:
    NavigationPathTrackAlg(const PathTrackAlgParam &param);
    ~NavigationPathTrackAlg();
    int DoPathTrack(const fescue_msgs__msg__NavigationEdgeFollowData &data, PathTrackAlgResult &result);
    void SetPathTrackParam(const PathTrackAlgParam &param);

private:
    bool GetEdgeFollowTrackPath(const fescue_msgs__msg__NavigationEdgeFollowData &data);
    geometry_msgs__msg__PoseStamped_iox GetEdgeFollowGoalPose(std::vector<geometry_msgs__msg__PoseStamped_iox> &track_path_local);
    PathTrackAlgResult GetEdgeFollowTwist(const geometry_msgs__msg__PoseStamped_iox &goal_pose);
    std::vector<geometry_msgs__msg__PoseStamped_iox> GetEdgeFollowTrackPathLocal(const nav_msgs__msg__Path_iox &track_path);
    double GetLookAheadDistance(float linear);
    void ApplyConstraints(const double &curvature, double &linear_vel);
    geometry_msgs__msg__Point_iox CircleSegmentIntersection(const geometry_msgs__msg__Point_iox &p1,
                                                            const geometry_msgs__msg__Point_iox &p2,
                                                            double r);

private:
    VecXYW robot_pose_;
    nav_msgs__msg__Path_iox track_path_;

    // Parameters
    float path_point_reached_threshold_;
    float max_linear_v_;
    float max_angular_w_;
    double desired_linear_vel_;
    double regulated_linear_scaling_min_radius_;
    double regulated_linear_scaling_min_speed_;
    float safety_distance_along_edge_; // Safety distance along the edge
    float resolution_;

    double lookahead_dist_;
    bool use_velocity_scaled_lookahead_dist_;
    double lookahead_time_;
    double min_lookahead_dist_;
    double max_lookahead_dist_;
    int filter_time_;

    // Robot position in the image
    float robot_in_map_x_;
    float robot_in_map_y_;
    float robot_in_map_yaw_;

    std::unique_ptr<MovingAverageFilter> filter_{nullptr};
};

} // namespace fescue_iox

#endif
