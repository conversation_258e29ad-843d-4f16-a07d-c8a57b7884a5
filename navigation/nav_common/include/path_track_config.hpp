#pragma once

#include "utils/config.hpp"

#include <string>

namespace fescue_iox
{

struct NavigationPathTrackAlgConfig
{
    float path_point_reached_threshold{0.195};
    float max_linear_v{0.2};
    float max_angular_w{0.3};

    double lookahead_dist{1.5};
    double desired_linear_vel{0.2};
    double regulated_linear_scaling_min_radius{1.0};
    double regulated_linear_scaling_min_speed{0.05};
    bool use_velocity_scaled_lookahead_dist{false};
    double lookahead_time{1.5};
    double min_lookahead_dist{0.3};
    double max_lookahead_dist{0.6};

    int filter_time{2};
    float safety_distance_along_edge{0.4}; // Safety distance along the edge

    // Robot position in the map
    float robot_in_map_x{198.0};
    float robot_in_map_y{-196.0};
    float robot_in_map_yaw{1.57};

    NavigationPathTrackAlgConfig() = default;
    ~NavigationPathTrackAlgConfig() = default;
    NavigationPathTrackAlgConfig(const NavigationPathTrackAlgConfig &config) = default;
    NavigationPathTrackAlgConfig &operator=(const NavigationPathTrackAlgConfig &config);
    std::string toString() const;
};

bool operator==(const NavigationPathTrackAlgConfig &lhs, const NavigationPathTrackAlgConfig &rhs);
bool operator!=(const NavigationPathTrackAlgConfig &lhs, const NavigationPathTrackAlgConfig &rhs);

} // namespace fescue_iox
