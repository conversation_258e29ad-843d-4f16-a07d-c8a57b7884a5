#pragma once

#include "utils/config.hpp"

#include <string>
#include <vector>

namespace fescue_iox
{

/**
 * @brief 斜坡控制节点配置
 * @param k_linear 线速度补偿系数
 * @param k_angular 角速度补偿系数
 */
struct NavigationSlopeControlAlgConfig
{
    double k_linear{0.0};
    double k_angular{0.35};
    NavigationSlopeControlAlgConfig() = default;
    ~NavigationSlopeControlAlgConfig() = default;
    NavigationSlopeControlAlgConfig(const NavigationSlopeControlAlgConfig &config) = default;
    NavigationSlopeControlAlgConfig &operator=(const NavigationSlopeControlAlgConfig &config);
    std::string toString() const;
};

bool operator==(const NavigationSlopeControlAlgConfig &lhs, const NavigationSlopeControlAlgConfig &rhs);
bool operator!=(const NavigationSlopeControlAlgConfig &lhs, const NavigationSlopeControlAlgConfig &rhs);

} // namespace fescue_iox
