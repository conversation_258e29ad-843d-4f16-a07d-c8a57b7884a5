#pragma once

#include "data_type.hpp"
#include "sdf_map.hpp"

namespace fescue_iox
{

enum class TrajectoryDirection {
    TURNING_LEFT_FORWARD,
    TURNING_RIGHT_FORWARD,
    TURNING_LEFT_BACKWARD,
    TURNING_RIGHT_BACKWARD,
    FORWARD,
    BACKWARD,
};

enum class TrajectoryMode {
    // 正常模式
    TRAJECTORY_MODE_NORMAL = 0,
    // 尽量前方朝向目标点（不越过目标点）的模式
    TRAJECTORY_MODE_HEADING_GOAL = 1,
};

struct ConnectTrajs {
    std::vector<TrajectoryPose> start_traj;
    std::vector<TrajectoryPose> connect_traj;
    std::vector<TrajectoryPose> end_traj;
};
    
class ConnectTrajectoryGenerator
{
    using Trajectory = std::vector<TrajectoryPose>;

public:
    ConnectTrajectoryGenerator(TrajectoryMode mode);

    /**
     * @brief 用于衔接2个位姿，使用前进后退的腾挪轨迹衔接，不做自旋
     * @param start_pose 起始位置
     * @param end_pose 终点位置
     * @return 多段轨迹
     */
    std::vector<std::vector<TrajectoryPose>> ConnectPose(const TrajectoryPose &start_pose, const TrajectoryPose &end_pose);

    std::vector<std::vector<TrajectoryPose>> ConnectPose(const TrajectoryPose &start_pose, const TrajectoryPose &end_pose, const OccupancyResult& start_pose_result);

private:
    std::vector<TrajectoryPose> GenerateTrajectory(const TrajectoryPose& pose, double linear_velocity, double angular_velocity, double distance) const;
    std::vector<std::vector<TrajectoryPose>> GenerateTrajs(const TrajectoryPose& pose, const TrajectoryDirection& direction, bool is_reverse = false) const;
    ConnectTrajs SelectBestTrajectory(const std::vector<ConnectTrajs>& trajs, const std::shared_ptr<SDFMap>& sdf_map) const;
    double CalculateNearPointAngleChange(const Trajectory& traj) const;
    double CalculateTrajectoryAngleChange(const Trajectory& traj) const;
    double CalculateTrajectoryLength(const Trajectory& traj) const;
    std::vector<TrajectoryPose> CubicSplineConnect(double x1, double y1, double theta1, double kappa1, 
                                           double x2, double y2, double theta2, double kappa2, 
                                           double L, bool is_forward) const;
    double CalculateLength(double theta1, double kappa1, double theta2, double kappa2) const;
    double CalculateKappa(double linear_velocity, double angular_velocity) const;
    double CalculateConnectLength(const TrajectoryPose& pose1, const TrajectoryPose& pose2) const;
    std::vector<std::vector<TrajectoryDirection>> GenerateTrajectoryDirections(
        const TrajectoryPose &start_pose, const TrajectoryPose &end_pose) const;
    bool IsCollision(const ConnectTrajs& trajs, const std::shared_ptr<SDFMap>& sdf_map) const;
    Pose2f GetRelativePose(const Pose2f &base_pose, const Pose2f &pose) const;
    bool IsTrajCollision(const Trajectory& traj, const std::shared_ptr<SDFMap>& sdf_map) const;
    std::vector<TrajectoryPose> LineConnectPose(const TrajectoryPose& start_pose, const TrajectoryPose& end_pose) const;
    std::vector<std::vector<TrajectoryPose>> SpinConnectPose(const TrajectoryPose& start_pose, const TrajectoryPose& end_pose) const;
    std::vector<std::vector<TrajectoryDirection>> GenerateHeadingGoalTrajectoryDirections(
        const TrajectoryPose &start_pose, const TrajectoryPose &end_pose) const;
    double CalculateMaxAngleChange(const Trajectory& traj) const;

private:
    TrajectoryMode mode_ = TrajectoryMode::TRAJECTORY_MODE_NORMAL;
};

} // namespace fescue_iox