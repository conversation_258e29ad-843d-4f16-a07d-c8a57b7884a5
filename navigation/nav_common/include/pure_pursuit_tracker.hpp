#pragma once

#include <memory>

#include "data_type.hpp"

namespace fescue_iox
{

struct PurePursuitConfig {
    double min_linear = 0.1;
    double max_linear = 0.2;
    double min_angular = 0;
    double max_angular = 1.5;
    double look_ahead_dist = 0.05;
    double look_ahead_dist_ratio = 1.0;
    double angle_diff_ratio = 1.0;
    double big_angle_diff_ratio = 3.0;
    double lat_dist_ratio = 1.5;
    double small_lat_dist_ratio = 0.8;
    double near_goal_dist = 0.15;
    double arrived_dist = 0.1;
    double timeout_time_ratio = 2;
    bool try_reach_goal_angle = true;
    double spin_timeout_time = 6.0;
    double spin_angular_ratio = 0.1;
    double spin_min_angular = 0.2;
    double spin_max_angular = 1.0;
    double spin_arrived_angle = 0.174;
    double small_spin_arrived_angle = 0.02;
};

enum class TrackerResult : int {
    kRunning = 0,
    kArrived = 1,
    kTimeout = 2,
    kInvalid = 3,
};

const char* TrackerResultString(const TrackerResult& result);

class PurePursuitTracker
{
public:
    PurePursuitTracker(const std::vector<TrajectoryPose>& traj, const PurePursuitConfig& config);

    void Update(const TrajectoryPose& pose, const OdomResult& odom_result);

    double GetLinearVelocity() const { return linear_velocity_; }
    double GetAngularVelocity() const { return angular_velocity_; }
    TrackerResult GetResult() const { return result_; }

private:
    void UpdateCurrentIndex(const TrajectoryPose& pose);
    double CalculateLength(const std::vector<TrajectoryPose>& traj) const;
    bool IsSpinTraj(const std::vector<TrajectoryPose>& traj) const;
    void TrackSpinTraj(const TrajectoryPose& pose, const OdomResult& odom_result);
    void TrackMovingTraj(const TrajectoryPose& pose, const OdomResult& odom_result);

private:
    std::vector<TrajectoryPose> traj_;
    PurePursuitConfig config_;
    double linear_velocity_ = 0;
    double angular_velocity_ = 0;
    TrackerResult result_ = TrackerResult::kInvalid;
    int look_ahead_index_ = -1;
    int min_dist_index_ = -1;
    double last_dist_to_goal_ = -1;
    double timeout_time_ = -1;
    uint64_t start_time_ = 0;
    bool is_spin_traj_ = false;
    double last_angle_diff_ = 1e6;
};

} // namespace fescue_iox