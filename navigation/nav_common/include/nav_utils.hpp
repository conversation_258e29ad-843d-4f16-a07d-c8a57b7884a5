#ifndef NAV_UTILS_HPP
#define NAV_UTILS_HPP

#include "opencv2/opencv.hpp"

#include <random>
#include <vector>

namespace fescue_iox
{

std::vector<std::vector<cv::Point>> GetEdgeContours(const uint8_t *img, int width, int height);

bool getLineIntersection(cv::Point2f p0, cv::Point2f p1, cv::Point2f p2, cv::Point2f p3, cv::Point2f &i);

float calculateAngle(const cv::Point &pt1, const cv::Point &pt2);

std::vector<cv::Point> filterPointsWithinContour(const std::vector<cv::Point> &path, const std::vector<cv::Point> &contour);

std::vector<cv::Point> handlePathWithIntersections(const std::vector<cv::Point> &path, const cv::Point &p1, const cv::Point &p2);

std::vector<cv::Point> processPath(const std::vector<cv::Point> &path_temp1, const std::vector<cv::Point> &contour, const cv::Point &p1, const cv::Point &p2);

bool isNonGrassOnLeft(const cv::Mat &img, const int &dividing_line_left = 100, const int &dividing_line_right = 200);

bool isHaveNonGrass(const cv::Mat &img);

void getLineEndPoints(const cv::Point &p, double theta, const cv::Size &imageSize, cv::Point &p1, cv::Point &p2);

bool HaveIntersection(const cv::Point &min_x_point, double theta,
                      const cv::Mat &image, const std::vector<std::vector<cv::Point>> &contours_approx,
                      cv::Point &p1, cv::Point &p2);

std::vector<cv::Point> GetFollowPath(const cv::Mat &input_img,
                                     const std::vector<cv::Point> &left_dead_line,
                                     const std::vector<cv::Point> &right_dead_line,
                                     const float &resolution);

std::vector<cv::Point> GetFollowPath(const cv::Mat &input_img);

std::vector<std::vector<cv::Point>> getContours(const cv::Mat &input_img);

/**
 * @brief Random number generation template function
 */
template <typename T>
T GenerateRandomNumber(T min, T max)
{
    std::random_device rd;
    std::mt19937 gen(rd());
    typename std::conditional<std::is_integral<T>::value, std::uniform_int_distribution<T>,
                              std::uniform_real_distribution<T>>::type dis(min, max);
    return dis(gen);
}

// Turning duration in ms
inline uint64_t CalculateDuration(float dis, float vel) { return static_cast<uint64_t>((dis / vel) * 1000); }

/**
 * @brief Convert degrees to radians
 *
 * @param degrees
 * @return double
 */
inline double Degrees2Radians(double degrees) { return degrees * M_PI / 180.0; }

/**
 * @brief Convert radians to degrees
 *
 * @param radians
 * @return double
 */
inline double Radians2Degrees(double radians) { return radians * 180.0 / M_PI; }

// Define data structure for storing pose
struct Pose_Mark
{
    double x, y, z;          // Position
    double roll, pitch, yaw; // Orientation (Euler angles)
};

void poseToMatrix(const Pose_Mark &pose, double matrix[4][4]);

void matrixToPose(const double matrix[4][4], Pose_Mark &pose);

void invertMatrix(const double input[4][4], double output[4][4]);

void multiplyMatrices(const double a[4][4], const double b[4][4], double result[4][4]);

Pose_Mark calculateBaseLinkRelativeToMark(const Pose_Mark &camera_to_mark, const Pose_Mark &camera_to_base_link);

void GetVelocityFromMotorRPM(float left_rpm, float right_rpm, float wheel_radius, float wheel_base, float &linear_velocity, float &angular_velocity);

} // namespace fescue_iox

#endif
