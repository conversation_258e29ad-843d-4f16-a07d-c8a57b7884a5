#ifndef MOVING_AVERAGE_FILTER_HPP
#define MOVING_AVERAGE_FILTER_HPP

#include "geometry_msgs/pose_stamped__struct.h"

#include <deque>

namespace fescue_iox
{

class MovingAverageFilter
{
public:
    explicit MovingAverageFilter(int filter_time);
    ~MovingAverageFilter() = default;
    void setFilterTime(int filter_time);
    geometry_msgs__msg__PoseStamped_iox filter(const geometry_msgs__msg__PoseStamped_iox &new_pose);

private:
    geometry_msgs__msg__PoseStamped_iox computeAveragePose();

    std::deque<geometry_msgs__msg__PoseStamped_iox> poses_; // Stores recent poses
    int filter_time_;                                       // Filter time window
};

} // namespace fescue_iox

#endif
