#pragma once

#include "data_type.hpp"
#include "escape.hpp"
#include "escape_config.hpp"
#include "escape_node_config.hpp"
#include "geometry_msgs/twist__struct.h"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/client.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/server.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "mower_msgs/msg/mcu_exception.hpp"
#include "mower_msgs/msg/mcu_imu.hpp"
#include "mower_msgs/msg/mcu_motor_speed.hpp"
#include "mower_msgs/msg/soc_imu.hpp"
#include "ob_mower_msgs/nav_alg_ctrl__struct.h"
#include "ob_mower_msgs/nav_escape_result.h"
#include "ob_mower_msgs/nav_running_state__struct.h"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "ob_mower_srvs/nav_escape_alg_param_service.h"
#include "ob_mower_srvs/node_common_param_service.h"
#include "opencv2/opencv.hpp"
#include "utils/heartbeat_publisher.hpp"
#include "utils/iceoryx_client_mower.hpp"
#include "utils/iceoryx_publisher_mower.hpp"
#include "utils/iceoryx_server_mower.hpp"
#include "utils/iceoryx_subscriber_mower.hpp"

#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{

class NavigationEscapeNode
{
    // node param
    using get_node_param_request = ob_mower_srvs::GetNodeParamRequest;
    using get_node_param_response = ob_mower_srvs::GetNodeParamResponse;
    using set_node_param_request = ob_mower_srvs::SetNodeParamRequest;
    using set_node_param_response = ob_mower_srvs::SetNodeParamResponse;

    // alg param
    using set_alg_param_request = ob_mower_srvs::SetNavEscapeAlgParamRequest;
    using set_alg_param_response = ob_mower_srvs::SetNavEscapeAlgParamResponse;
    using get_alg_param_request = ob_mower_srvs::GetNavEscapeAlgParamRequest;
    using get_alg_param_response = ob_mower_srvs::GetNavEscapeAlgParamResponse;

public:
    NavigationEscapeNode(const std::string &node_name);
    ~NavigationEscapeNode();

private:
    void InitWorkingDirectory();
    void InitParam();
    void InitLogger();
    void InitPublisher();
    void InitSubscriber();
    void InitAlgorithm();
    void InitAlgorithmParam();
    void DeinitAlgorithm();
    void InitService();
    void InitHeartbeat();

private:
    void DealFusionResult(const fescue_msgs__msg__PerceptionFusionResult &msg);
    void DealNavAlgCtrlResult(const fescue_msgs__msg__NavigationAlgoCtrlData &msg);
    void DealNavRunningState(const fescue_msgs__msg__NavigationRunningStateData &data);
    void DealMcuImu(const mower_msgs::msg::McuImu &data);
    void DealSocImu(const mower_msgs::msg::SocImu &data);
    void DealMcuMotorSpeed(const mower_msgs::msg::McuMotorSpeed &data);
    bool DealSetNodeParam(const ob_mower_srvs::NodeParamData &data);
    bool DealGetNodeParam(ob_mower_srvs::NodeParamData &data);
    bool DealSetAlgParam(const ob_mower_srvs::NavEscapeAlgParam &data);
    bool DealGetAlgParam(ob_mower_srvs::NavEscapeAlgParam &data);
    void EscapeThread();
    void PublishResult(bool is_loop);

private:
    // 订阅
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>> sub_nav_alg_ctrl_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationRunningStateData>> sub_nav_running_state_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__PerceptionFusionResult>> sub_fusion_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuImu>> sub_mcu_imu_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::SocImu>> sub_soc_imu_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuMotorSpeed>> sub_mcu_motor_speed_{nullptr};
    // 服务
    std::unique_ptr<IceoryxServerMower<set_node_param_request, set_node_param_response>> service_set_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<get_node_param_request, get_node_param_response>> service_get_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_alg_param_request, set_alg_param_response>> service_set_alg_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<get_alg_param_request, get_alg_param_response>> service_get_alg_param_{nullptr};
    // 发布
    std::unique_ptr<IceoryxPublisherMower<ob_mower_msgs::NavEscapeResult>> pub_escape_result_{nullptr};

    // 数据
    std::mutex fusion_mtx_;
    PerceptionFusionResult fusion_result_;
    std::mutex imu_mtx_;
    ImuData imu_data_;
    std::mutex motor_speed_mtx_;
    MotorSpeedData motor_speed_data_;

private:
    std::atomic_bool escape_enable_{false}; // false
    std::thread escape_thread_;
    std::atomic_bool thread_running_{true};

    std::string node_name_{"navigation_escape_node"};
    std::string log_dir_{"/userdata/log"};
    std::string console_log_level_{"info"};
    std::string file_log_level_{"warn"};
    std::string escape_alg_conf_file_{"conf/navigation_escape_node/escape.yaml"};

    EscapeAlgParam escape_alg_param_;
    std::unique_ptr<NavigationEscapeAlg> escape_alg_;
    std::unique_ptr<NodeHeartbeatPublisher> pub_heartbeat_{nullptr};
};

} // namespace fescue_iox