#!/bin/sh

# add system mode setting
echo performance > /sys/devices/system/cpu/cpufreq/policy0/scaling_governor
echo performance > /sys/devices/platform/soc/a3000000.cnn/devfreq/devfreq1/governor

# 定义脚本路径
INSTALL_DIR="/app/active_slot/"
APP_SCRIPTS_DIR=${INSTALL_DIR}/mower_software/scripts
ALG_SCRIPTS_DIR=${INSTALL_DIR}/mower_algorithm/scripts

# 在后台执行/app/install目录下的脚本
echo "开始在后台执行 ${APP_SCRIPTS_DIR} 目录下的脚本..."
chmod 777 ${APP_SCRIPTS_DIR}/mower_sdk_roudi.sh
${APP_SCRIPTS_DIR}/mower_sdk_roudi.sh &
#  等待2秒
sleep 2
${APP_SCRIPTS_DIR}/mower_sdk_decision_maker.sh &
sleep 2
${APP_SCRIPTS_DIR}/mower_sdk_run.sh &

# run test
chmod 777 ${ALG_SCRIPTS_DIR}/mower_sdk_test.sh
${ALG_SCRIPTS_DIR}/mower_sdk_test.sh &

echo "所有脚本已在后台启动。"