#!/bin/sh

CUR_DIR=$(cd $(dirname $0); pwd)
MOWER_COMMON_LIB_DIR=$CUR_DIR/../../mower_common_lib
LIB_DIR=$CUR_DIR/../lib
BIN_DIR=$CUR_DIR/../bin

export LD_LIBRARY_PATH=$MOWER_COMMON_LIB_DIR:$LIB_DIR:$LD_LIBRARY_PATH
export PATH=$PATH:$BIN_DIR

chmod a+x -R $BIN_DIR

$BIN_DIR/navigation_cross_region_node &
$BIN_DIR/navigation_edge_follow_node &
$BIN_DIR/navigation_random_mower_node &
$BIN_DIR/navigation_recharge_node &
$BIN_DIR/navigation_behavior_node &
$BIN_DIR/navigation_spiral_mower_node &
$BIN_DIR/navigation_escape_node &

# Wait 1 second for the previous nodes to start
sleep 1
$BIN_DIR/navigation_mower_node &
