#!/bin/sh

# 定义进程名称列表
processes="navigation_mower_node
    navigation_random_mower_node
    navigation_recharge_node
    navigation_cross_region_node
    navigation_edge_follow_node
    navigation_behavior_node
    navigation_spiral_mower_node
    navigation_cut_border_node
    navigation_escape_node
    mower_sdk_navigation.sh"

# 循环遍历进程并执行 killall 命令
for process in ${processes}; do
    killall -9 "$process"
done
