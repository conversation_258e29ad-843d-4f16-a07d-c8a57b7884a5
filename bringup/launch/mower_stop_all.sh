#!/bin/sh

# Close the system_manager_node first
killall -9 system_manager_node

sleep 1

# 定义进程名称列表
processes="perception_segment_object_fusion_node
    perception_charge_mark_detection_node
    perception_occlusion_detection_node
    mower_sdk_perception.sh
    navigation_mower_node
    navigation_random_mower_node
    navigation_recharge_node
    navigation_cross_region_node
    navigation_edge_follow_node
    navigation_behavior_node
    navigation_spiral_mower_node
    navigation_cut_border_node
    navigation_escape_node
    mower_sdk_navigation.sh
    calibration_bev_node
    mower_sdk_calibration.sh
    localization_slope_detection_node
    localization_motion_detection_node
    localization_area_estimation_node
    localization_cross_region_node
    localization_recharge_node
    pcba_test_node
    cam_calib_server_node
    mower_sdk_localization.sh
    test_server_node
    mower_sdk_test.sh
    decision_maker_node
    camera_node
    mcu_communication_node
    process_monitor_node
    system_monitor_node
    cam_calib_server_node
    pcba_test_node
    ota_node
    shell_node
    mower_sdk_decision_maker.sh
    mower_sdk_run.sh
    usb_watcher_node
    iox-roudi
    mower_sdk_roudi.sh"

# 循环遍历进程并执行 killall 命令
for process in ${processes}; do
    killall -9 "$process"
done