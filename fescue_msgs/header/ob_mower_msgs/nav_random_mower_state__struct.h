#pragma once

#include "geometry_msgs/pose__struct.h"
#include "iceoryx_hoofs/cxx/vector.hpp"
// #include "nav_msgs/path__struct.h"
// #include "std_msgs/header__struct.h"

#include <chrono>
#include <cstdint>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief
 */
typedef enum fescue_msgs_enum__RandomMowerState
{
    // FESCUE_MSGS_ENUM_RANDOM_MOWER_STATE_STOP = 0,    /* 停止 */
    // FESCUE_MSGS_ENUM_RANDOM_MOWER_STATE_RUNNING = 1, /* 正在执行 */
    // FESCUE_MSGS_ENUM_RANDOM_MOWER_STATE_FINISH = 2,  /* 执行完成 */
    FESCUE_MSGS_ENUM_RANDOM_MOWER_STATE_NORMAL = 0,
    FESCUE_MSGS_ENUM_RANDOM_MOWER_STATE_TRAP_WAIT_BIAS = 1,
    FESCUE_MSGS_ENUM_RANDOM_MOWER_STATE_RUNNING_TRAP_EDGE_FOLLOW = 2,
    FESCUE_MSGS_ENUM_RANDOM_MOWER_STATE_UNDEFINED, /* 未定义 */
} fescue_msgs_enum__RandomMowerState;

/**
 * @brief 跨区域状态数据结构体
 */
typedef struct fescue_msgs__msg__RandomMowerStateData
{
    fescue_msgs_enum__RandomMowerState state;
} fescue_msgs__msg__RandomMowerStateData;

#ifdef __cplusplus
}
#endif
