#pragma once

#include "iceoryx_hoofs/cxx/vector.hpp"
#include "std_msgs/header__struct.h"

#include <cstdint>

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_NAVIGATION_ALGO_NUM 16 /* 设置的能控制的算法的最多数量 */

/**
 * @brief 算法类型ID
 */
typedef enum fescue_msgs__enum__NavigationAlgoType
{
    /* 规控沿边算法 */
    FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_EDGE_FOLLOW = 0,
    /* 规控跨区域算法 */
    FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_CROSS_REGION = 1,
    /* 规控回充算法 */
    FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_RECHARGE = 2,
    /* 规控随机割草算法 */
    FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_RANDOM_MOWER = 3,
    /* 规控行为恢复算法 */
    FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_BEHAVIOR = 4,
    /* 规控螺旋割草算法 */
    FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_SPIRAL_MOWER = 5,
    /* 规控切边割草算法 */
    FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_CUT_BORDER = 6,
    /* 规控脱困算法 */
    FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_ESCAPE = 7,
    /* 全部算法 */
    FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_ALL,
} fescue_msgs__enum__NavigationAlgoType;

/**
 * @brief 算法类型ID
 */
typedef enum fescue_msgs__enum__NavigationAlgoState
{
    FESCUE_MSGS_ENUM_NAV_ALGO_STATE_IGNORE = -1,
    FESCUE_MSGS_ENUM_NAV_ALGO_STATE_DISABLE = 0,
    FESCUE_MSGS_ENUM_NAV_ALGO_STATE_ENABLE = 1,
} fescue_msgs__enum__NavigationAlgoState;

/**
 * @brief 规控算法控制结构体类型
 */
typedef struct fescue_msgs__msg__NavigationAlgoCtrlInfo
{
    fescue_msgs__enum__NavigationAlgoType type;   /* 算法类型 */
    fescue_msgs__enum__NavigationAlgoState state; /* 控制状态，-1, 忽略，0-关闭，1-开启 */
} fescue_msgs__msg__NavigationAlgoCtrlInfo;

/**
 * @brief 规控算法控制数据
 */
typedef struct fescue_msgs__msg__NavigationAlgoCtrlData
{
    std_msgs__msg__Header_iox header;
    iox::cxx::string<64> sender;
    iox::cxx::vector<fescue_msgs__msg__NavigationAlgoCtrlInfo, MAX_NAVIGATION_ALGO_NUM> data;
} fescue_msgs__msg__NavigationAlgoCtrlData;

typedef struct fescue_msgs__srv__NavigationAlgoCtrl_Request
{
    iox::cxx::vector<fescue_msgs__msg__NavigationAlgoCtrlInfo, MAX_NAVIGATION_ALGO_NUM> data;
} fescue_msgs__srv__NavigationAlgoCtrl_Request;

typedef struct fescue_msgs__srv__NavigationAlgoCtrl_Response
{
    bool success;
    iox::cxx::string<32> message;
} fescue_msgs__srv__NavigationAlgoCtrl_Response;

#ifdef __cplusplus
}
#endif
