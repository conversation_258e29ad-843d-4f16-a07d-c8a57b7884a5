#pragma once

#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"

#include <cstdint>

#ifdef __cplusplus
extern "C" {
#endif

typedef struct fescue_msgs__msg__PerceptionOcclusionDetectionAlgParam
{
    int ignore_num;              // Number of images ignored at the beginning.
    float line1;                 // 中区比例
    float line2;                 // 下区比例
    int num_frames_mid;          // 中区最多连续检测的次数
    int min_detected_frames_mid; // 中区判断为遮挡所需累计的次数
    int num_frames_bot;          // 下区最多连续检测的次数
    int min_detected_frames_bot; // 下区判断为遮挡所需累计的次数
    int block_size;              // 块大小
    double laplacian_thresh;     // 拉普拉斯阈值
    double sobel_thresh;         // sobel阈值
    int min_area;                // debug显示时绘制的最小面积，仅对显示结果有影响
    int min_blocks_mid;          // 中区判断为遮挡的最少块数
    int min_blocks_bot;          // 下区判断为遮挡的最少块数
    int skip_frames;             // 跳帧数
    bool test_mode;              // 测试模式开关，设置为true时，会可视化每一帧的检测结果。
    int log_level;               // 0：打印所有调试信息，1：打印INFO以上调试信息， 2：打印WARN以上调试信息，3：打印ERROR调试信息。
    bool save_log;
    int debug_mode;
} fescue_msgs__msg__PerceptionOcclusionDetectionAlgParam;

typedef struct fescue_msgs__srv__SetPerceptionOcclusionDetectionAlgParam_Request
{
    fescue_msgs__msg__PerceptionOcclusionDetectionAlgParam data;
} fescue_msgs__srv__SetPerceptionOcclusionDetectionAlgParam_Request;

typedef struct fescue_msgs__srv__SetPerceptionOcclusionDetectionAlgParam_Response
{
    bool success;
    iox::cxx::string<32> message;
} fescue_msgs__srv__SetPerceptionOcclusionDetectionAlgParam_Response;

typedef struct fescue_msgs__srv__GetPerceptionOcclusionDetectionAlgParam_Request
{
} fescue_msgs__srv__GetPerceptionOcclusionDetectionAlgParam_Request;

typedef struct fescue_msgs__srv__GetPerceptionOcclusionDetectionAlgParam_Response
{
    bool success;
    iox::cxx::string<32> message;
    fescue_msgs__msg__PerceptionOcclusionDetectionAlgParam data;
} fescue_msgs__srv__GetPerceptionOcclusionDetectionAlgParam_Response;

#ifdef __cplusplus
}
#endif