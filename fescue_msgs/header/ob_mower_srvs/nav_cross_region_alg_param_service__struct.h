#pragma once

#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"

#include <cstdint>

#ifdef __cplusplus
extern "C" {
#endif

typedef struct fescue_msgs__msg__NavigationCrossRegionAlgParam
{
    float cross_region_linear;             /*param*/
    float cross_region_angular;            /*param*/
    float max_distance_threshold;          /*param*/
    float min_distance_threshold;          /*param*/
    float cross_region_special_linear;     // 特殊情况下的线速度（例如感知驱动）/*param*/
    float cross_region_special_angular;    // 特殊情况下的角速度（例如感知驱动）/*param*/
    float dis_tolerance;                   // 调整的距离阈值，防止出现冗余旋转 /*param*/
    float cross_region_angle_compensation; // 跨区域角度补偿 /*param*/

    float channel_stop_pose_x;               /*param*/
    int grass_count_threshold;               /*param*/
    int edge_mode_direction;                 // 默认逆时针 -1/*param*/
    float channel_width;                     // 通道宽度/*param*/
    float camera_2_center_dis;               // 小车摄像头到旋转中心的距离为切尔西(0.37) 格力博(0.45) /*param*/
    float adjust_mode_x_direction_threshold; // 过通道前，矫正模式下x方向阈值 默认-0.5/*param*/

    // 新增参数
    float mark_distance_threshold;                // 0.5 信标相对小车摄像头的距离阈值，判断是否在区域范围内 /*param*/
    int perception_drive_cooldown_time_threshold; // 3s 感知驱动冷却时间  /*param*/

    float cross_region_adjust_displace; // 0.7 跨区域后调整位移 /*param*/
    float channel_fixed_distance;
} fescue_msgs__msg__NavigationCrossRegionAlgParam;

typedef struct fescue_msgs__srv__SetNavigationCrossRegionAlgParam_Request
{
    fescue_msgs__msg__NavigationCrossRegionAlgParam data;
} fescue_msgs__srv__SetNavigationCrossRegionAlgParam_Request;

typedef struct fescue_msgs__srv__SetNavigationCrossRegionAlgParam_Response
{
    bool success;
    iox::cxx::string<32> message;
} fescue_msgs__srv__SetNavigationCrossRegionAlgParam_Response;

typedef struct fescue_msgs__srv__GetNavigationCrossRegionAlgParam_Request
{
} fescue_msgs__srv__GetNavigationCrossRegionAlgParam_Request;

typedef struct fescue_msgs__srv__GetNavigationCrossRegionAlgParam_Response
{
    bool success;
    iox::cxx::string<32> message;
    fescue_msgs__msg__NavigationCrossRegionAlgParam data;
} fescue_msgs__srv__GetNavigationCrossRegionAlgParam_Response;

#ifdef __cplusplus
}
#endif
