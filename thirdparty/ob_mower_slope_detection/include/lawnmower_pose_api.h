/*******************************************************************************
 * Copyright (c) 2025 Orbbec 3D Technology, Inc
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *******************************************************************************/


#ifndef AHRS_LAWNMOWER_POSE_API_H
#define AHRS_LAWNMOWER_POSE_API_H
#pragma once

#include <stdint.h>
#include <vector>

#define ORBBEC_EXPORTS 1

#if defined(_WIN32) || defined(WIN32)
#if defined(ORBBEC_EXPORTS) || defined(ORBBEC_BUILD_SHARED)
#define ORBBEC_API __declspec(dllexport)
#define ORBBEC_LOCAL
#else
#define ORBBEC_API __declspec(dllimport)
#define ORBBEC_LOCAL
#endif
#else
#if defined(ORBBEC_EXPORTS) || defined(ORBBEC_BUILD_SHARED)
#if __GNUC__ >= 4
#define ORBBEC_API __attribute__((visibility("default")))
#define ORBBEC_LOCAL __attribute__((visibility("hidden")))
#else
#define ORBBEC_API __attribute__((visibility("default")))
#define ORBBEC_LOCAL __attribute__((visibility("default")))
#endif
#endif
#endif

#ifdef __cplusplus
extern "C"
{
#endif

/** define the handle */
#define LAWNMOWER_POSE_HANDLE void *

enum SLOPE_STATUS {
  Abnormal = -1,
  FlatMoving = 0,
  Flat2Upslope,
  Flat2Downslope,
  Upslope2Flat,
  NormalUpslope,
  BumpRoad1,
  Downslope2Flat,
  BumpRoad2,
  NormalDownslop
};

typedef struct
{
  uint64_t timestamp; // ns
  float quaternion_w;
  float quaternion_x;
  float quaternion_y;
  float quaternion_z;
} ob_lawnmower_pose_result;

typedef struct
{
  uint64_t timestamp; // ns
  float roll;
  float pitch;
  float yaw;
  SLOPE_STATUS slope_status;
  float quaternion_w;
  float quaternion_x;
  float quaternion_y;
  float quaternion_z;
} ob_lawnmower_slope_status;

typedef struct
{
  int verbosity;               // 打印log等级，4
  bool saveResult;             // 是否保存结果，false, 下一次启动生效
  float ImuLostTime;           // imu 丢失多长时间重启，单位：s

}AHRS_detection_config;

#define SUCCESS 0                         // success for function callback
#define POSE_OK 0x9000                              // Pose estimate OK
#define POSE_IMU_INIT_ERROR 0x9001                  // parse config error
#define POSE_IMU_TIME_ERROR 0x9002                  // wrong imu timestamp

                 /****  VPE: Vehicle pose estimation  ****/
typedef void (*ob_lawnmower_pose_result_callback)(const ob_lawnmower_pose_result *result);

/**
 * @brief 回调函数
 * @param timestamp 时间戳 ms
 * @param width 图像宽度
 * @param height 图像高度
 * @param data 图像数据
 * @param size 图像通道数目：1或3
 */
typedef void (*ob_lawnmower_slope_status_callback)(const ob_lawnmower_slope_status *slopeStatus);

/**
 * @brief 配置回调函数：算法结果
 * @param lawnmower_pose_handle 对象句柄
 * @param proc 回调函数
 */
ORBBEC_API void VPE_register_result_callback(LAWNMOWER_POSE_HANDLE lawnmower_pose_handle, ob_lawnmower_pose_result_callback callback);

/**
 * @brief 配置回调函数：可视化图像
 * @param lawnmower_pose_handle 对象句柄
 * @param proc 回调函数
 */
ORBBEC_API void VPE_register_slope_status_callback(LAWNMOWER_POSE_HANDLE lawnmower_pose_handle, ob_lawnmower_slope_status_callback callback);

/**
 * @brief 创建系统
 * @param lawnmower_pose_handle 对象句柄
 * @param config_path 配置文件路径及文件名
 * @return 0 for create successfully; RECHARGE_INIT_FAIL for create failure
 */
ORBBEC_API int32_t VPE_create(LAWNMOWER_POSE_HANDLE *lawnmower_pose_handle, const char *config_path);

/**
 * @brief 启动系统
 * @param lawnmower_pose_handle 对象句柄
 * @return 0 for start successfully; 1 for recharge_location_handle nullptr;
 *         RECHARGE_SET_DICT_FAIL, RECHARGE_SET_CAM_FAIL
 */
ORBBEC_API int32_t VPE_start(LAWNMOWER_POSE_HANDLE lawnmower_pose_handle);

/**
 * @brief 停止系统
 * @param lawnmower_pose_handle 对象句柄
 * @return 0 for stop successfully; 1 for Stop failure
 */
ORBBEC_API int32_t VPE_stop(LAWNMOWER_POSE_HANDLE lawnmower_pose_handle);

/**
 * @brief 释放系统
 * @param lawnmower_pose_handle 对象句柄
 */
ORBBEC_API void VPE_release(LAWNMOWER_POSE_HANDLE lawnmower_pose_handle);

/**
 * @brief 获得版本号
 * @param lawnmower_pose_handle 对象句柄
 * @param **/
ORBBEC_API const char *VPE_get_version(LAWNMOWER_POSE_HANDLE lawnmower_pose_handle);


/**
 * @brief 输入 imu 信息
 * @param lawnmower_pose_handle 对象句柄
 * @param timestamp 时间戳 ns
 * @return SUCCESS for add imu successfully; POSE_IMU_TIME_ERROR for slam handle error
 */
ORBBEC_API int32_t VPE_add_imu(LAWNMOWER_POSE_HANDLE lawnmower_pose_handle, uint64_t timestamp,
                               double gry_x, double gry_y, double gry_z,
                               double acc_x, double acc_y, double acc_z);


/**
* @brief 获取设置信息
* @param lawnmower_pose_handle 对象句柄
* @return SUCCESS for set config successfully; POSE_IMU_INIT_ERROR for slam handle error
*/
ORBBEC_API int32_t VPE_get_config_parameter(LAWNMOWER_POSE_HANDLE lawnmower_pose_handle, AHRS_detection_config *config);
/**
* @brief 设置信息
* @param lawnmower_pose_handle 对象句柄
* @return SUCCESS for get config successfully; POSE_IMU_INIT_ERROR for slam handle error
*/
ORBBEC_API int32_t VPE_set_config_parameter(LAWNMOWER_POSE_HANDLE lawnmower_pose_handle, AHRS_detection_config *config);


#ifdef __cplusplus
}
#endif
#endif  // AHRS_LAWNMOWER_POSE_API_H
