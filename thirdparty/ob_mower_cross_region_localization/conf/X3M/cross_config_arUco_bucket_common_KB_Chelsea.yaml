%YAML:1.0 # need to specify the file type at the top!

#CameraModel: Brown = 0; KB = 1; MEI = 2; DS = 3
cameraModel: 1

# fx fy cx fy
intrinsics:  [521.75677490234375, 521.79620361328125, 644.79693603515625, 346.58099365234375]
# k1 k2 k3 k4 or k1 ~ k6 p1 p2
distortion_coeffs: [-0.039965365082025528, 0.00342573132365942, 0.00019144757243338972, -0.00067251670407131314]
resolution: [1280, 720]

#markModel: urPro plane all = 0; urPro plane roi = 1; urPro perception = 2; urPro cylinder = 3;
#           arUco grid = 4; arUco urPro = 5; arUco bucket = 6;
#           3 and 6 for cross region；other model for recharge
markModel: 6

# perception
percept_time_diff_thre: 0.2
only_use_perception: 1

# urPro cylinder
radius_cylinder: 0.05
# urPro id 0 point center arc distance from center line of arrow
offset_arc: 0.0055
# urPro id 0 point center distance from the upper surface
offset_upper: 0.033

# if urPro
# urProCfgPath: conf/perception_alg_node/ob_mower_mark_location/config_Ur_Pro_3_3_30.json
# urProCfgPath: conf/perception_alg_node/ob_mower_mark_location/config_Ur_Pro_5_9_30.json
# urProCfgPath: conf/perception_alg_node/ob_mower_mark_location/config_Ur_Pro_5_9_30_seed0.json
# urProCfgPath: ../config/config_Ur_Pro_3_3_30.json
# urProCfgPath: ../config/config_Ur_Pro_5_9_30.json
# urProCfgPath: ../config/config_Ur_Pro_5_9_30_seed0.json
# urProCfgPath: ../config/config_Ur_Pro_5_9_30_seed2.json
# urProCfgPath: ../config/cross_config_Ur_Pro_8_15_20_id8.json

# urProCfgPath: conf/location_alg_node/ob_mower_mark_location/cross_config_Ur_Pro_8_15_20_id8.json

urPro_rows: 8
urPro_cols: 15
# unit: m
urPro_step: 0.02

# if ArUco
arUcoID: [0,1,2,3]
# unit: m
arUcoScale: 0.08
# The origin is in the upper left corner
arUcoGrid_cols: 2
arUcoGrid_rows: 2
# unit: m
arUcoGrid_step: 0.065

# bucket detection
bucketID: 1
# follow the arrow: length\depth\height
bucketLength: 0.103
bucketDepth: 0.103
# arUco distance from the upper surface
arUcoHeight: 0.05

# DICT_APRILTAG_16h5\DICT_APRILTAG_25h9\DICT_APRILTAG_36h11
arUcoID_dictionary: DICT_APRILTAG_25h9

# if arUco + urPro
arUcoID_urPro: 20
# unit: m
arUcoScale_urPro: 0.07
useArUcoCalc: true

# global configuration
doubleDetect: false

# image processing
downSamplingScale: 1
roiUpSamplingScale: 3

# show image
showImg: false

# evaluation
evaluation: false
evaluation_path: /userdata

# output pose: rgb camera to robot frame or camera center frame
# unit: m
outputFrameOffset_x: 0.0
outputFrameOffset_y: 0.0
outputFrameOffset_z: 0.0
# camera tilt down angle
# unit: angle
outputFrame_theta: 0.0
# unit: m
outputDisThre: 10.0
# ROI: width\height < resolution
roiWidth: 800
roiHeight: 500
outputRollAng: 0.7  # rad, angle check threshold

# debug write image
writeImg: false
writeSrcImgPath: /userdata/Src
writeRoiImgPath: /userdata/Roi

# perception
# perceptionCfgPath: conf/perception_alg_node/ob_mower_mark_location/detect_config.yaml

# 0 -- ALL; 1 -- DEBUG; 2 -- INFO; 3 -- WARNING; 4 -- ERROR; 5 -- SILENT
verbosity: 4

# arUco parameter setting for recharge
# aprilTagMinClusterPixels: 100 #拒绝包含太少像素的四边形周长，默认值为5
# minMarkerPerimeterRate: 0.3   #确定标记轮廓被检测的最小周长，相对于输入图像最大维度的比率，默认值为0.03
# maxMarkerPerimeterRate: 1.5   #确定标记轮廓被检测的最大周长，相对于输入图像最大维度的比率，默认值为4.0
# minCornerDistanceRate: 0.1    #检测到的角点之间的最小角点距离，相对于其周长的比率，默认值为0.05
# minDistanceToBorder: 3        #检测到的标记的任何角点到图像边缘的最小距离，以像素为单位，默认值为3
# aprilTagQuadSigma: 0.0        #应用于分割图像的高斯模糊的标准差，默认值为0.0
# aprilTagMaxLineFitMse: 10     #控制 AprilTag 检测中线段拟合的误差阈值,默认值：通常为 10.0

Perceptual_window_ratio: 1.2  # percetion window zoom rate
# nearby distance QR detection setting, Not used for cross qr detection
use_nearby_speed_up: 0
edgeThresholdRate: 0.1
detection_area_size: 40000
nearbyMinMarkerPerimeterRate: 0.3


# ***conservative parameter***
aprilTagMinClusterPixels: 5  #拒绝包含太少像素的四边形边长，默认值为5
minMarkerPerimeterRate: 0.03 #确定标记轮廓被检测的最小周长，相对于输入图像最大维度的比率，默认值为0.03
maxMarkerPerimeterRate: 4.0  #确定标记轮廓被检测的最大周长，相对于输入图像最大维度的比率，默认值为4.0
minCornerDistanceRate: 0.05  #检测到的角点之间的最小角点距离，相对于其周长的比率，默认值为0.05
minDistanceToBorder: 3       #检测到的标记的任何角点到图像边缘的最小距离，以像素为单位，默认值为3
aprilTagQuadSigma: 0.0       #应用于分割图像的高斯模糊的标准差，默认值为0.0
aprilTagMaxLineFitMse: 10    #控制 AprilTag 检测中线段拟合的误差阈值,默认值：通常为 10.0
minMarkerDistanceRate: 0.01  #多个二维码，通过距离比率去除较小的
detectInvertedMarker: 0      #1：探测黑色背景二维码
#Perceptual_window_ratio: 0.4 # percetion window zoom rate

# *** pnp solve methods ***
# SOLVEPNP_ITERATIVE:0
# SOLVEPNP_EPNP:1
# SOLVEPNP_P3P:2
# SOLVEPNP_DLS:3
# SOLVEPNP_UPNP:4
# SOLVEPNP_AP3P:5
# SOLVEPNP_IPPE:6, plane method
pnp_method: 0
# CORNER_REFINE_NONE, 0     ///< Tag and corners detection based on the ArUco approach
# CORNER_REFINE_SUBPIX, 1   ///< ArUco approach and refine the corners locations using corner subpixel accuracy
# CORNER_REFINE_CONTOUR, 2  ///< ArUco approach and refine the corners locations using the contour-points line fitting
# CORNER_REFINE_APRILTAG, 3 ///< Tag and corners detection based on the AprilTag 2 approach @cite wang2016iros
cornerRefinementMethod: 0
use_bilateral_filter: 0
use_block_binary: 0

use_gauss_blur: 0
win_size: 7
sigma: 3.0