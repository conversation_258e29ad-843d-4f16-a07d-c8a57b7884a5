/*******************************************************************************
 * Copyright (c) 2023 Orbbec 3D Technology, Inc
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *******************************************************************************/

#ifndef MARKLOCATIONAPI_H
#define MARKLOCATIONAPI_H

#pragma once

#include <stdint.h>
#include <vector>

#define ORBBEC_EXPORTS 1

#if defined(_WIN32) || defined(WIN32)
#if defined(ORBBEC_EXPORTS) || defined(ORBBEC_BUILD_SHARED)
#define ORBBEC_API __declspec(dllexport)
#define ORBBEC_LOCAL
#else
#define ORBBEC_API __declspec(dllimport)
#define ORBBEC_LOCAL
#endif
#else
#if defined(ORBBEC_EXPORTS) || defined(ORBBEC_BUILD_SHARED)
#if __GNUC__ >= 4
#define ORBBEC_API __attribute__((visibility("default")))
#define ORBBEC_LOCAL __attribute__((visibility("hidden")))
#else
#define ORBBEC_API __attribute__((visibility("default")))
#define ORBBEC_LOCAL __attribute__((visibility("default")))
#endif
#endif
#endif

#ifdef __cplusplus
extern "C"
{
#endif

/** define the handle */
#define MARK_LOCATION_HANDLE void *

  // enum ob_mark_detect_status
  // {
  //   detect_ok = 0,
  //   detect_no_match = 1, // 未检测到所需markID，但检测到其他mark
  //   detect_pnp_fail = 2, // 检测到所需markID，但pnp解算失败
  //   detect_over_dis = 3, // 检测到所需markID，但超出检测距离
  //   detect_error = 4,    // 检测到所需markID，但计算状态错误
  //   detect_ng = 100      // 未检测到mark
  // };

  enum ob_mark_detect_status_output
  {
    detect_output_ng = 0,             // 未检测到二维码
    detect_output_calc_pose_fail = 1, // 检测到二维码，不能算出位姿
    detect_output_ok = 2              // 可以计算出位姿
  };

  /**
   * @brief 位姿结果
   * @param timestamp 时间戳 ns
   * @param mark_perception_status 0 表示未感知（检测）到信标；1 表示感知（检测）到信标
   * @param mark_perception_direction  -1 偏左，跨区信标在相机画面中偏左; 0 居中；1 偏右
   * @param detect_status mark位姿检测状态
   * @param v_markID_dis 检测到的所有 markID 和 Mark 2 Camera 的距离 m（米）
   * @param markID 待（正在）检测的锥桶ID
   * @param roi_confidence 检测到信标时的置信度：0~100，数值越到表示置信度越高，mark点越接近图像中心
   * @param target_direction -1 偏左，需左转向; 0 居中；1 偏右，需右转向
   * @param pos_x Robot 2 Target 平移 x m（米）
   * @param pos_y Robot 2 Target 平移 y m（米）
   * @param pos_z Robot 2 Target 平移 z m（米）
   * @param quaternion_w Robot 2 Target 旋转四元数表示
   * @param quaternion_x
   * @param quaternion_y
   * @param quaternion_z
   * @param roll 弧度，Robot 2 Target 旋转四元数表示
   * @param pitch 弧度
   * @param yaw 弧度
   */
  typedef struct
  {
    uint64_t timestamp; // ns
    int mark_perception_status;
    int mark_perception_direction;
    ob_mark_detect_status_output detect_status;
    std::vector<std::pair<int, float>> v_markID_dis;
    int markID;
    int roi_confidence;
    int target_direction; // unused
    float pos_x;
    float pos_y;
    float pos_z;
    float quaternion_w;
    float quaternion_x;
    float quaternion_y;
    float quaternion_z;
    float roll;
    float pitch;
    float yaw;
    int ERROR_CODE;
  } ob_mark_location_result;

  /**
   * @brief 感知检测结果
   * @param timestamp 时间戳 ns
   * @param is_cross_mark 是否检测到跨区信标
   * @param cross_mark_direction -1 偏左，跨区信标在相机画面中偏左; 0 居中；1 偏右
   * @param confidence 感知结果置信度
   * @param x1y1_x2y2 感知结果左上角图像坐标、右下角图像坐标
   */
  typedef struct
  {
    uint64_t timestamp; // ns
    bool is_cross_mark;
    int cross_mark_direction;
    int width;
    int height;
    float confidence;
    float x1y1_x2y2[4];
  } PerceptionInfo;

  typedef struct
  {
    // need to adjust dynamically
    int verbosity;                 // log level, 4: just error
    bool showImg;                  // false
    int only_use_perception;       // 1, use the perception result
    float percept_time_diff_thre;  // 0.2
    bool writeImg;                 // false, if debug image, revise to true;
    float Perceptual_window_ratio; // default: 1.2
    // remote distance QR detection setting
    int pnp_method;               // default 0, values: 0,1,2,3,4,5,6
    int aprilTagMinClusterPixels; // default: 5
    float minMarkerPerimeterRate; // default: 0.03
    float maxMarkerPerimeterRate; // default: 4.0
    float outputRollAng;          // default: 0.7
    int cornerRefinementMethod;   // default 0, range: 0~3
    bool use_bilateral_filter;    // default: false
    // nearby QR parameter
    bool use_nearby_speed_up;           // default: 0
    float minCornerDistanceRate;        // default: 0.05
    float edgeThresholdRate;            // default 0.1
    int detection_area_size;            // default 40000
    float nearbyMinMarkerPerimeterRate; // default 0.3

    /* now, don't need to adjust dynamically */
    int markModel;             // Swansea arUco grid: 4；Chelsea arUco bucket: 6
    int bucketID;              // default: 1
    bool evaluation;           // default: false
    float outputFrameOffset_x; // default: 0
    float outputFrameOffset_y; // default: 0
    float outputFrameOffset_z; // default: 0
    float outputFrame_theta;   // default: 0
    float outputDisThre;       // default: 10.0
  } Cross_region_config;

  typedef struct
  {
    uint8_t model_;       // 畸变模型：0->K6, 1->KB
    uint32_t img_width_;  // 像素宽1280
    uint32_t img_height_; // 像素高720
    float focal_x_;       // 焦距fx
    float focal_y_;       // 焦距fy
    float cx_;            // 主点cx
    float cy_;            // 主点cy
    float k1_;            // 畸变参数
    float k2_;
    float k3_;
    float k4_;
    float k5_; // 使用KB模型时，k5_=0.0
    float k6_; // 使用KB模型时，k6_=0.0
    float p1_; // 使用KB模型时，p1_=0.0
    float p2_; // 使用KB模型时，p2_=0.0
  } Cross_region_camera_intrinsic;

#define CROSS_OK 0x8000                     // QR code detection OK
#define CROSS_INIT_FAIL 0x8001              // Parse config fail and init failure
#define CROSS_SET_DICT_FAIL 0x8002          // Setting QR code dictionary failure
#define CROSS_SET_CAM_FAIL 0x8003           // Setting camera model failure
#define CROSS_NOT_PERCEPTION 0x8004         // No perception result
#define CROSS_PERCEPTION_DELAY_LARGE 0x8005 // Perception result delay too large
#define CROSS_PREPROCESS_FAIL 0x8006        // Image preprocess failure
#define CROSS_MARK_FAIL 0x8007              // No mark detection
#define CROSS_PNP_FAIL 0x8008               // PNP solve pose failure
#define CROSS_DISTANCE_LARGE 0x8009         // Detection distance too large
#define CROSS_ROLL_LARGE 0x800A             // Angle check not success
#define CROSS_OTHER_ID 0x800B               // Detect other ID code

  typedef void (*ob_mark_location_result_callback)(const ob_mark_location_result *result);

  /**
   * @brief 回调函数
   * @param timestamp 时间戳 ms
   * @param width 图像宽度
   * @param height 图像高度
   * @param data 图像数据
   * @param size 图像通道数目：1或3
   */
  typedef void (*ob_mark_location_image_show_callback)(const uint64_t timestamp, const uint32_t width, const uint32_t height, uint8_t *data, const uint64_t size);

  /**
   * @brief 配置回调函数：算法结果
   * @param mark_location_handle 对象句柄
   * @param proc 回调函数
   */
  ORBBEC_API void OML_register_result_callback(MARK_LOCATION_HANDLE mark_location_handle, ob_mark_location_result_callback proc);

  /**
   * @brief 配置回调函数：可视化图像
   * @param mark_location_handle 对象句柄
   * @param proc 回调函数
   */
  ORBBEC_API void OML_register_image_show_callback(MARK_LOCATION_HANDLE mark_location_handle, ob_mark_location_image_show_callback proc);

  /**
   * @brief 创建系统
   * @param mark_location_handle 对象句柄
   * @param config_path 配置文件路径及文件名
   * @return 0 for create successfully; CROSS_INIT_FAIL for create failure
   */
  ORBBEC_API int32_t OML_create(MARK_LOCATION_HANDLE *mark_location_handle, const char *config_path);

  /**
   * @brief 启动系统
   * @param mark_location_handle 对象句柄
   * @return 0 for start successfully; 1 for recharge_location_handle nullptr;
   *         CROSS_SET_DICT_FAIL, CROSS_SET_CAM_FAIL
   */
  ORBBEC_API int32_t OML_start(MARK_LOCATION_HANDLE mark_location_handle);

  /**
   * @brief 停止系统
   * @param mark_location_handle 对象句柄
   * @return 0 for stop successfully; 1 for stop failure
   */
  ORBBEC_API int32_t OML_stop(MARK_LOCATION_HANDLE mark_location_handle);

  /**
   * @brief 释放系统
   * @param mark_location_handle 对象句柄
   */
  ORBBEC_API void OML_release(MARK_LOCATION_HANDLE mark_location_handle);

  /**
   * @brief 获得版本号
   * @param mark_location_handle 对象句柄
   * @param **/
  ORBBEC_API const char *OML_get_version(MARK_LOCATION_HANDLE mark_location_handle);

  /**
   * @brief 设置锥桶id
   * @param mark_location_handle 对象句柄
   * @param id 预检测锥桶id
   */
  ORBBEC_API int32_t OML_set_bucket_id(MARK_LOCATION_HANDLE mark_location_handle, uint32_t id);

  /**
   * @brief 输入单目图像信息
   * @param mark_location_handle 对象句柄
   * @param timestamp 时间戳 ms
   * @param width 图像宽度
   * @param height 图像高度
   * @param data 图像数据
   * @param size 图像通道数目：1或3
   * @return 1 for success, 0 for failure
   */
  ORBBEC_API int32_t OML_add_image(MARK_LOCATION_HANDLE mark_location_handle, uint64_t timestamp, uint32_t width, uint32_t height, uint8_t *data, uint64_t size = 1);

  /**
   * @brief 输入感知算法结果信息
   * @param mark_location_handle 对象句柄
   * @param timestamp 时间戳 ms
   * @param percept_info 感知检测结果：对应1280*720分辨率图像检测结果
   * @return 1
   */
  ORBBEC_API int32_t OML_add_perception(MARK_LOCATION_HANDLE mark_location_handle, uint64_t timestamp, PerceptionInfo *percept_info);

  /**
   * @brief 获取信标设置信息
   * @param mark_location_handle 对象句柄
   * @param Cross_region_config 要获取的信标配置
   * @return 1
   */
  ORBBEC_API int32_t OML_get_config_parameter(MARK_LOCATION_HANDLE mark_location_handle, Cross_region_config *cross_region_config);

  /**
   * @brief 设置信标信息
   * @param mark_location_handle 对象句柄
   * @param Cross_region_config 要设置的信标配置
   * @return 1
   * */
  ORBBEC_API int32_t OML_set_config_parameter(MARK_LOCATION_HANDLE mark_location_handle, Cross_region_config *cross_region_config);

  /**
   * @brief 设置相机标定内参
   * @param mark_location_handle 对象句柄
   * @param Cross_region_camera_intrinsic 标定内参结构体
   * @return 1
   * */
  ORBBEC_API int32_t OML_set_camera_intrinsic_parameter(MARK_LOCATION_HANDLE mark_location_handle, Cross_region_camera_intrinsic *cross_region_camera_intrinsic);

#ifdef __cplusplus
}
#endif

#endif // MARKLOCATIONAPI_H