/*****************************************************************************
 *  Orbbec Vision Core Library
 *  Copyright (C) 2017 by ORBBEC Technology., Inc.
 *
 *  This file is part of Orbbec Vision Core Library 2.0
 *
 *  This file belongs to ORBBEC Technology., Inc.
 *  It is considered a trade secret, and is not to be divulged or used by
 *  parties who have NOT received written authorization from the owner.
 ****************************************************************************/

#ifndef __OB_OCCLUSION_CLASSIFY_HPP__
#define __OB_OCCLUSION_CLASSIFY_HPP__

#include <iostream>
#include <vector>
#include "common_defs.h"
#include "ob_mower_occlusion_eorrcodes.h"

// #define CHECK 0
#define SHOW 0
// #define WRITE 0
// #define TIME 0

#ifdef __cplusplus
extern "C"
{

#endif

#ifdef _WIN32
#ifdef VCAL_EXPORTS
#define VCAL_API __declspec(dllexport)
#else
#define VCAL_API __declspec(dllimport)
#endif
#else
#define VCAL_API
#endif

/** define the object detector handle */
#define OCCLUSION_CLASSIFIER_HANDLE void *

#define INPUT_IMAGE_WIDTH 640  // 算法输入的图像宽高，注意，不是算法的原始尺寸
#define INPUT_IMAGE_HEIGHT 360 // 算法输入的图像宽高

  typedef struct
  {
    int ignore_num = 15;              // Number of images ignored at the beginning.
    float line1 = 0.30f;              // 中区比例
    float line2 = 0.60f;              // 下区比例
    int num_frames_mid = 30;          // 中区最多连续检测的次数
    int min_detected_frames_mid = 15; // 中区判断为遮挡所需累计的次数
    int num_frames_bot = 10;          // 下区最多连续检测的次数
    int min_detected_frames_bot = 1;  // 下区判断为遮挡所需累计的次数
    int block_size = 64;              // 块大小
    double laplacian_thresh = 12.0;   // 拉普拉斯阈值
    double sobel_thresh = 8.0;        // sobel阈值
    int min_area = 200;               // debug显示时绘制的最小面积，仅对显示结果有影响
    int min_blocks_mid = 11;          // 中区判断为遮挡的最少块数
    int min_blocks_bot = 6;           // 下区判断为遮挡的最少块数
    int skip_frames = 15;             // 跳帧数
    bool test_mode = false;           // 测试模式开关，设置为true时，会可视化每一帧的检测结果。
    int log_level = 2;                // 0：打印所有调试信息，1：打印INFO以上调试信息， 2：打印WARN以上调试信息，3：打印ERROR调试信息。
    bool save_log = false;
    std::string save_log_path = "/userdata/log";
    int debug_mode = 0;                                                            // 0: 关闭debug模式, 1: 绘制检测结果，2: 绘制检测结果，并保存结果图，
                                                                                   // 3: 绘制检测结果，并保存原始图，但不保存结果图， 4：绘制检测结果，并保存原始图和结果图。
    std::string save_debug_img_path = "/userdata/muxulong/occlusion/data/result/"; // debug模式下保存图片的路径, 不存在会自动创建

    //************************如下坐标仅仅是为了大致确定一个ROI区域***************************************** //
    // 如下坐标仅仅为了大致确定一个ROI区域，不需要从外部获取，不对外开放修改
    ObPoint lLine_ptStart = {260, 149}; // 左侧车道线起点(此处"起点"是指车道线上远离图片底边的一个点)
    ObPoint lLine_ptEnd = {108, 327};   // 左侧车道线终点(此处"终点"是指车道线上靠近图片底边的一个点)
    ObPoint rLine_ptStart = {377, 149}; // 右侧车道线起点.
    ObPoint rLine_ptEnd = {528, 327};   // 右侧车道线终点.

  } OcclusionClsInputParams;

  // // 目标物体信息.
  // // classID:        目标物体的类别id.
  // // score:          目标物体置信度.
  // typedef struct
  // {
  //   int classID;
  //   float score;
  // } ClassifyResult;

  // 遮挡检测结果
  //  timestamp:        该帧输入时的时间戳.
  //  occlusion_result  遮挡检测结果  0：未遮挡, 1：遮挡但不影响算法, 2：遮挡且影响算法，3：不确定
  //  debug_img         debug图像
  typedef struct
  {
    uint64_t timestamp;
    int occlusion_result;
    // ClassifyResult classify_result;
    ImageBuffer debug_img;
  } OcclusionResultBuffer;

  // 目标类别ID映射表
  // {0,"not occlusion"}
  // {1,"occlusion"}

  typedef void (*CallbackFunction)(OcclusionResultBuffer *occlusionResult);
  int OcclusionClassifierRegisterCallback(CallbackFunction func);

  VCAL_API int OcclusionClassifierCreate(OCCLUSION_CLASSIFIER_HANDLE *classifierHandle, const std::string &classify_config_path);
  //==================================================================
  // Name: OcclusionClassifierCreate
  // Description: Algorithm initialization. Load the model file and parse the parameters in the yaml file.
  // Input parameters:
  //        config_path: path to the yaml file.
  //        model_path: path to the model_file , selectable,  if not passed, use the one in the yaml config file
  // Return: bool type, whether successful.
  //==================================================================

  // init by params struct
  VCAL_API int OcclusionClassifierCreateFromStruct(OCCLUSION_CLASSIFIER_HANDLE *classifierHandle, OcclusionClsInputParams &inputParams);

  VCAL_API int OcclusionClassifierExecute(OCCLUSION_CLASSIFIER_HANDLE *classifierHandle, ImageBuffer *inputImage);
  // VCAL_API int OcclusionClassifierExecute(OCCLUSION_CLASSIFIER_HANDLE *classifierHandle, ImageBuffer* imageData, OcclusionResultBuffer &occlusionResult);

  //==================================================================
  // Name: OcclusionClassifierExecute
  // Description: Perform model inference on the input image data. Each time a frame of image is input, the corresponding result will be returned.
  // Input parameters:
  //       *imageData: image address,
  //       occlusionResult: detection result.
  //       cls_num: model class num, selectable,  if not passed, use the one in the yaml config file
  // Return: bool type, whether successful.
  //==================================================================

  VCAL_API OcclusionClsInputParams OcclusionClassifierGetParams(OCCLUSION_CLASSIFIER_HANDLE *classifierHandle);
  VCAL_API int OcclusionClassifierSetParams(OCCLUSION_CLASSIFIER_HANDLE *classifierHandle, OcclusionClsInputParams &inputParams);
  // VCAL_API int OcclusionClassifierSetSafetyContour(OCCLUSION_CLASSIFIER_HANDLE *classifierHandle, std::vector<ObPoint> &safetyContour);
  VCAL_API int OcclusionClassifierRelease(OCCLUSION_CLASSIFIER_HANDLE *classifierHandle);
  VCAL_API const char *GetOcclusionClassifierVersion();

#ifdef __cplusplus
}
#endif

#endif