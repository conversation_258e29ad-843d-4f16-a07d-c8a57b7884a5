#ifndef PERCEPTION_QRCODE_LOCATION_HPP
#define PERCEPTION_QRCODE_LOCATION_HPP

#include "charge_station_localization_config.hpp"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "mower_msgs/msg/soc_exception.hpp"
#include "ob_mower_msgs/qrcode_result__struct.h"
#include "opencv2/opencv.hpp"
#include "rechargeLocationAPI.h"
#include "sensor_msgs/image__struct.h"
#include "utils/iceoryx_publisher_mower.hpp"

#include <string>

namespace fescue_iox
{

struct QrCodeImage
{
    uint64_t sec;
    uint64_t nanosec;
    uint32_t width;
    uint32_t height;
    std::string frame_id;
    std::string encoding;
    cv::Mat img;
    QrCodeImage() = default;
    ~QrCodeImage() = default;
    QrCodeImage(const QrCodeImage &other)
    {
        this->sec = other.sec;
        this->nanosec = other.nanosec;
        this->width = other.width;
        this->height = other.height;
        this->frame_id = other.frame_id;
        this->encoding = other.encoding;
        this->img = other.img.clone();
    }
    QrCodeImage &operator=(const QrCodeImage &other)
    {
        if (this != &other)
        {
            this->sec = other.sec;
            this->nanosec = other.nanosec;
            this->width = other.width;
            this->height = other.height;
            this->frame_id = other.frame_id;
            this->encoding = other.encoding;
            this->img = other.img.clone();
        }
        return *this;
    }
};

class ChargeStationLocalizationAlg
{
    using iox_image_publisher = iox::popo::Publisher<sensor_msgs__msg__Image_iox>;
    using iox_qrocde_result_publisher = iox::popo::Publisher<fescue_msgs__msg__QrCodeResult>;

public:
    ChargeStationLocalizationAlg(const std::string &conf_file, Recharge_camera_intrinsic &camera_intrinsic, bool result);
    ~ChargeStationLocalizationAlg();
    bool DoQRCodeLocation(const sensor_msgs__msg__Image_iox &image);
    void AddChargeDetectStationInfo(const PerceptionInfo_recharge &info);
    bool SetQRCodeLocationParam(Recharge_config &param);
    bool GetQRCodeLocationParam(Recharge_config &param);
    const char *GetQRCodeLocationAlgoVersion();

private:
    void InitAlgParam();
    bool InitAlg(Recharge_camera_intrinsic &camera_intrinsic, bool result);
    void InitPublisher();
    void PreQRCodeLocation(const sensor_msgs__msg__Image_iox &image);
    void PublishResult();
    void PulishImage();
    void PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value);
    void SaveImage(const sensor_msgs__msg__Image_iox &image);
    void PublishResultThread();
    void ConfigParam2AlgParam(const QRCodeLocationAlgConfig &config, Recharge_config &param);
    void AlgParam2ConfigParam(const Recharge_config &param, QRCodeLocationAlgConfig &config);

private:
    static std::mutex qrcode_result_mtx_;
    static ob_recharge_location_result qrcode_result_;
    static std::mutex qrcode_img_mtx_;
    static QrCodeImage qrcode_img_;
    static void QRCodeLocationResultCallback(const ob_recharge_location_result *result);
    static void QRCodeLocationImageCallback(const uint64_t timestamp, const uint32_t width, const uint32_t height,
                                            uint8_t *data, const uint64_t size);

private:
    std::unique_ptr<iox_image_publisher> pub_qrcode_img_{nullptr};            // 只做debug
    std::unique_ptr<iox_qrocde_result_publisher> pub_qrcode_result_{nullptr}; // 目标检测结果发布
    std::unique_ptr<IceoryxPublisherMower<mower_msgs::msg::SocException>> pub_exception_{nullptr};

    std::atomic_bool thread_running_{true};
    std::thread pub_qrcode_result_thread_;
    uint64_t last_qrcode_result_timestamp_{0};

    // parameters
    bool save_img_enable_{false};
    std::string save_img_dir_{""};
    std::string conf_file_{""};

    RECHARGE_LOCATION_HANDLE aruco_handle_{nullptr}; // 二维码检测句柄
    std::string encoding_{""};
    std::string frame_id_{""};
    uint64_t sec_;
    uint64_t nanosec_;
    uint64_t timestamp_ms_{0};

    cv::Mat gray_image_;
    cv::Mat bgr_img_;

    uint32_t img_width_;
    uint32_t img_height_;
    uint8_t *img_data_;
    uint64_t img_size_;
    static constexpr int32_t MAX_IMG_BUFF_SIZE = 1280 * 800 * 3;
};

} // namespace fescue_iox

#endif
