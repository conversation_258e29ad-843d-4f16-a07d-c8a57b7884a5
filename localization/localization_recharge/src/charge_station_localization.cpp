#include "charge_station_localization.hpp"

#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"

using namespace mower_msgs::msg;

namespace fescue_iox
{

std::mutex ChargeStationLocalizationAlg::qrcode_result_mtx_;
ob_recharge_location_result ChargeStationLocalizationAlg::qrcode_result_;
std::mutex ChargeStationLocalizationAlg::qrcode_img_mtx_;
QrCodeImage ChargeStationLocalizationAlg::qrcode_img_;

void ChargeStationLocalizationAlg::QRCodeLocationResultCallback(const ob_recharge_location_result *result)
{
    LOG_DEBUG("QrCodeResult: {} pose({:.3f} {:.3f} {:.3f}) rpy({:.3f} {:.3f} {:.3f})",
              result->timestamp,
              result->pos_x, result->pos_y, result->pos_z,
              result->roll, result->pitch, result->yaw);
    qrcode_result_mtx_.lock();
    qrcode_result_ = *result;
    qrcode_result_mtx_.unlock();
}

void ChargeStationLocalizationAlg::QRCodeLocationImageCallback(const uint64_t timestamp, const uint32_t width, const uint32_t height,
                                                               uint8_t *data, const uint64_t size)
{
    qrcode_img_mtx_.lock();
    qrcode_img_.sec = timestamp / 1000;
    qrcode_img_.nanosec = (timestamp % 1000) * 1000 * 1000;
    qrcode_img_.frame_id = "qr_code";
    qrcode_img_.width = width / 2;
    qrcode_img_.height = height / 2;
    if (size == 1)
    {
        qrcode_img_.encoding = "mono8";
        cv::Mat img(height, width, CV_8UC1, data);
        cv::resize(img, qrcode_img_.img, cv::Size(qrcode_img_.width, qrcode_img_.height));
    }
    else if (size == 3)
    {
        qrcode_img_.encoding = "bgr8";
        cv::Mat img(height, width, CV_8UC3, data);
        cv::resize(img, qrcode_img_.img, cv::Size(qrcode_img_.width, qrcode_img_.height));
    }
    qrcode_img_mtx_.unlock();
}

ChargeStationLocalizationAlg::ChargeStationLocalizationAlg(const std::string &conf_file,
                                                           Recharge_camera_intrinsic &camera_intrinsic,
                                                           bool result)
    : conf_file_(conf_file)
{
    InitPublisher();
    InitAlg(camera_intrinsic, result);
}

ChargeStationLocalizationAlg::~ChargeStationLocalizationAlg()
{
    LOG_WARN("ChargeStationLocalizationAlg start stop!");
    thread_running_.store(false);
    if (pub_qrcode_result_thread_.joinable())
    {
        pub_qrcode_result_thread_.join();
    }
    if (ORL_stop(aruco_handle_) != 0)
    {
        LOG_WARN("ChargeStationLocalizationAlg ORL_stop execute failed!");
    }
    ORL_release(aruco_handle_);
    LOG_WARN("ChargeStationLocalizationAlg stop success!");
}

bool ChargeStationLocalizationAlg::InitAlg(Recharge_camera_intrinsic &camera_intrinsic, bool intrinsic_param_valid)
{
    if (!intrinsic_param_valid)
    {
        SocExceptionValue err_code = SocExceptionValue::ALG_CHARGE_STATION_LOCALIZATION_GET_INTRINSIC_PARAM_ERROR_EXCEPTION;
        LOG_ERROR("Charge station localization algorithm get intrinsic param fail, error code: {:X}", static_cast<uint16_t>(err_code));
        PublishException(SocExceptionLevel::ERROR, err_code);
        aruco_handle_ = nullptr;
        return false;
    }

    InitAlgParam();
    QRCodeLocationAlgConfig config = Config<QRCodeLocationAlgConfig>::GetConfig();

    int32_t result = ORL_create(&aruco_handle_, config.inner_config_path.c_str());
    if (result != 0)
    {
        LOG_ERROR("Charge station localization algorithm initialization fail, error code: {:X}", result);
        PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                         mower_msgs::msg::SocExceptionValue::ALG_CHARGE_STATION_LOCALIZATION_INIT_EXCEPTION);
        return false;
    }

    Recharge_config param;
    ConfigParam2AlgParam(config, param);
    ORL_start(aruco_handle_);
    ORL_set_config_parameter(aruco_handle_, &param);
    if (intrinsic_param_valid)
    {
        LOG_WARN("************ Perception charge station localization using camera intrinsic parameter **********************");
        ORL_set_camera_intrinsic_parameter(aruco_handle_, &camera_intrinsic);
    }
    else
    {
        LOG_WARN("************ Perception charge station localization using config file intrinsic parameter **********************");
    }
    ORL_register_result_callback(aruco_handle_, &ChargeStationLocalizationAlg::QRCodeLocationResultCallback);
    ORL_register_image_show_callback(aruco_handle_, &ChargeStationLocalizationAlg::QRCodeLocationImageCallback);
    LOG_INFO("ob_mower_qrcode_version:{}", ORL_get_version(aruco_handle_));
    pub_qrcode_result_thread_ = std::thread(&ChargeStationLocalizationAlg::PublishResultThread, this);

    return true;
}

void ChargeStationLocalizationAlg::InitAlgParam()
{
    std::string conf_path = GetDirectoryPath(conf_file_);
    if (!conf_path.empty())
    {
        LOG_INFO("ChargeStationLocalizationAlg create alg config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("ChargeStationLocalizationAlg create alg config path failed!!!");
        }
    }
    if (!Config<QRCodeLocationAlgConfig>::Init(conf_file_))
    {
        LOG_WARN("Init ChargeStationLocalizationAlg config parameters failed!");
    }
    QRCodeLocationAlgConfig config = Config<QRCodeLocationAlgConfig>::GetConfig();
    LOG_INFO("{}", config.toString().c_str());
    if (!Config<QRCodeLocationAlgConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set ChargeStationLocalizationAlg config parameters failed!");
    }
}

bool ChargeStationLocalizationAlg::DoQRCodeLocation(const sensor_msgs__msg__Image_iox &image)
{
    PreQRCodeLocation(image);
    ORL_add_image(aruco_handle_, timestamp_ms_, img_width_, img_height_, img_data_, img_size_);
    PulishImage();
    return true;
}

void ChargeStationLocalizationAlg::AddChargeDetectStationInfo(const PerceptionInfo_recharge &station_info)
{
    uint64_t timestamp_ms = station_info.timestamp / 1e6;
    ORL_add_perception(aruco_handle_, timestamp_ms, (PerceptionInfo_recharge *)&station_info);
}

bool ChargeStationLocalizationAlg::SetQRCodeLocationParam(Recharge_config &param)
{
    ORL_set_config_parameter(aruco_handle_, &param);
    LOG_INFO("Set ChargeStationLocalizationAlg alg params success!");
    QRCodeLocationAlgConfig config = Config<QRCodeLocationAlgConfig>::GetConfig();
    AlgParam2ConfigParam(param, config);
    Config<QRCodeLocationAlgConfig>::SetConfig(config);
    LOG_INFO("New ChargeStationLocalizationAlg alg params: {}", config.toString().c_str());
    return true;
}

bool ChargeStationLocalizationAlg::GetQRCodeLocationParam(Recharge_config &param)
{
    ORL_get_config_parameter(aruco_handle_, &param);
    return true;
}

const char *ChargeStationLocalizationAlg::GetQRCodeLocationAlgoVersion()
{
    return ORL_get_version(aruco_handle_);
}

void ChargeStationLocalizationAlg::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_qrcode_img_ = std::make_unique<iox_image_publisher>(
        iox::capro::ServiceDescription{kPerceptionDetectQrCodeColorImageIox[0],
                                       kPerceptionDetectQrCodeColorImageIox[1],
                                       kPerceptionDetectQrCodeColorImageIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
    pub_qrcode_result_ = std::make_unique<iox_qrocde_result_publisher>(
        iox::capro::ServiceDescription{kPerceptionDetectQrCodeResultIox[0],
                                       kPerceptionDetectQrCodeResultIox[1],
                                       kPerceptionDetectQrCodeResultIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);

    pub_exception_ = std::make_unique<IceoryxPublisherMower<mower_msgs::msg::SocException>>("soc_exception");
}

void ChargeStationLocalizationAlg::PreQRCodeLocation(const sensor_msgs__msg__Image_iox &image)
{
    sec_ = image.header.stamp.sec;
    nanosec_ = image.header.stamp.nanosec;
    timestamp_ms_ = sec_ * 1000 + nanosec_ / 1000000;
    frame_id_ = std::string(image.header.frame_id.c_str());
    encoding_ = std::string(image.encoding.c_str());

    NV12ToGrayMat(gray_image_, image.data.data(), image.width, image.height);

    img_width_ = gray_image_.cols;
    img_height_ = gray_image_.rows;
    img_data_ = gray_image_.data;
    img_size_ = gray_image_.channels();
}

void ChargeStationLocalizationAlg::PublishResult()
{
    ob_recharge_location_result result;
    qrcode_result_mtx_.lock();
    result = qrcode_result_;
    qrcode_result_mtx_.unlock();

    if (result.ERROR_CODE == RECHARGE_INIT_FAIL ||
        result.ERROR_CODE == RECHARGE_SET_DICT_FAIL ||
        result.ERROR_CODE == RECHARGE_SET_CAM_FAIL)
    {
        LOG_ERROR("Charge station localization algorithm execute failed, error code: {:X}", result.ERROR_CODE);
        PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                         mower_msgs::msg::SocExceptionValue::ALG_CHARGE_STATION_LOCALIZATION_EXECUTE_ERROR_EXCEPTION);
    }

    if (last_qrcode_result_timestamp_ != result.timestamp)
    {
        last_qrcode_result_timestamp_ = result.timestamp;
        if (pub_qrcode_result_->hasSubscribers())
        {
            auto loan = pub_qrcode_result_->loan();
            if (!loan.has_error())
            {
                auto &msg = loan.value();
                uint64_t timestamp_ms = result.timestamp / 1e6;
                msg->header.stamp.sec = timestamp_ms / 1000; // ms->s
                msg->header.stamp.nanosec = (timestamp_ms % 1000) * 1000 * 1000;
                msg->timestamp_ms = timestamp_ms;
                msg->mark_perception_status = result.mark_perception_status;
                msg->mark_perception_direction = result.mark_perception_direction;
                msg->status = static_cast<fescue_msgs__enum__QrCodeStatus>(result.detect_status);
                int dis_size = std::min(static_cast<int>(result.v_markID_dis.size()), IOX_MAX_QRCODE_NUM);
                for (int i = 0; i < dis_size; i++)
                {
                    fescue_msgs__msg__QrCodeDistance qrcode_dis;
                    qrcode_dis.id = result.v_markID_dis[i].first;
                    qrcode_dis.distance = result.v_markID_dis[i].second;
                    msg->qrcode_dis.push_back(qrcode_dis);
                }
                msg->roi_confidence = result.roi_confidence;
                msg->target_direction = result.target_direction;
                msg->mark_id = result.markID;
                msg->pose.position.x = result.pos_x;
                msg->pose.position.y = result.pos_y;
                msg->pose.position.z = result.pos_z;
                msg->pose.orientation.x = result.quaternion_x;
                msg->pose.orientation.y = result.quaternion_y;
                msg->pose.orientation.z = result.quaternion_z;
                msg->pose.orientation.w = result.quaternion_w;
                msg->roll = result.roll;
                msg->pitch = result.pitch;
                msg->yaw = result.yaw;
                msg.publish();
            }
        }
    }
}

void ChargeStationLocalizationAlg::PulishImage()
{
    QrCodeImage image;
    qrcode_img_mtx_.lock();
    image = qrcode_img_;
    qrcode_img_.img.release();
    qrcode_img_mtx_.unlock();

    if (!image.img.empty() && pub_qrcode_img_->hasSubscribers())
    {
        auto loan = pub_qrcode_img_->loan();
        if (!loan.has_error())
        {
            auto &msg = loan.value();
            msg->header.stamp.sec = image.sec;
            msg->header.stamp.nanosec = image.nanosec;
            msg->header.frame_id.unsafe_assign(image.frame_id.c_str());
            msg->width = image.width;
            msg->height = image.height;
            msg->is_bigendian = false;
            msg->encoding.unsafe_assign(image.encoding.c_str());
            msg->step = msg->width * image.img.channels();
            size_t img_size = (msg->step * msg->height > IOX_IMAGE_DATA_MAX)
                                  ? IOX_IMAGE_DATA_MAX
                                  : (msg->step * msg->height);
            msg->data.resize(img_size);
            memcpy(msg->data.data(), image.img.data, img_size);
            msg.publish();
        }
    }
}

void ChargeStationLocalizationAlg::SaveImage(const sensor_msgs__msg__Image_iox &image)
{
    char file_name[128] = {0};
    cv::Mat mat_img;
    NV12ToMat(mat_img, image.data.data(), image.width, image.height);
    sprintf(file_name, "%s/%lu_color_src_%dx%d_rgb.png", save_img_dir_.c_str(), timestamp_ms_,
            mat_img.cols, mat_img.rows);
    cv::imwrite(file_name, mat_img);
}

void ChargeStationLocalizationAlg::PublishResultThread()
{
    LOG_INFO("PublishQrCodeResultThread is running!");

    while (thread_running_.load())
    {
        PublishResult();
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    LOG_INFO("PublishQrCodeResultThread is stoped!");
}

void ChargeStationLocalizationAlg::ConfigParam2AlgParam(const QRCodeLocationAlgConfig &config, Recharge_config &param)
{
    param.verbosity = config.verbosity;
    param.showImg = config.showImg;
    param.only_use_perception = config.only_use_perception;
    param.percept_time_diff_thre = config.percept_time_diff_thre;
    param.writeImg = config.writeImg;
    param.Perceptual_window_ratio = config.Perceptual_window_ratio;
    param.plane_distance_threshold = config.plane_distance_threshold;
    param.pnp_method = config.pnp_method;
    param.aprilTagMinClusterPixels = config.aprilTagMinClusterPixels;
    param.minMarkerPerimeterRate = config.minMarkerPerimeterRate;
    param.maxMarkerPerimeterRate = config.maxMarkerPerimeterRate;
    param.outputRollAng = config.outputRollAng;
    param.cornerRefinementMethod = config.cornerRefinementMethod;
    param.use_bilateral_filter = config.use_bilateral_filter;

    param.markModel = config.markModel;
    param.bucketID = config.bucketID;
    param.evaluation = config.evaluation;
    param.outputFrameOffset_x = config.outputFrameOffset_x;
    param.outputFrameOffset_y = config.outputFrameOffset_y;
    param.outputFrameOffset_z = config.outputFrameOffset_z;
    param.outputFrame_theta = config.outputFrame_theta;
    param.outputDisThre = config.outputDisThre;

    param.use_nearby_speed_up = config.use_nearby_speed_up;
    param.minCornerDistanceRate = config.minCornerDistanceRate;
    param.edgeThresholdRate = config.edgeThresholdRate;
    param.detection_area_size = config.detection_area_size;
    param.nearbyMinMarkerPerimeterRate = config.nearbyMinMarkerPerimeterRate;
}

void ChargeStationLocalizationAlg::AlgParam2ConfigParam(const Recharge_config &param, QRCodeLocationAlgConfig &config)
{
    config.verbosity = param.verbosity;
    config.showImg = param.showImg;
    config.only_use_perception = param.only_use_perception;
    config.percept_time_diff_thre = param.percept_time_diff_thre;
    config.writeImg = param.writeImg;
    config.Perceptual_window_ratio = param.Perceptual_window_ratio;
    config.plane_distance_threshold = param.plane_distance_threshold;
    config.pnp_method = param.pnp_method;
    config.aprilTagMinClusterPixels = param.aprilTagMinClusterPixels;
    config.minMarkerPerimeterRate = param.minMarkerPerimeterRate;
    config.maxMarkerPerimeterRate = param.maxMarkerPerimeterRate;
    config.outputRollAng = param.outputRollAng;
    config.cornerRefinementMethod = param.cornerRefinementMethod;
    config.use_bilateral_filter = param.use_bilateral_filter;

    config.markModel = param.markModel;
    config.bucketID = param.bucketID;
    config.evaluation = param.evaluation;
    config.outputFrameOffset_x = param.outputFrameOffset_x;
    config.outputFrameOffset_y = param.outputFrameOffset_y;
    config.outputFrameOffset_z = param.outputFrameOffset_z;
    config.outputFrame_theta = param.outputFrame_theta;
    config.outputDisThre = param.outputDisThre;

    config.use_nearby_speed_up = param.use_nearby_speed_up;
    config.minCornerDistanceRate = param.minCornerDistanceRate;
    config.edgeThresholdRate = param.edgeThresholdRate;
    config.detection_area_size = param.detection_area_size;
    config.nearbyMinMarkerPerimeterRate = param.nearbyMinMarkerPerimeterRate;
}

void ChargeStationLocalizationAlg::PublishException(mower_msgs::msg::SocExceptionLevel level,
                                                    mower_msgs::msg::SocExceptionValue value)
{
    if (pub_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.timestamp = GetTimestampMs();
        exception.node_name = "PerceptionRechargeNode";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_exception_->publishCopyOf(exception);
    }
}
} // namespace fescue_iox
