#pragma once

#include "cross_region_localization_config.hpp"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "markLocationAPI.h"
#include "mower_msgs/msg/soc_exception.hpp"
#include "ob_mower_msgs/mark_location_result_struct.h"
#include "opencv2/opencv.hpp"
#include "sensor_msgs/image__struct.h"
#include "utils/iceoryx_publisher_mower.hpp"

#include <condition_variable>
#include <mutex>
#include <string>
#include <vector>

namespace fescue_iox
{

struct MarkLocationImage
{
    uint64_t sec;
    uint64_t nanosec;
    uint32_t width;
    uint32_t height;
    std::string frame_id;
    std::string encoding;
    cv::Mat img;
    MarkLocationImage() = default;
    ~MarkLocationImage() = default;
    MarkLocationImage(const MarkLocationImage &other)
    {
        this->sec = other.sec;
        this->nanosec = other.nanosec;
        this->width = other.width;
        this->height = other.height;
        this->frame_id = other.frame_id;
        this->encoding = other.encoding;
        this->img = other.img.clone();
    }
    MarkLocationImage &operator=(const MarkLocationImage &other)
    {
        if (this != &other)
        {
            this->sec = other.sec;
            this->nanosec = other.nanosec;
            this->width = other.width;
            this->height = other.height;
            this->frame_id = other.frame_id;
            this->encoding = other.encoding;
            this->img = other.img.clone();
        }
        return *this;
    }
};

class MarkLocation
{
    using iox_mark_location_result_publisher = iox::popo::Publisher<fescue_msgs__msg__MarkLocationResult>;
    using iox_image_publisher = iox::popo::Publisher<sensor_msgs__msg__Image_iox>;

public:
    MarkLocation(const std::string &conf_file, Cross_region_camera_intrinsic &intrinsic_param, bool result);
    ~MarkLocation();
    void ShowCameraIntrinsicParam(const Cross_region_camera_intrinsic &intrinsic_param);
    bool DoMarkLocation(const sensor_msgs__msg__Image_iox &image);
    void AddPerceptionResult(const PerceptionInfo &info);
    bool SetDetectMarkId(uint32_t mark_id);
    bool SetMarkLocationAlgoParam(Cross_region_config &param);
    bool GetMarkLocationAlgoParam(Cross_region_config &param);
    const char *GetMarkLocationAlgoVersion();

private:
    bool InitAlg(Cross_region_camera_intrinsic &intrinsic_param, bool result);
    void InitAlgParam();
    void InitPublisher();
    void PreMarkLocation(const sensor_msgs__msg__Image_iox &image);
    void PublishMarkLocationResult();
    void PublishMarkLocationImage();
    void PublishMarkLocationResultThread();
    void PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value);
    void ConfigParam2AlgParam(const MarkLocationAlgConfig &config, Cross_region_config &param);
    void AlgParam2ConfigParam(const Cross_region_config &param, MarkLocationAlgConfig &config);

private:
    static std::mutex result_mtx_;
    static std::condition_variable result_cv_;
    static ob_mark_location_result mark_loc_result_;

    static std::mutex img_mtx_;
    static MarkLocationImage mark_loc_img_;
    // 算法回调
    static void MarkLocationResultCallback(const ob_mark_location_result *result);
    static void MarkLocationImgCallback(const uint64_t timestamp, const uint32_t width, const uint32_t height,
                                        uint8_t *data, const uint64_t size);

private:
    MARK_LOCATION_HANDLE mark_location_handle_{nullptr};

    std::unique_ptr<iox_image_publisher> pub_mark_loc_img_{nullptr};
    std::unique_ptr<iox_mark_location_result_publisher> pub_mark_loc_result_{nullptr};
    std::unique_ptr<IceoryxPublisherMower<mower_msgs::msg::SocException>> pub_exception_{nullptr};

    std::atomic_bool thread_running_{true};
    std::thread pub_result_thread_;

    std::string conf_file_{""};
    std::string encoding_{""};
    std::string frame_id_{""};
    uint64_t sec_;
    uint64_t nanosec_;
    uint64_t timestamp_ms_;

    cv::Mat bgr_img_;
    uint32_t img_width_;
    uint32_t img_height_;
    uint8_t *img_data_;
    uint64_t img_size_;
};

} // namespace fescue_iox
