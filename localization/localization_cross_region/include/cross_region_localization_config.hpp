#pragma once

#include "utils/config.hpp"

#include <string>

namespace fescue_iox
{

struct MarkLocationAlgConfig
{
    // need to adjust dynamically
    int verbosity{4};                   // log level, 4: just error
    bool showImg{false};                // false
    int only_use_perception{1};         // 1, use the perception result
    float percept_time_diff_thre{0.2};  // 0.2
    bool writeImg{false};               // false, if debug image, revise to true;
    float Perceptual_window_ratio{1.2}; // default: 1.2
    // nearby distance QR detection setting
    int pnp_method{0};                  // default 0, values: 0,1,2,3,4,5,6
    int aprilTagMinClusterPixels{5};    // default: 5
    float minMarkerPerimeterRate{0.03}; // default: 0.03
    float maxMarkerPerimeterRate{4.0};  // default: 4.0
    float outputRollAng{0.7};           // default: 0.7
    int cornerRefinementMethod{0};      // default 0, range: 0~3
    bool use_bilateral_filter{false};   // default: false

    // nearby QR parameter
    bool use_nearby_speed_up{0};             // default: 0                                         // QR parameter
    float minCornerDistanceRate{0.05};       // default: 0.05
    float edgeThresholdRate{0.1};            // default 0.1
    int detection_area_size{40000};          // default 40000
    float nearbyMinMarkerPerimeterRate{0.3}; // default 0.3

    /* now, don't need to adjust dynamically */
    int markModel{6};             // arUco bucket: 6
    int bucketID{1};              // default: 1
    bool evaluation{false};       // default: false
    float outputFrameOffset_x{0}; // default: 0
    float outputFrameOffset_y{0}; // default: 0
    float outputFrameOffset_z{0}; // default: 0
    float outputFrame_theta{0};   // default: 0
    float outputDisThre{10.0};    // default: 10.0

    std::string inner_config_path{"conf/localization_cross_region_node/cross_config_arUco_bucket_common_KB_Chelsea.yaml"}; // 算法内部配置文件

    MarkLocationAlgConfig() = default;
    ~MarkLocationAlgConfig() = default;
    MarkLocationAlgConfig(const MarkLocationAlgConfig &config) = default;
    MarkLocationAlgConfig &operator=(const MarkLocationAlgConfig &config);
    std::string toString() const;
};

bool operator==(const MarkLocationAlgConfig &lhs, const MarkLocationAlgConfig &rhs);
bool operator!=(const MarkLocationAlgConfig &lhs, const MarkLocationAlgConfig &rhs);

} // namespace fescue_iox
