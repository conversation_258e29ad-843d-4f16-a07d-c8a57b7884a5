#pragma once

#include "area_estimation.hpp"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/client.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/server.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "localization_area_estimation_node_config.hpp"
#include "mower_msgs/msg/mcu_motor_speed.hpp"
#include "mower_msgs/srv/area_calculation.hpp"
#include "ob_mower_msgs/perception_localization_alg_ctrl.h"
#include "ob_mower_srvs/algorithm_version_service__struct.h"
#include "ob_mower_srvs/localization_area_estimate_alg_param.h"
#include "ob_mower_srvs/node_common_param_service.h"
#include "utils/heartbeat_publisher.hpp"
#include "utils/iceoryx_server_mower.hpp"
#include "utils/iceoryx_subscriber_mower.hpp"

#include <cmath>
#include <memory>
#include <sys/prctl.h>

namespace fescue_iox
{

class LocalizationAreaEstimationNode
{
    using set_node_param_request = ob_mower_srvs::SetNodeParamRequest;
    using set_node_param_response = ob_mower_srvs::SetNodeParamResponse;
    using get_node_param_request = ob_mower_srvs::GetNodeParamRequest;
    using get_node_param_response = ob_mower_srvs::GetNodeParamResponse;

    using get_alg_param_request = ob_mower_srvs::GetLocAreaEstimateAlgParamRequest;
    using get_alg_param_response = ob_mower_srvs::GetLocAreaEstimateAlgParamResponse;
    using set_alg_param_request = ob_mower_srvs::SetLocAreaEstimateAlgParamRequest;
    using set_alg_param_response = ob_mower_srvs::SetLocAreaEstimateAlgParamResponse;

    using area_calc_start_request = mower_msgs::srv::AreaCalculationStartRequest;
    using area_calc_start_response = mower_msgs::srv::AreaCalculationStartResponse;
    using area_calc_stop_request = mower_msgs::srv::AreaCalculationStopRequest;
    using area_calc_stop_response = mower_msgs::srv::AreaCalculationStopResponse;

    using get_alg_version_request = ob_mower_srvs::GetAlgoVersionRequest;
    using get_alg_version_response = ob_mower_srvs::GetAlgoVersionResponse;

public:
    LocalizationAreaEstimationNode(const std::string &node_name);
    ~LocalizationAreaEstimationNode();

private:
    void InitWorkingDirectory();
    void InitParam();
    void InitHeartbeat();
    void InitLogger();
    void InitAlgorithms();
    void InitSubscriber();
    void InitServer();

private:
    void DealMotorSpeedData(const mower_msgs::msg::McuMotorSpeed &data);
    void DealImuData(const mower_msgs::msg::McuImu &data);
    void DealImuData(const mower_msgs::msg::SocImu &data);
    void DealAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data);
    bool DealGetNodeParam(ob_mower_srvs::NodeParamData &data);
    bool DealSetNodeParam(const ob_mower_srvs::NodeParamData &data);
    bool DealSetAlgParam(const ob_mower_srvs::LocAreaEstimateAlgParam &data);
    bool DealGetAlgParam(ob_mower_srvs::LocAreaEstimateAlgParam &data);
    bool StartAreaCalculation();
    bool StopAreaCalculation(mower_msgs::srv::WheelOdomAreaResult &odom_result);
    bool DealGetAlgVersion(ob_mower_srvs::AlgoVersionDataVect &data);

private:
    // subscriber
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuMotorSpeed>> sub_mcu_motor_speed_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuImu>> sub_mcu_imu_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::SocImu>> sub_soc_imu_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<ob_mower_msgs::PerceptionLocalizationAlgCtrl>> sub_algo_ctrl_{nullptr};

    // server
    std::unique_ptr<IceoryxServerMower<get_node_param_request, get_node_param_response>> service_get_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_node_param_request, set_node_param_response>> service_set_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<get_alg_param_request, get_alg_param_response>> service_get_alg_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_alg_param_request, set_alg_param_response>> service_set_alg_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<area_calc_start_request, area_calc_start_response>> service_area_calculation_start_{nullptr};
    std::unique_ptr<IceoryxServerMower<area_calc_stop_request, area_calc_stop_response>> service_area_calculation_stop_{nullptr};
    std::unique_ptr<IceoryxServerMower<get_alg_version_request, get_alg_version_response>> service_get_algo_version_{nullptr};

    std::string node_name_{"localization_area_estimation_node"};
    std::string log_dir_{"/userdata/log"};
    std::string console_log_level_{"info"};
    std::string file_log_level_{""};
    std::string area_estimation_conf_file_{"conf/localization_area_estimation_node/localization_area_estimation.yaml"};

    bool area_estimation_alg_enable_{true};
    std::unique_ptr<LocalizationAreaEstimationAlg> area_estimation_alg_{nullptr};
    std::unique_ptr<NodeHeartbeatPublisher> pub_heartbeat_{nullptr};
};

} // namespace fescue_iox