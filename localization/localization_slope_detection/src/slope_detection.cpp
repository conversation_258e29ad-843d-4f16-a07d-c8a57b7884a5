#include "slope_detection.hpp"

#include "utils/logger.hpp"
#include "utils/utils.hpp"

namespace fescue_iox
{

void LocalizationSlopeDetectionAlg::SlopeStatusCallback(const ob_lawnmower_slope_status *result)
{
    LOG_DEBUG("ob_lawnmower_slope_status, timestamp: {} roll: {} pitch: {} yaw: {} slope_status: {})",
              result->timestamp / 1000, result->roll, result->pitch, result->yaw, result->slope_status);
    LOG_DEBUG("Quaternion (w ,x ,y,z): {} {} {} {}",
              result->quaternion_w, result->quaternion_x, result->quaternion_y, result->quaternion_z);
    if (pub_slope_detection_result_)
    {
        if (pub_slope_detection_result_->hasSubscribers())
        {
            auto loan = pub_slope_detection_result_->loan();
            if (!loan.has_error())
            {
                auto &msg = loan.value();
                uint64_t timestamp_ms = result->timestamp / 1e6;
                msg->timestamp_ms = timestamp_ms;
                msg->roll = result->roll;
                msg->pitch = result->pitch;
                msg->yaw = result->yaw;
                msg->slope_status = static_cast<mower_msgs::msg::SlopeStatus>(result->slope_status);
                msg->quaternion_w = result->quaternion_w;
                msg->quaternion_x = result->quaternion_x;
                msg->quaternion_y = result->quaternion_y;
                msg->quaternion_z = result->quaternion_z;
                msg.publish();
            }
        }
    }
}

LocalizationSlopeDetectionAlg::LocalizationSlopeDetectionAlg(const std::string &conf_file)
    : conf_file_(conf_file)
{
    InitAlgo();
    InitPublisher();
}

LocalizationSlopeDetectionAlg::~LocalizationSlopeDetectionAlg()
{
    DeinitAlgo();

    LOG_WARN("LocalizationSlopeDetectionAlg exit success!");
}

void LocalizationSlopeDetectionAlg::DoSlopeDetection(const mower_msgs::msg::McuImu &data)
{
    uint64_t timestamp = data.system_timestamp * 1000000;
    VPE_add_imu(slope_detection_handle_, timestamp,
                data.angular_velocity_x, data.angular_velocity_y, data.angular_velocity_z,
                data.linear_acceleration_x, data.linear_acceleration_y, data.linear_acceleration_z);
}

void LocalizationSlopeDetectionAlg::DoSlopeDetection(const mower_msgs::msg::SocImu &data)
{
    uint64_t timestamp = data.system_timestamp * 1000000;
    VPE_add_imu(slope_detection_handle_, timestamp,
                data.angular_velocity_x, data.angular_velocity_y, data.angular_velocity_z,
                data.linear_acceleration_x, data.linear_acceleration_y, data.linear_acceleration_z);
}

const char *LocalizationSlopeDetectionAlg::GetAlgVersion()
{
    return VPE_get_version(slope_detection_handle_);
}

void LocalizationSlopeDetectionAlg::InitAlgo()
{
    InitAlgoParam();
    VPE_create(&slope_detection_handle_, conf_file_.c_str());
    VPE_start(slope_detection_handle_);
    VPE_register_slope_status_callback(slope_detection_handle_, SlopeStatusCallback);
    LOG_INFO("localization slope detection alg version: {}", VPE_get_version(slope_detection_handle_));
}

void LocalizationSlopeDetectionAlg::DeinitAlgo()
{
    VPE_stop(slope_detection_handle_);
    VPE_release(slope_detection_handle_);
}

void LocalizationSlopeDetectionAlg::InitAlgoParam()
{
}

void LocalizationSlopeDetectionAlg::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_slope_detection_result_ = std::make_unique<iox_slope_detection_result_publisher>(
        iox::capro::ServiceDescription{KLocalizationSlopeDetectionResultIox[0],
                                       KLocalizationSlopeDetectionResultIox[1],
                                       KLocalizationSlopeDetectionResultIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
}

} // namespace fescue_iox