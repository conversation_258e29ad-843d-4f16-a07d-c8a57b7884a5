#pragma once

#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/client.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/server.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "localization_slope_detection_node_config.hpp"
#include "mower_msgs/msg/mcu_imu.hpp"
#include "mower_msgs/msg/soc_imu.hpp"
#include "ob_mower_msgs/perception_localization_alg_ctrl.h"
#include "ob_mower_srvs/algorithm_version_service__struct.h"
#include "ob_mower_srvs/node_common_param_service.h"
#include "opencv2/opencv.hpp"
#include "slope_detection.hpp"
#include "utils/heartbeat_publisher.hpp"
#include "utils/iceoryx_server_mower.hpp"
#include "utils/iceoryx_subscriber_mower.hpp"

#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{

class LocalizationSlopeDetectionNode
{
    using set_node_param_request = ob_mower_srvs::SetNodeParamRequest;
    using set_node_param_response = ob_mower_srvs::SetNodeParamResponse;
    using get_node_param_request = ob_mower_srvs::GetNodeParamRequest;
    using get_node_param_response = ob_mower_srvs::GetNodeParamResponse;

    using get_alg_version_request = ob_mower_srvs::GetAlgoVersionRequest;
    using get_alg_version_response = ob_mower_srvs::GetAlgoVersionResponse;

public:
    LocalizationSlopeDetectionNode(const std::string &node_name);
    ~LocalizationSlopeDetectionNode();

private:
    void InitWorkingDirectory();
    void InitHeartbeat();
    void InitParam();
    void InitLogger();
    void InitAlgorithms();
    void InitSubscriber();
    void InitService();

private:
    void DealImuData(const mower_msgs::msg::McuImu &data);
    void DealImuData(const mower_msgs::msg::SocImu &data);
    bool DealGetAlgVersion(ob_mower_srvs::AlgoVersionDataVect &data);
    void DealAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data);
    bool DealGetNodeParam(ob_mower_srvs::NodeParamData &data);
    bool DealSetNodeParam(const ob_mower_srvs::NodeParamData &data);

private:
    // subscriber
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuImu>> sub_mcu_imu_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::SocImu>> sub_soc_imu_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<ob_mower_msgs::PerceptionLocalizationAlgCtrl>> sub_algo_ctrl_{nullptr};

    // server
    std::unique_ptr<IceoryxServerMower<get_node_param_request, get_node_param_response>> service_get_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_node_param_request, set_node_param_response>> service_set_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<get_alg_version_request, get_alg_version_response>> service_get_alg_version_{nullptr};

    std::string node_name_{"localization_slope_detection_node"};
    std::string log_dir_{"/userdata/log"};
    std::string console_log_level_{"info"};
    std::string file_log_level_{""};
    std::string slope_detection_conf_file_{"conf/localization_slope_detection_node/slop_config.yaml"};

    // algorithms
    bool slope_detection_alg_enable_{true}; // 陡坡检测算法是否启用;
    std::unique_ptr<LocalizationSlopeDetectionAlg> slope_detection_alg_{nullptr};
    std::unique_ptr<NodeHeartbeatPublisher> pub_heartbeat_{nullptr};
};

} // namespace fescue_iox